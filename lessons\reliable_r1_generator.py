"""
Reliable R1 Flashcard Generator
Ultra-simple approach that works with DeepSeek R1's reasoning style
"""

import logging
from typing import Optional
from openai import OpenAI

from .models import ContentItem
from ai_services.llm_config import get_recommended_config

logger = logging.getLogger(__name__)


class ReliableR1Generator:
    """
    Ultra-reliable flashcard generator for DeepSeek R1
    """
    
    def __init__(self):
        self.config = get_recommended_config()
        self.client = self._setup_client()
    
    def _setup_client(self):
        """Setup OpenAI-compatible client"""
        try:
            client = OpenAI(api_key=self.config.api_key, base_url=self.config.base_url)
            logger.info(f"Reliable R1 Generator initialized")
            return client
        except Exception as e:
            logger.error(f"Failed to initialize R1 client: {e}")
            return None
    
    def create_flashcard(self, language: str, topic: str, card_type: str = "vocabulary") -> Optional[ContentItem]:
        """Create a single flashcard with ultra-simple prompting"""
        
        if card_type == "vocabulary":
            prompt = f"Create a {language} vocabulary question about {topic}. Give me just the question, answer, and explanation."
        else:
            prompt = f"Create a {language} grammar question about {topic}. Give me just the question, answer, and explanation."
        
        try:
            response = self.client.chat.completions.create(
                model=self.config.model_config.name,
                messages=[
                    {
                        "role": "system",
                        "content": f"You are a {language} teacher. Create educational content."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.7
            )
            
            content = response.choices[0].message.content.strip()
            
            # Extract the actual response after reasoning
            lines = content.split('\n')
            useful_lines = []
            
            # Look for lines that seem like actual content (not reasoning)
            for line in lines:
                line = line.strip()
                if line and not line.startswith(('Okay', 'Alright', 'Let me', 'I need', 'So I', 'First')):
                    useful_lines.append(line)
            
            if len(useful_lines) >= 2:
                # Use the content we found
                question = useful_lines[0]
                answer = useful_lines[1] if len(useful_lines) > 1 else "Answer"
                explanation = useful_lines[2] if len(useful_lines) > 2 else "Explanation provided."
                
                # Clean up formatting
                question = question.replace('Question:', '').replace('Q:', '').strip()
                answer = answer.replace('Answer:', '').replace('A:', '').strip()
                explanation = explanation.replace('Explanation:', '').replace('E:', '').strip()
                
                # Create flashcard
                flashcard = ContentItem.objects.create(
                    type="flashcard",
                    question_text=question,
                    answer_text=answer,
                    explanation_text=explanation,
                    hint_text=f"Think about {topic} in {language}",
                    language=language,
                    difficulty=2,  # Default to A2 level
                    cefr_level="A2",
                    tags=[card_type, "r1_generated", "reliable"]
                )
                
                logger.info(f"Created {card_type} flashcard: {question[:30]}...")
                return flashcard
            
        except Exception as e:
            logger.error(f"Error creating flashcard: {e}")
        
        return None
    
    def generate_basic_set(self, language: str, count: int = 20) -> dict:
        """Generate a basic set of flashcards"""
        
        # Simple topics that work well
        vocab_topics = ["family", "food", "colors", "animals", "home", "work", "travel", "time"]
        grammar_topics = ["present_tense", "past_tense", "articles", "plurals", "questions"]
        
        generated = []
        
        for i in range(count):
            try:
                if i % 2 == 0:  # Vocabulary
                    topic = vocab_topics[i % len(vocab_topics)]
                    card = self.create_flashcard(language, topic, "vocabulary")
                else:  # Grammar
                    topic = grammar_topics[i % len(grammar_topics)]
                    card = self.create_flashcard(language, topic, "grammar")
                
                if card:
                    generated.append(card)
                
            except Exception as e:
                logger.error(f"Error generating card {i+1}: {e}")
        
        return {
            "success": True,
            "generated_count": len(generated),
            "requested_count": count,
            "success_rate": (len(generated) / count * 100) if count > 0 else 0,
            "flashcards": generated,
            "language": language
        }


def quick_generate_content():
    """Quick function to generate content for testing"""
    generator = ReliableR1Generator()
    
    if not generator.client:
        print("ERROR: R1 not available")
        return
    
    languages = ["spanish", "french", "german"]
    total_generated = 0
    
    for language in languages:
        print(f"Generating 15 flashcards for {language}...")
        result = generator.generate_basic_set(language, 15)
        
        if result["success"]:
            generated = result["generated_count"]
            total_generated += generated
            print(f"  SUCCESS: {generated}/15 ({result['success_rate']:.1f}%)")
        else:
            print(f"  FAILED: {language}")
    
    print(f"\nTotal generated: {total_generated}")
    print(f"Database total: {ContentItem.objects.count()}")
    
    return total_generated


if __name__ == "__main__":
    quick_generate_content()
