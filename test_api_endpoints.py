#!/usr/bin/env python
"""
Test script to verify API endpoints are working
"""
import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()


def test_api_endpoints():
    """Test main API endpoints"""
    base_url = "http://127.0.0.1:8000"

    print("🧪 Testing API Endpoints")
    print("=" * 50)

    # Test 1: Flashcard generation
    print("\n📝 Test 1: Flashcard Generation")
    try:
        response = requests.get(
            f"{base_url}/api/flashcard/",
            params={
                "difficulty": "beginner",
                "type": "multiple_choice",
                "language": "spanish",
            },
            timeout=10,
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Question: {data.get('question', 'N/A')[:50]}...")
            print("   ✅ Flashcard generation working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 2: Content preloading
    print("\n🔄 Test 2: Content Preloading")
    try:
        response = requests.post(
            f"{base_url}/api/preload/",
            json={"user_id": 1},
            headers={"Content-Type": "application/json"},
            timeout=10,
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Message: {data.get('message', 'N/A')}")
            print("   ✅ Preloading endpoint working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 3: Answer submission
    print("\n✏️ Test 3: Answer Submission")
    try:
        response = requests.post(
            f"{base_url}/api/answer/",
            json={
                "flashcard_id": "test123",
                "user_answer": "Hola",
                "correct_answer": "Hola",
                "question": "How do you say 'hello' in Spanish?",
                "language": "spanish",
            },
            headers={"Content-Type": "application/json"},
            timeout=10,
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Correct: {data.get('is_correct', 'N/A')}")
            print(f"   Feedback: {data.get('feedback', 'N/A')[:50]}...")
            print("   ✅ Answer submission working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    print("\n🎉 API Endpoint Test Complete!")


if __name__ == "__main__":
    test_api_endpoints()
