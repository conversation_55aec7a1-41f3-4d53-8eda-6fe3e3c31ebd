"""
URL patterns for Language Management API
"""

from django.urls import path
from . import language_api

app_name = "language_api"

urlpatterns = [
    # Language information endpoints
    path(
        "supported/", language_api.get_supported_languages, name="supported_languages"
    ),
    path(
        "statistics/", language_api.get_language_statistics, name="language_statistics"
    ),
    path(
        "validate/<str:language_code>/",
        language_api.validate_language_support,
        name="validate_language",
    ),
    # User language management
    path("user/current/", language_api.get_user_language, name="get_user_language"),
    path("user/set/", language_api.set_user_language, name="set_user_language"),
    # Quality reporting (admin only - accessed via Django admin)
    path(
        "quality/report/",
        language_api.get_language_quality_report,
        name="quality_report",
    ),
]
