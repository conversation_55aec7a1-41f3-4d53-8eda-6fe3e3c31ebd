#!/usr/bin/env python3
"""
Test script to verify C.A.R.E. system access and functionality
"""
import requests
import sys

BASE_URL = "http://127.0.0.1:8000"


def test_care_access():
    """Test C.A.R.E. lesson access"""
    print("=== Testing C.A.R.E. System Access ===")

    # Create a session to maintain cookies
    session = requests.Session()

    # First try to access the main page
    try:
        response = session.get(f"{BASE_URL}/")
        print(f"Main page status: {response.status_code}")

        # Try to access dashboard (may require login)
        response = session.get(f"{BASE_URL}/dashboard/")
        print(f"Dashboard status: {response.status_code}")

        # Check if redirected to login
        if "login" in response.url:
            print("❌ Login required - user not authenticated")
            return False

        # Try to access C.A.R.E. lesson
        response = session.get(f"{BASE_URL}/care/lesson/")
        print(f"C.A.R.E. lesson status: {response.status_code}")

        if response.status_code == 200:
            print("✅ C.A.R.E. lesson page accessible")

            # Check if the page contains expected elements
            if "C.A.R.E." in response.text:
                print("✅ C.A.R.E. content found")
            else:
                print("❌ C.A.R.E. content missing")

            if "Contextualize" in response.text:
                print("✅ Contextualize phase found")
            else:
                print("❌ Contextualize phase missing")

            return True
        else:
            print(f"❌ C.A.R.E. lesson page returned status {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error testing C.A.R.E. access: {e}")
        return False


def test_care_api_endpoints():
    """Test C.A.R.E. API endpoints"""
    print("\n=== Testing C.A.R.E. API Endpoints ===")

    session = requests.Session()

    # Test phase data endpoint
    try:
        response = session.get(f"{BASE_URL}/care/api/phase/contextualize/")
        print(f"Contextualize API status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Contextualize API working - returned {len(data)} items")
        else:
            print(f"❌ Contextualize API failed: {response.status_code}")

    except Exception as e:
        print(f"❌ Error testing C.A.R.E. API: {e}")


if __name__ == "__main__":
    print("Testing C.A.R.E. System after HTML fix...\n")

    care_works = test_care_access()
    test_care_api_endpoints()

    if care_works:
        print("\n✅ C.A.R.E. system appears to be working!")
    else:
        print("\n❌ C.A.R.E. system needs debugging")
