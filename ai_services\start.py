#!/usr/bin/env python3
"""
TalonTalk AI Services Startup Script
Starts the FastAPI microservice for LLM-powered language learning features
"""

import os
import sys
import subprocess
from pathlib import Path


def check_requirements():
    """Check if required packages are installed"""
    try:
        import fastapi
        import uvicorn
        import openai
        import requests
        import dotenv

        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("📦 Installing requirements...")
        return False


def install_requirements():
    """Install required packages"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    if requirements_file.exists():
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            print("✅ Requirements installed successfully")
            return True
        else:
            print(f"❌ Failed to install requirements: {result.stderr}")
            return False
    else:
        print(f"❌ Requirements file not found: {requirements_file}")
        return False


def check_environment():
    """Check environment configuration"""
    env_file = Path(__file__).parent / ".env"
    example_file = Path(__file__).parent / ".env.example"

    if not env_file.exists():
        if example_file.exists():
            print("📋 Creating .env file from template...")
            with open(example_file, "r") as src, open(env_file, "w") as dst:
                dst.write(src.read())
            print(f"📝 Please edit {env_file} and add your API keys")
        else:
            print("⚠️  No .env file found. Create one with your API keys.")

    # Load environment variables
    from dotenv import load_dotenv

    load_dotenv(env_file)

    # Check for at least one API key
    api_keys = [
        ("OPENROUTER_API_KEY", "OpenRouter"),
        ("DEEPSEEK_API_KEY", "DeepSeek"),
        ("OPENAI_API_KEY", "OpenAI"),
        ("ANTHROPIC_API_KEY", "Anthropic"),
    ]

    available_providers = []
    for key, name in api_keys:
        if os.getenv(key):
            available_providers.append(name)

    if available_providers:
        print(f"🔑 Available LLM providers: {', '.join(available_providers)}")
        return True
    else:
        print("⚠️  No API keys found. Service will run in demo mode.")
        print("   Add at least one API key to .env for full functionality:")
        for key, name in api_keys:
            print(f"   - {key}=your_key_here  # {name}")
        return False


def start_service():
    """Start the FastAPI service"""
    host = os.getenv("AI_SERVICE_HOST", "127.0.0.1")
    port = int(os.getenv("AI_SERVICE_PORT", "8001"))
    reload = os.getenv("AI_SERVICE_RELOAD", "true").lower() == "true"

    print(f"🚀 Starting TalonTalk AI Services on http://{host}:{port}")
    print("📚 Available endpoints:")
    print(f"   - Health check: http://{host}:{port}/health")
    print(f"   - Generate flashcard: http://{host}:{port}/generate_flashcard")
    print(f"   - Grade answer: http://{host}:{port}/grade_answer")
    print(f"   - API docs: http://{host}:{port}/docs")

    try:
        import uvicorn

        uvicorn.run(
            "ai_services.api:app", host=host, port=port, reload=reload, log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Service stopped by user")
    except Exception as e:
        print(f"❌ Failed to start service: {e}")


def main():
    """Main startup function"""
    print("🤖 TalonTalk AI Services")
    print("=" * 40)

    # Check and install requirements
    if not check_requirements():
        if not install_requirements():
            sys.exit(1)

    # Check environment configuration
    has_api_keys = check_environment()

    if not has_api_keys:
        response = input("\nContinue in demo mode? (y/n): ")
        if response.lower() != "y":
            print("👋 Setup your API keys and run again")
            sys.exit(0)

    # Start the service
    start_service()


if __name__ == "__main__":
    main()
