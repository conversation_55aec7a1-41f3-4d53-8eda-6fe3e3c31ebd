"""
Content Quality Gateway for TalonTalk
Implements the two-part system: Advanced Prompt Engineering + Automated Validation
"""

import json
import logging
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass

from .quality_control import FlashcardQualityValidator, QualityIssue
from .structured_prompting import StructuredPromptBuilder, StructuredPromptExecutor

logger = logging.getLogger(__name__)


@dataclass
class ContentGenerationRequest:
    """Structured request for content generation"""

    user_id: int
    content_type: str  # "flashcard", "lesson", "vocabulary"
    target_language: str
    difficulty_level: str
    topic: Optional[str] = None
    context: Optional[str] = None
    quantity: int = 1
    session_type: str = "focused_practice"


@dataclass
class ContentGenerationResult:
    """Result of content generation with quality metrics"""

    success: bool
    content: List[Dict[str, Any]]
    quality_score: float
    issues_found: List[str]
    issues_fixed: List[str]
    generation_time: float
    fallback_used: bool = False


class ContentQualityGateway:
    """
    The Content Quality Gateway - ensures all AI-generated content meets quality standards
    before being saved to the database.
    """

    def __init__(self, llm_service):
        self.llm_service = llm_service
        self.validator = FlashcardQualityValidator()
        self.prompt_builder = StructuredPromptBuilder()
        self.prompt_executor = StructuredPromptExecutor(llm_service)

        # Quality thresholds
        self.min_quality_score = 0.8
        self.max_retry_attempts = 3

    def generate_quality_content(
        self, request: ContentGenerationRequest
    ) -> ContentGenerationResult:
        """
        Generate high-quality content using the two-part quality system:
        1. Advanced Prompt Engineering
        2. Automated Validation & Fixing
        """
        import time

        start_time = time.time()

        try:
            # Step 1: Generate content using structured prompts
            raw_content = self._generate_with_structured_prompts(request)

            # Step 2: Validate and fix content
            validated_content, quality_metrics = self._validate_and_fix_content(
                raw_content, request
            )

            generation_time = time.time() - start_time

            return ContentGenerationResult(
                success=len(validated_content) > 0,
                content=validated_content,
                quality_score=quality_metrics["average_score"],
                issues_found=quality_metrics["issues_found"],
                issues_fixed=quality_metrics["issues_fixed"],
                generation_time=generation_time,
                fallback_used=quality_metrics.get("fallback_used", False),
            )

        except Exception as e:
            logger.error(f"Content generation failed: {e}")
            return self._generate_fallback_content(request, str(e))

    def _generate_with_structured_prompts(
        self, request: ContentGenerationRequest
    ) -> List[Dict[str, Any]]:
        """Generate content using advanced structured prompts"""

        # Create context for structured prompting
        from django.contrib.auth import get_user_model

        User = get_user_model()

        try:
            user = User.objects.get(id=request.user_id)
        except User.DoesNotExist:
            # Create anonymous user context
            user = type(
                "AnonymousUser",
                (),
                {
                    "id": request.user_id,
                    "profile": type(
                        "Profile",
                        (),
                        {
                            "skill_level": request.difficulty_level,
                            "target_language": request.target_language,
                        },
                    )(),
                },
            )()

        # Build context using structured prompt builder
        context = self.prompt_builder.build_context(
            session_type=request.session_type,
            user=user,
            lesson_id=None,  # For now, we'll handle lesson-specific content later
            session_length=request.quantity,
            topic=request.topic,
            difficulty_level=request.difficulty_level,
        )

        # Execute structured prompt
        result = self.prompt_executor.execute_focused_practice(context)

        if "error" in result:
            logger.warning(f"Structured prompt execution failed: {result['error']}")
            return []

        # Extract flashcards from result
        flashcards = result.get("flashcards", [])

        if not flashcards and "questions" in result:
            # Handle different response formats
            flashcards = result["questions"]

        if not flashcards and "content" in result:
            # Try content field
            flashcards = result["content"]

        return flashcards

    def _validate_and_fix_content(
        self, content: List[Dict[str, Any]], request: ContentGenerationRequest
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """Validate content and attempt to fix issues"""

        validated_content = []
        all_issues_found = []
        all_issues_fixed = []
        quality_scores = []

        for item in content:
            # Validate each item
            is_valid, issues, fixed_item = self.validator.validate_flashcard(item)

            if issues:
                all_issues_found.extend(issues)
                logger.info(f"Quality issues found: {issues}")

            # Calculate quality score
            quality_score = self._calculate_quality_score(fixed_item, issues)
            quality_scores.append(quality_score)

            # Accept item if it meets minimum quality threshold
            if quality_score >= self.min_quality_score:
                validated_content.append(fixed_item)
                if not is_valid:
                    all_issues_fixed.extend(issues)
            else:
                logger.warning(
                    f"Content item rejected due to low quality score: {quality_score}"
                )

        # Calculate overall metrics
        average_score = (
            sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        )

        quality_metrics = {
            "average_score": average_score,
            "issues_found": list(set(all_issues_found)),
            "issues_fixed": list(set(all_issues_fixed)),
            "items_processed": len(content),
            "items_accepted": len(validated_content),
        }

        return validated_content, quality_metrics

    def _calculate_quality_score(
        self, item: Dict[str, Any], issues: List[str]
    ) -> float:
        """Calculate quality score for a content item"""
        base_score = 1.0

        # Deduct points for each issue type
        issue_penalties = {
            QualityIssue.CIRCULAR_LOGIC.value: 0.4,
            QualityIssue.POOR_QUESTION_FORMAT.value: 0.3,
            QualityIssue.MISSING_CORRECT_ANSWER.value: 0.5,
            QualityIssue.CONFUSING_OPTIONS.value: 0.2,
            QualityIssue.CAPITALIZATION_MISMATCH.value: 0.1,
            QualityIssue.LANGUAGE_MIXING.value: 0.2,
        }

        for issue in issues:
            penalty = issue_penalties.get(issue, 0.1)
            base_score -= penalty

        # Bonus points for good structure
        if all(
            field in item
            for field in ["question", "correct_answer", "options", "explanation"]
        ):
            base_score += 0.1

        return max(0.0, min(1.0, base_score))

    def _generate_fallback_content(
        self, request: ContentGenerationRequest, error_msg: str
    ) -> ContentGenerationResult:
        """Generate fallback content when main generation fails"""

        # Simple fallback flashcard
        fallback_content = [
            {
                "question": f"How do you say 'hello' in {request.target_language}?",
                "correct_answer": (
                    "hola" if request.target_language.lower() == "spanish" else "hello"
                ),
                "options": (
                    ["hola", "adiós", "gracias", "por favor"]
                    if request.target_language.lower() == "spanish"
                    else ["hello", "goodbye", "thank you", "please"]
                ),
                "explanation": "This is a basic greeting.",
                "difficulty_level": request.difficulty_level,
                "tags": ["greetings", "basic"],
            }
        ]

        return ContentGenerationResult(
            success=True,
            content=fallback_content,
            quality_score=0.6,  # Lower score for fallback
            issues_found=[f"Generation failed: {error_msg}"],
            issues_fixed=[],
            generation_time=0.0,
            fallback_used=True,
        )


def validate_content_before_save(
    content_items: List[Dict[str, Any]], language: str = "spanish"
) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """
    Utility function to validate content before saving to database
    Can be used as a decorator or called directly
    """
    validator = FlashcardQualityValidator()
    validated_items = []
    validation_report = {
        "total_items": len(content_items),
        "accepted_items": 0,
        "rejected_items": 0,
        "issues_found": [],
        "issues_fixed": [],
    }

    for item in content_items:
        is_valid, issues, fixed_item = validator.validate_flashcard(item)

        if issues:
            validation_report["issues_found"].extend(issues)

        # Accept item if it has basic required fields
        if "question" in fixed_item and "correct_answer" in fixed_item:
            validated_items.append(fixed_item)
            validation_report["accepted_items"] += 1

            if not is_valid:
                validation_report["issues_fixed"].extend(issues)
        else:
            validation_report["rejected_items"] += 1

    return validated_items, validation_report
