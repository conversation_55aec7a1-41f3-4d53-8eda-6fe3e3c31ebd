"""
Language Management System for TalonTalk
Handles language selection, validation, and user preferences
"""

import logging
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache

from .models import UserLearningProfile, ContentItem

logger = logging.getLogger(__name__)


class LanguageManager:
    """
    Centralized language management system for TalonTalk
    """
    
    # Language configuration with metadata
    LANGUAGE_CONFIG = {
        "spanish": {
            "name": "Spanish",
            "native_name": "Español",
            "code": "es",
            "flag": "🇪🇸",
            "difficulty_for_english": "easy",
            "speech_code": "es-ES",
            "rtl": False,
            "active": True,
            "content_quality": "high",
            "ai_support": "excellent",
        },
        "french": {
            "name": "French",
            "native_name": "Français",
            "code": "fr",
            "flag": "🇫🇷",
            "difficulty_for_english": "medium",
            "speech_code": "fr-FR",
            "rtl": False,
            "active": True,
            "content_quality": "high",
            "ai_support": "excellent",
        },
        "german": {
            "name": "German",
            "native_name": "Deutsch",
            "code": "de",
            "flag": "🇩🇪",
            "difficulty_for_english": "hard",
            "speech_code": "de-DE",
            "rtl": False,
            "active": True,
            "content_quality": "high",
            "ai_support": "good",
        },
        "italian": {
            "name": "Italian",
            "native_name": "Italiano",
            "code": "it",
            "flag": "🇮🇹",
            "difficulty_for_english": "medium",
            "speech_code": "it-IT",
            "rtl": False,
            "active": True,
            "content_quality": "medium",
            "ai_support": "good",
        },
        "portuguese": {
            "name": "Portuguese",
            "native_name": "Português",
            "code": "pt",
            "flag": "🇵🇹",
            "difficulty_for_english": "medium",
            "speech_code": "pt-PT",
            "rtl": False,
            "active": True,
            "content_quality": "medium",
            "ai_support": "good",
        },
        "mandarin": {
            "name": "Mandarin Chinese",
            "native_name": "中文",
            "code": "zh",
            "flag": "🇨🇳",
            "difficulty_for_english": "very_hard",
            "speech_code": "zh-CN",
            "rtl": False,
            "active": False,  # Coming soon
            "content_quality": "low",
            "ai_support": "fair",
        },
        "japanese": {
            "name": "Japanese",
            "native_name": "日本語",
            "code": "ja",
            "flag": "🇯🇵",
            "difficulty_for_english": "very_hard",
            "speech_code": "ja-JP",
            "rtl": False,
            "active": False,  # Coming soon
            "content_quality": "low",
            "ai_support": "fair",
        },
        "korean": {
            "name": "Korean",
            "native_name": "한국어",
            "code": "ko",
            "flag": "🇰🇷",
            "difficulty_for_english": "very_hard",
            "speech_code": "ko-KR",
            "rtl": False,
            "active": False,  # Coming soon
            "content_quality": "low",
            "ai_support": "fair",
        },
        "arabic": {
            "name": "Arabic",
            "native_name": "العربية",
            "code": "ar",
            "flag": "🇸🇦",
            "difficulty_for_english": "very_hard",
            "speech_code": "ar-SA",
            "rtl": True,
            "active": False,  # Coming soon
            "content_quality": "low",
            "ai_support": "fair",
        },
        "russian": {
            "name": "Russian",
            "native_name": "Русский",
            "code": "ru",
            "flag": "🇷🇺",
            "difficulty_for_english": "hard",
            "speech_code": "ru-RU",
            "rtl": False,
            "active": False,  # Coming soon
            "content_quality": "low",
            "ai_support": "fair",
        },
        "hindi": {
            "name": "Hindi",
            "native_name": "हिन्दी",
            "code": "hi",
            "flag": "🇮🇳",
            "difficulty_for_english": "hard",
            "speech_code": "hi-IN",
            "rtl": False,
            "active": False,  # Coming soon
            "content_quality": "low",
            "ai_support": "fair",
        },
    }

    @classmethod
    def get_supported_languages(cls, active_only: bool = True) -> Dict:
        """Get list of supported languages with metadata"""
        if active_only:
            return {k: v for k, v in cls.LANGUAGE_CONFIG.items() if v["active"]}
        return cls.LANGUAGE_CONFIG

    @classmethod
    def get_language_info(cls, language_code: str) -> Optional[Dict]:
        """Get detailed information about a specific language"""
        return cls.LANGUAGE_CONFIG.get(language_code)

    @classmethod
    def is_language_supported(cls, language_code: str) -> bool:
        """Check if a language is supported"""
        return language_code in cls.LANGUAGE_CONFIG

    @classmethod
    def is_language_active(cls, language_code: str) -> bool:
        """Check if a language is actively supported (not coming soon)"""
        lang_info = cls.get_language_info(language_code)
        return lang_info and lang_info.get("active", False)

    @classmethod
    def get_user_language(cls, user: User) -> str:
        """Get user's preferred language"""
        try:
            profile = UserLearningProfile.objects.get(user=user)
            return profile.target_language
        except UserLearningProfile.DoesNotExist:
            return "spanish"  # Default fallback

    @classmethod
    def set_user_language(cls, user: User, language_code: str) -> bool:
        """Set user's preferred language"""
        if not cls.is_language_active(language_code):
            logger.warning(f"Attempted to set inactive language: {language_code}")
            return False

        try:
            profile, created = UserLearningProfile.objects.get_or_create(
                user=user,
                defaults={"target_language": language_code}
            )
            if not created:
                profile.target_language = language_code
                profile.save()
            
            # Clear user's content cache when language changes
            cache.delete(f"user_content_{user.id}")
            
            logger.info(f"User {user.id} language set to {language_code}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set user language: {e}")
            return False

    @classmethod
    def get_language_choices_for_forms(cls) -> List[Tuple[str, str]]:
        """Get language choices formatted for Django forms"""
        active_languages = cls.get_supported_languages(active_only=True)
        return [
            (code, f"{info['flag']} {info['name']} ({info['native_name']})")
            for code, info in active_languages.items()
        ]

    @classmethod
    def validate_language_content_quality(cls, language_code: str) -> Dict:
        """Validate if a language has sufficient content quality for learning"""
        lang_info = cls.get_language_info(language_code)
        if not lang_info:
            return {"valid": False, "reason": "Language not supported"}
        
        if not lang_info["active"]:
            return {"valid": False, "reason": "Language coming soon"}
        
        if lang_info["content_quality"] == "low":
            return {
                "valid": True, 
                "warning": "Limited content available. More content coming soon!"
            }
        
        return {"valid": True}

    @classmethod
    def get_speech_synthesis_config(cls, language_code: str) -> Dict:
        """Get speech synthesis configuration for a language"""
        lang_info = cls.get_language_info(language_code)
        if not lang_info:
            return {"supported": False}
        
        return {
            "supported": True,
            "lang": lang_info["speech_code"],
            "rate": 0.8,
            "pitch": 1.0,
            "volume": 1.0,
        }
