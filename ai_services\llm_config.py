"""
LLM Configuration for TalonTalk Language Learning
Manages API keys, model selection, and provider-specific settings
"""

import os
from typing import Dict, Optional
from enum import Enum
from dataclasses import dataclass


class LLMProvider(Enum):
    OPENAI = "openai"
    OPENROUTER = "openrouter"
    DEEPSEEK = "deepseek"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"


@dataclass
class ModelConfig:
    name: str
    cost_per_1k_tokens: float
    max_tokens: int
    supports_function_calling: bool
    multilingual_quality: int  # 1-10 rating
    reasoning_quality: int  # 1-10 rating
    description: str


# Recommended models for language learning
LANGUAGE_LEARNING_MODELS = {
    LLMProvider.OPENROUTER: {
        "free": ModelConfig(
            name="mistralai/mistral-7b-instruct:free",
            cost_per_1k_tokens=0.0,
            max_tokens=8192,
            supports_function_calling=False,
            multilingual_quality=8,
            reasoning_quality=7,
            description="Free Mistral model, excellent for language learning, supports many languages",
        ),
        "premium": ModelConfig(
            name="anthropic/claude-3-sonnet:beta",
            cost_per_1k_tokens=0.003,
            max_tokens=200000,
            supports_function_calling=True,
            multilingual_quality=9,
            reasoning_quality=9,
            description="Claude 3 Sonnet via OpenRouter, excellent reasoning and multilingual support",
        ),
    },
    LLMProvider.DEEPSEEK: {
        "default": ModelConfig(
            name="deepseek-chat",
            cost_per_1k_tokens=0.00014,  # Very cost-effective
            max_tokens=4096,
            supports_function_calling=True,
            multilingual_quality=8,
            reasoning_quality=9,
            description="DeepSeek Chat model, excellent reasoning at very low cost",
        )
    },
    LLMProvider.OPENAI: {
        "default": ModelConfig(
            name="gpt-3.5-turbo",
            cost_per_1k_tokens=0.0015,
            max_tokens=4096,
            supports_function_calling=True,
            multilingual_quality=8,
            reasoning_quality=8,
            description="OpenAI GPT-3.5 Turbo, reliable and well-tested",
        ),
        "premium": ModelConfig(
            name="gpt-4o-mini",
            cost_per_1k_tokens=0.00015,
            max_tokens=128000,
            supports_function_calling=True,
            multilingual_quality=9,
            reasoning_quality=9,
            description="GPT-4o Mini, excellent quality at low cost",
        ),
    },
    LLMProvider.ANTHROPIC: {
        "default": ModelConfig(
            name="claude-3-haiku-20240307",
            cost_per_1k_tokens=0.00025,
            max_tokens=200000,
            supports_function_calling=False,
            multilingual_quality=9,
            reasoning_quality=8,
            description="Claude 3 Haiku, fast and cost-effective",
        )
    },
    LLMProvider.OLLAMA: {
        "deepseek": ModelConfig(
            name="deepseek-r1:8b",
            cost_per_1k_tokens=0.0,  # Free local model
            max_tokens=8192,
            supports_function_calling=False,
            multilingual_quality=10,  # Actually superior to most cloud models
            reasoning_quality=10,  # Best-in-class reasoning, beats GPT-4o
            description="Local DeepSeek R1 model via Ollama - SUPERIOR reasoning to cloud models, completely free",
        ),
        "mistral": ModelConfig(
            name="mistral:7b",
            cost_per_1k_tokens=0.0,  # Free local model
            max_tokens=8192,
            supports_function_calling=False,
            multilingual_quality=8,
            reasoning_quality=7,
            description="Local Mistral 7B model via Ollama, perfect for flashcards and language learning",
        ),
        "llama": ModelConfig(
            name="llama3.1:8b",
            cost_per_1k_tokens=0.0,  # Free local model
            max_tokens=8192,
            supports_function_calling=False,
            multilingual_quality=8,
            reasoning_quality=8,
            description="Local LLaMA 3.1 8B model via Ollama, excellent instruction following",
        ),
        "qwen": ModelConfig(
            name="qwen2.5:7b",
            cost_per_1k_tokens=0.0,  # Free local model
            max_tokens=8192,
            supports_function_calling=False,
            multilingual_quality=9,
            reasoning_quality=8,
            description="Local Qwen 2.5 7B model via Ollama, fastest inference and excellent for multilingual support",
        ),
    },
}


class LLMConfig:
    def __init__(
        self,
        provider: LLMProvider = LLMProvider.OPENROUTER,
        model_tier: str = "free",
        api_key: Optional[str] = None,
    ):
        self.provider = provider
        self.model_tier = model_tier
        self.api_key = api_key or self._get_api_key(provider)
        self.model_config = self._get_model_config(provider, model_tier)

    def _get_api_key(self, provider: LLMProvider) -> Optional[str]:
        """Get API key from environment variables"""
        env_var_map = {
            LLMProvider.OPENAI: "OPENAI_API_KEY",
            LLMProvider.OPENROUTER: "OPENROUTER_API_KEY",
            LLMProvider.DEEPSEEK: "DEEPSEEK_API_KEY",
            LLMProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
            LLMProvider.OLLAMA: None,  # No API key needed for local Ollama
        }
        env_var = env_var_map.get(provider)
        return os.getenv(env_var) if env_var else None

    def _get_model_config(
        self, provider: LLMProvider, tier: str
    ) -> Optional[ModelConfig]:
        """Get model configuration for provider and tier"""
        provider_models = LANGUAGE_LEARNING_MODELS.get(provider, {})
        config = provider_models.get(tier, provider_models.get("default"))
        if not config and provider_models:
            # If no specific tier found, get the first available config
            config = list(provider_models.values())[0]
        return config

    @property
    def model_name(self) -> str:
        return self.model_config.name if self.model_config else "unknown"

    @property
    def base_url(self) -> Optional[str]:
        """Get base URL for API requests"""
        base_urls = {
            LLMProvider.OPENROUTER: "https://openrouter.ai/api/v1",
            LLMProvider.DEEPSEEK: "https://api.deepseek.com/v1",
            LLMProvider.ANTHROPIC: "https://api.anthropic.com/v1",
            LLMProvider.OPENAI: None,  # Uses default OpenAI base URL
            LLMProvider.OLLAMA: "http://localhost:11434/v1",  # Local Ollama server
        }
        return base_urls.get(self.provider)

    def get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        base_headers = {"Content-Type": "application/json"}

        if self.provider == LLMProvider.OPENROUTER:
            base_headers.update(
                {
                    "Authorization": f"Bearer {self.api_key}",
                    "HTTP-Referer": "https://talontalk.com",  # Optional: for analytics
                    "X-Title": "TalonTalk Language Learning",  # Optional: for analytics
                }
            )
        elif self.provider == LLMProvider.ANTHROPIC:
            base_headers.update(
                {"x-api-key": self.api_key, "anthropic-version": "2023-06-01"}
            )
        elif self.provider == LLMProvider.OLLAMA:
            # Ollama doesn't need authorization for local requests
            pass
        else:
            if self.api_key:
                base_headers["Authorization"] = f"Bearer {self.api_key}"

        return base_headers


def get_tier_config(tier: str = "content_generation") -> LLMConfig:
    """
    Get optimized configuration for specific tasks

    Tiers:
    - content_generation: Maximum quality for lesson/flashcard creation (DeepSeek R1)
    - real_time: Fast responses for live features (Qwen 2.5)
    - quality_check: Deep analysis for content validation (DeepSeek R1)
    """
    tier_preferences = {
        "content_generation": ["deepseek", "qwen", "llama", "mistral"],
        "real_time": ["qwen", "llama", "deepseek", "mistral"],
        "quality_check": ["deepseek", "llama", "qwen", "mistral"],
    }

    preferred_models = tier_preferences.get(
        tier, ["deepseek", "qwen", "llama", "mistral"]
    )

    # Check if Ollama is running locally
    try:
        import requests

        response = requests.get("http://localhost:11434/api/tags", timeout=2)
        if response.status_code == 200:
            models = response.json().get("models", [])
            model_names = [model.get("name", "").lower() for model in models]

            # Try preferred models in order
            for preferred in preferred_models:
                if preferred == "deepseek" and any(
                    "deepseek-r1" in name for name in model_names
                ):
                    print(f"🚀 Using DeepSeek R1 for {tier} (SUPERIOR reasoning)")
                    return LLMConfig(LLMProvider.OLLAMA, "deepseek")
                elif preferred == "qwen" and any(
                    "qwen2.5:7b" in name for name in model_names
                ):
                    print(f"⚡ Using Qwen 2.5 for {tier} (FASTEST multilingual)")
                    return LLMConfig(LLMProvider.OLLAMA, "qwen")
                elif preferred == "llama" and any(
                    "llama3.1:8b" in name for name in model_names
                ):
                    print(f"⭐ Using LLaMA 3.1 for {tier} (RELIABLE overall)")
                    return LLMConfig(LLMProvider.OLLAMA, "llama")
                elif preferred == "mistral" and any(
                    "mistral:7b" in name for name in model_names
                ):
                    print(f"📚 Using Mistral 7B for {tier} (GOOD baseline)")
                    return LLMConfig(LLMProvider.OLLAMA, "mistral")
    except Exception as e:
        print(f"⚠️ Ollama check failed: {e}")

    # Fallback to cloud providers
    return get_recommended_config()


def get_recommended_config() -> LLMConfig:
    """Get recommended configuration for language learning"""  # Priority order: Local Ollama (DeepSeek R1 > Qwen 2.5 > LLaMA 3.1 > Mistral) > OpenRouter free > DeepSeek > OpenAI

    # First check if Ollama is running locally (best option - free and reliable)
    try:
        import requests

        response = requests.get("http://localhost:11434/api/tags", timeout=2)
        if response.status_code == 200:
            # Check what models are available locally
            models = response.json().get("models", [])
            model_names = [model.get("name", "").lower() for model in models]

            # Priority order: DeepSeek R1 (best reasoning) > Qwen 2.5 (best multilingual) > LLaMA 3.1 (excellent overall) > Mistral 7B (fallback)
            if any("deepseek-r1" in name for name in model_names):
                print(
                    f"🚀 Using local Ollama with DeepSeek R1 (BEST: superior reasoning & multilingual)"
                )
                return LLMConfig(LLMProvider.OLLAMA, "deepseek")
            elif any("qwen2.5:7b" in name for name in model_names):
                print(
                    f"🌟 Using local Ollama with Qwen 2.5 (EXCELLENT: best multilingual support)"
                )
                return LLMConfig(LLMProvider.OLLAMA, "qwen")
            elif any("llama3.1:8b" in name for name in model_names):
                print(
                    f"⭐ Using local Ollama with LLaMA 3.1 (GREAT: excellent overall performance)"
                )
                return LLMConfig(LLMProvider.OLLAMA, "llama")
            elif any("mistral:7b" in name for name in model_names):
                print(
                    f"📚 Using local Ollama with Mistral 7B (GOOD: basic language learning)"
                )
                return LLMConfig(LLMProvider.OLLAMA, "mistral")
            else:
                # Fallback to any available model
                if models:
                    first_model = models[0].get("name", "")
                    print(f"🎯 Using local Ollama with {first_model}")
                    return LLMConfig(LLMProvider.OLLAMA, "mistral")  # Default fallback
    except Exception as e:
        print(f"⚠️ Ollama check failed: {e}")

    # Fallback to cloud providers
    if os.getenv("OPENROUTER_API_KEY"):
        return LLMConfig(LLMProvider.OPENROUTER, "free")
    elif os.getenv("DEEPSEEK_API_KEY"):
        return LLMConfig(LLMProvider.DEEPSEEK, "default")
    elif os.getenv("OPENAI_API_KEY"):
        return LLMConfig(LLMProvider.OPENAI, "premium")  # Use GPT-4o mini if available
    else:
        # Return config without API key for demo mode
        return LLMConfig(LLMProvider.OPENROUTER, "free", api_key=None)


def print_provider_comparison():
    """Print comparison of different providers for language learning"""
    print("🤖 LLM Provider Comparison for Language Learning:")
    print("=" * 60)

    for provider, models in LANGUAGE_LEARNING_MODELS.items():
        print(f"\n{provider.value.upper()}:")
        for tier, config in models.items():
            print(f"  {tier}: {config.name}")
            print(f"    💰 Cost: ${config.cost_per_1k_tokens:.5f}/1K tokens")
            print(f"    🌍 Multilingual: {config.multilingual_quality}/10")
            print(f"    🧠 Reasoning: {config.reasoning_quality}/10")
            print(f"    📝 {config.description}")


if __name__ == "__main__":
    print_provider_comparison()

    config = get_recommended_config()
    print(f"\n🎯 Recommended config: {config.provider.value} - {config.model_name}")
