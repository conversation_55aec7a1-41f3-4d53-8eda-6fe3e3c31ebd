export = TalonTalkAdaptiveLearning;
/**
 * TalonTalk Content Preloading and Adaptive Learning System
 * ========================================================
 *
 * This JavaScript module handles intelligent content preloading, adaptive learning,
 * performance tracking, and seamless offline/online learning experiences.
 *
 * Features:
 * - Content preloading and caching
 * - Performance tracking and analytics
 * - Adaptive difficulty adjustment
 * - Offline learning support
 * - Real-time recommendations
 */
declare class TalonTalkAdaptiveLearning {
    apiBase: string;
    cache: Map<any, any>;
    performanceData: {
        sessionId: string;
        startTime: number;
        questions: never[];
        currentStreak: number;
        totalCorrect: number;
        totalQuestions: number;
    };
    isOnline: boolean;
    preloadedContent: any;
    adaptiveParams: any;
    init(): Promise<void>;
    setupEventListeners(): void;
    preloadDailyContent(options?: {}): Promise<any>;
    getAdaptiveContent(sessionContext?: {}): Promise<any>;
    trackPerformance(performanceData?: null): Promise<any>;
    recordQuestionAttempt(question: any, userAnswer: any, isCorrect: any, responseTime: any, usedHint?: boolean): void;
    getContentRecommendations(): Promise<any>;
    getLearningAnalytics(): Promise<any>;
    checkForDifficultyAdjustment(): void;
    cacheContent(key: any, content: any): void;
    getCachedContent(key: any): any;
    updateProgressUI(): void;
    updateRecommendations(insights: any): void;
    showLearningInsights(insights: any): void;
    showNotification(message: any, type?: string): void;
    apiCall(endpoint: any, method?: string, data?: null): Promise<any>;
    generateSessionId(): string;
    calculateAverageResponseTime(questions: any): number;
    getCurrentDifficulty(): any;
    updateDifficulty(newDifficulty: any): void;
    getNextDifficulty(current: any, direction: any): any;
    getCSRFToken(): any;
    isUserAuthenticated(): boolean;
    saveToLocalStorage(): void;
    loadFromLocalStorage(): void;
    getFallbackContent(language: any, difficulty: any): {
        flashcards: {
            id: string;
            question: string;
            options: string[];
            correct_answer: string;
            hint: string;
            explanation: string;
            difficulty: any;
            language: any;
        }[];
        is_fallback: boolean;
        message: string;
    };
    setupPeriodicSync(): void;
    syncPerformanceData(): Promise<void>;
    getNotificationClass(type: any): any;
}
//# sourceMappingURL=adaptive-learning.d.ts.map