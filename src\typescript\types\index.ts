/**
 * TalonTalk Application Type Definitions
 * Core types for the language learning platform
 */

// ==================== CARE Framework Types ====================

export interface CAREPhase {
    phase: 'contextualize' | 'acquire' | 'reinforce' | 'extend';
    title: string;
    description: string;
    content: CAREPhaseContent;
    completed: boolean;
    progress: number;
}

export interface CAREPhaseContent {
    scenario?: Scenario;
    cultural_context?: CulturalContext;
    key_phrases?: KeyPhrase[];
    vocabulary?: VocabularyItem[];
    grammar?: GrammarSection;
    exercises?: Exercise[];
    real_world_applications?: RealWorldApplication[];
    expansion_topics?: ExpansionTopic[];
    homework?: Homework;
}

export interface Scenario {
    title: string;
    description: string;
    location?: string;
    setting: string;
    objectives: string[];
}

export interface CulturalContext {
    title: string;
    facts: string[];
    tips: string[];
    etiquette: string[];
}

export interface KeyPhrase {
    spanish: string;
    english: string;
    pronunciation: string;
    context: string;
    formality: 'formal' | 'informal' | 'neutral';
}

export interface VocabularyItem {
    word: string;
    translation: string;
    pronunciation: string;
    part_of_speech: string;
    example: string;
    example_translation: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    tags: string[];
}

export interface GrammarSection {
    topic: string;
    explanation: string;
    structures: GrammarStructure[];
    rules: string[];
}

export interface GrammarStructure {
    pattern: string;
    meaning: string;
    examples: string[];
    usage_notes: string[];
}

// ==================== Exercise Types ====================

export type ExerciseType = 'multiple_choice' | 'translation' | 'pronunciation' | 'conversation' | 'fill_blank' | 'matching';

export interface Exercise {
    id: string;
    type: ExerciseType;
    question: string;
    difficulty: 'easy' | 'medium' | 'hard';
    points: number;
    time_limit?: number;
}

export interface MultipleChoiceExercise extends Exercise {
    type: 'multiple_choice';
    options: string[];
    correct: number;
    explanation: string;
}

export interface TranslationExercise extends Exercise {
    type: 'translation';
    answer: string;
    alternative_answers?: string[];
    explanation: string;
}

export interface PronunciationExercise extends Exercise {
    type: 'pronunciation';
    phrase: string;
    translation: string;
    phonetic: string;
    audio_url?: string;
}

export interface ConversationExercise extends Exercise {
    type: 'conversation';
    scenario: string;
    responses: ConversationResponse[];
}

export interface ConversationResponse {
    text: string;
    is_correct: boolean;
    feedback: string;
    leads_to?: string;
}

// ==================== Real World & Extension Types ====================

export interface RealWorldApplication {
    title: string;
    description: string;
    scenario: string;
    tasks: string[];
    skills_practiced: string[];
    difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface ExpansionTopic {
    topic: string;
    vocabulary: string[];
    phrases: string[];
    cultural_notes: string[];
    related_lessons: string[];
}

export interface Homework {
    title: string;
    description: string;
    steps: string[];
    due_date?: string;
    estimated_time: number;
    requirements: string[];
}

// ==================== API Response Types ====================

export interface APIResponse<T = any> {
    success: boolean;
    content?: T;
    error?: string;
    message?: string;
    timestamp?: string;
}

export interface CAREPhaseAPIResponse extends APIResponse<CAREPhaseContent> {
    phase: string;
    lesson_id: string;
}

// ==================== User & Progress Types ====================

export interface User {
    id: string;
    username: string;
    email: string;
    profile: UserProfile;
    progress: UserProgress;
}

export interface UserProfile {
    native_language: string;
    target_language: string;
    level: 'beginner' | 'intermediate' | 'advanced';
    learning_goals: string[];
    daily_goal_minutes: number;
    timezone: string;
}

export interface UserProgress {
    total_lessons: number;
    completed_lessons: number;
    current_streak: number;
    total_points: number;
    level: number;
    achievements: Achievement[];
}

export interface Achievement {
    id: string;
    title: string;
    description: string;
    icon: string;
    earned_date: string;
    points: number;
}

// ==================== Lesson Types ====================

export interface Lesson {
    id: string;
    title: string;
    description: string;
    level: 'beginner' | 'intermediate' | 'advanced';
    duration_minutes: number;
    phases: CAREPhase[];
    prerequisites: string[];
    learning_objectives: string[];
    tags: string[];
    created_date: string;
    updated_date: string;
}

// ==================== Flashcard Types ====================

export interface Flashcard {
    id: string;
    front: string;
    back: string;
    type: 'vocabulary' | 'phrase' | 'grammar';
    difficulty: 'easy' | 'medium' | 'hard';
    tags: string[];
    lesson_id: string;
    created_date: string;
    last_reviewed?: string;
    review_count: number;
    success_rate: number;
}

export interface FlashcardSession {
    id: string;
    flashcards: Flashcard[];
    current_index: number;
    correct_answers: number;
    total_answers: number;
    start_time: string;
    end_time?: string;
    session_type: 'review' | 'new' | 'mixed';
}

// ==================== AI Tutor Types ====================

export interface AITutorMessage {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
    context?: string;
    suggestions?: string[];
}

export interface AITutorSession {
    id: string;
    messages: AITutorMessage[];
    lesson_context?: string;
    phase_context?: string;
    user_id: string;
    created_date: string;
}

// ==================== Event and DOM Types ====================

export interface CARELessonEvent {
    type: 'phase_change' | 'exercise_complete' | 'lesson_complete';
    data: any;
    timestamp: string;
}

export interface ModalConfig {
    title: string;
    content: string;
    buttons: ModalButton[];
    closable: boolean;
    size: 'small' | 'medium' | 'large' | 'fullscreen';
}

export interface ModalButton {
    text: string;
    action: () => void;
    style: 'primary' | 'secondary' | 'danger' | 'success';
}

// ==================== Utility Types ====================

export type EventCallback<T = any> = (data: T) => void;

export interface EventEmitter {
    on<T>(event: string, callback: EventCallback<T>): void;
    off<T>(event: string, callback: EventCallback<T>): void;
    emit<T>(event: string, data: T): void;
}

export interface AnimationConfig {
    duration: number;
    easing: string;
    delay?: number;
    onComplete?: () => void;
}

// ==================== Component Props Types ====================

export interface BaseComponentProps {
    className?: string;
    id?: string;
    style?: Partial<CSSStyleDeclaration>;
}

export interface CARELessonManagerProps extends BaseComponentProps {
    lessonId: string;
    initialPhase?: string;
    onPhaseChange?: (phase: string) => void;
    onLessonComplete?: () => void;
}

export interface WelcomeModalProps extends BaseComponentProps {
    showOnMount?: boolean;
    steps: WelcomeStep[];
    onComplete?: () => void;
}

export interface WelcomeStep {
    title: string;
    content: string;
    action?: () => Promise<void>;
    skippable: boolean;
}

// ==================== Django Integration Types ====================

export interface CSRFToken {
    value: string;
    headerName: string;
}

export interface DjangoContext {
    user: User | null;
    csrfToken: CSRFToken;
    staticUrl: string;
    mediaUrl: string;
    debug: boolean;
}

// ==================== Error Types ====================

export class CAREError extends Error {
    code: string;
    phase?: string | undefined;
    context?: any;

    constructor(message: string, code: string, phase?: string | undefined, context?: any) {
        super(message);
        this.name = 'CAREError';
        this.code = code;
        this.phase = phase;
        this.context = context;
    }
}

export interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: any;
}
