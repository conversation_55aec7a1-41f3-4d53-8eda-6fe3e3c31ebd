"""
Django management command to populate the content pipeline for C.A.R.E. framework
Usage: python manage.py populate_content_pipeline --language spanish --difficulty 1-5 --batch-size 50
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from lessons.content_pipeline import content_pipeline
from lessons.models import ContentItem
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Populate content pipeline with C.A.R.E. framework content"

    def add_arguments(self, parser):
        parser.add_argument(
            "--language",
            type=str,
            default="spanish",
            help="Target language for content generation (default: spanish)",
        )
        parser.add_argument(
            "--difficulty",
            type=str,
            default="1-3",
            help='Difficulty range (e.g., "1-3" or "2" for single level)',
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=20,
            help="Number of content items to generate per batch (default: 20)",
        )
        parser.add_argument(
            "--content-types",
            type=str,
            default="flashcard,mcq,translation,grammar",
            help="Comma-separated list of content types to generate",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force regeneration even if content exists",
        )

    def handle(self, *args, **options):
        """Execute the content pipeline population"""

        language = options["language"]
        batch_size = options["batch_size"]
        content_types = options["content_types"].split(",")
        force = options["force"]

        # Parse difficulty range
        difficulty_range = self.parse_difficulty_range(options["difficulty"])

        self.stdout.write(
            self.style.SUCCESS(f"Starting C.A.R.E. content pipeline for {language}")
        )
        self.stdout.write(f"Difficulty levels: {difficulty_range}")
        self.stdout.write(f"Content types: {content_types}")
        self.stdout.write(f"Batch size: {batch_size}")

        total_generated = 0
        total_errors = []

        for difficulty in difficulty_range:
            self.stdout.write(
                f"\n📚 Generating content for difficulty level {difficulty}..."
            )

            # Check if content already exists (unless forcing)
            if not force:
                existing_count = ContentItem.objects.filter(
                    language=language, difficulty=difficulty, is_active=True
                ).count()

                if existing_count >= batch_size:
                    self.stdout.write(
                        self.style.WARNING(
                            f"  Skipping difficulty {difficulty} - {existing_count} items already exist. Use --force to regenerate."
                        )
                    )
                    continue

            try:
                # Generate content batch
                result = content_pipeline.generate_care_content_batch(
                    language=language,
                    difficulty_level=difficulty,
                    content_types=content_types,
                    batch_size=batch_size,
                )

                if result["success"]:
                    generated_count = result["generated_count"]
                    total_generated += generated_count

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"  ✅ Generated {generated_count} items for difficulty {difficulty}"
                        )
                    )

                    # Show content breakdown by C.A.R.E. phase
                    self.show_care_breakdown(result["content_items"])

                    if result["errors"]:
                        self.stdout.write(
                            self.style.WARNING(
                                f"  ⚠️  {len(result['errors'])} items failed to generate"
                            )
                        )
                        total_errors.extend(result["errors"])
                else:
                    self.stdout.write(
                        self.style.ERROR(
                            f"  ❌ Failed to generate content for difficulty {difficulty}"
                        )
                    )

            except Exception as e:
                error_msg = f"Error generating difficulty {difficulty}: {str(e)}"
                self.stdout.write(self.style.ERROR(f"  ❌ {error_msg}"))
                total_errors.append(error_msg)

        # Summary
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write(self.style.SUCCESS(f"📊 CONTENT PIPELINE SUMMARY"))
        self.stdout.write(f"Total content items generated: {total_generated}")
        self.stdout.write(f"Total errors: {len(total_errors)}")

        if total_errors:
            self.stdout.write("\n🐛 Errors encountered:")
            for error in total_errors[:5]:  # Show first 5 errors
                self.stdout.write(f"  - {error}")
            if len(total_errors) > 5:
                self.stdout.write(f"  ... and {len(total_errors) - 5} more errors")

        # Database stats
        total_content = ContentItem.objects.filter(
            language=language, is_active=True
        ).count()

        self.stdout.write(f"\n📈 Database Statistics:")
        self.stdout.write(f"Total active {language} content: {total_content} items")

        # Content distribution by difficulty
        for difficulty in range(1, 6):
            count = ContentItem.objects.filter(
                language=language, difficulty=difficulty, is_active=True
            ).count()
            self.stdout.write(f"  Difficulty {difficulty}: {count} items")

        self.stdout.write(
            self.style.SUCCESS(
                f"\n🎉 Content pipeline completed! Ready for C.A.R.E. framework learning."
            )
        )

    def parse_difficulty_range(self, difficulty_str):
        """Parse difficulty range string into list of integers"""
        try:
            if "-" in difficulty_str:
                start, end = map(int, difficulty_str.split("-"))
                return list(range(start, end + 1))
            else:
                return [int(difficulty_str)]
        except ValueError:
            raise CommandError(f"Invalid difficulty range: {difficulty_str}")

    def show_care_breakdown(self, content_items):
        """Show breakdown of content by C.A.R.E. phases"""
        care_phases = ["contextualize", "acquire", "reinforce", "extend"]
        breakdown = {phase: 0 for phase in care_phases}

        for item in content_items:
            for phase in care_phases:
                if phase in item.tags:
                    breakdown[phase] += 1
                    break

        self.stdout.write("    C.A.R.E. Phase Distribution:")
        for phase, count in breakdown.items():
            if count > 0:
                self.stdout.write(f"      {phase.capitalize()}: {count} items")
