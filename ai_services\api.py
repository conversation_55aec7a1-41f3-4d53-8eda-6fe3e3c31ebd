"""
FastAPI Microservice for TalonTalk AI Features
Provides LLM-powered flashcard generation, grading, and language learning features
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
from dotenv import load_dotenv
import os

try:
    from .llm_flashcards import (
        LLMFlashcardService,
        FlashcardRequest,
        FlashcardResponse,
        DifficultyLevel,
        ExerciseType,
        get_vocabulary_for_level,
        get_grammar_topics,
    )
except ImportError:
    # Fallback for development without relative imports
    from ai_services.llm_flashcards import (
        LLMFlashcardService,
        FlashcardRequest,
        FlashcardResponse,
        DifficultyLevel,
        ExerciseType,
        get_vocabulary_for_level,
        get_grammar_topics,
    )
try:
    from .llm_config import get_recommended_config, print_provider_comparison
except ImportError:
    from ai_services.llm_config import get_recommended_config, print_provider_comparison

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="TalonTalk AI Services",
    description="LLM-powered language learning features for TalonTalk",
    version="1.0.0",
)

# Add CORS middleware for Django integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ],  # Django dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize LLM service with automatic provider selection
try:
    config = get_recommended_config()
    llm_service = LLMFlashcardService(config)
    logger.info(
        f"🤖 Initialized LLM service with {config.provider.value} - {config.model_name}"
    )
except Exception as e:
    logger.error(f"Failed to initialize LLM service: {e}")
    llm_service = None


# Pydantic models for API
class FlashcardRequestAPI(BaseModel):
    language: str = "english"
    target_language: str
    difficulty: str  # "beginner", "intermediate", "advanced"
    exercise_type: str  # "translation", "multiple_choice", etc.
    vocabulary: Optional[List[str]] = None
    grammar_topic: Optional[str] = None
    context: Optional[str] = None


class FlashcardResponseAPI(BaseModel):
    question: str
    correct_answer: str
    options: Optional[List[str]] = None
    explanation: str
    hint: str
    example_sentence: str
    pronunciation_guide: str


class GradeRequestAPI(BaseModel):
    question: str
    correct_answer: str
    user_answer: str
    language: str


class GradeResponseAPI(BaseModel):
    score: int
    is_correct: bool
    feedback: str
    suggestions: List[str]


@app.get("/")
async def root():
    return {"message": "TalonTalk LLM Service is running"}


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "llm-flashcards"}


@app.post("/generate-flashcard", response_model=FlashcardResponseAPI)
async def generate_flashcard(request: FlashcardRequestAPI):
    """Generate a flashcard based on the request parameters"""
    try:
        # Convert API request to internal request
        internal_request = FlashcardRequest(
            language=request.language,
            target_language=request.target_language,
            difficulty=DifficultyLevel(request.difficulty),
            exercise_type=ExerciseType(request.exercise_type),
            vocabulary=request.vocabulary,
            grammar_topic=request.grammar_topic,
            context=request.context,
        )

        # Generate flashcard
        flashcard = llm_service.generate_flashcard(internal_request)

        # Convert to API response
        return FlashcardResponseAPI(
            question=flashcard.question,
            correct_answer=flashcard.correct_answer,
            options=flashcard.options,
            explanation=flashcard.explanation,
            hint=flashcard.hint,
            example_sentence=flashcard.example_sentence,
            pronunciation_guide=flashcard.pronunciation_guide,
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid request: {str(e)}")
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error generating flashcard: {str(e)}"
        )


@app.post("/grade-answer", response_model=GradeResponseAPI)
async def grade_answer(request: GradeRequestAPI):
    """Grade a user's answer and provide feedback"""
    try:
        result = llm_service.grade_answer(
            question=request.question,
            correct_answer=request.correct_answer,
            user_answer=request.user_answer,
            language=request.language,
        )

        return GradeResponseAPI(
            score=result["score"],
            is_correct=result["is_correct"],
            feedback=result["feedback"],
            suggestions=result["suggestions"],
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error grading answer: {str(e)}")


@app.get("/vocabulary/{language}/{difficulty}")
async def get_vocabulary(language: str, difficulty: str):
    """Get vocabulary words for a language and difficulty level"""
    try:
        diff_level = DifficultyLevel(difficulty)
        vocab = get_vocabulary_for_level(language, diff_level)
        return {"language": language, "difficulty": difficulty, "vocabulary": vocab}
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid difficulty level")


@app.get("/grammar-topics/{language}/{difficulty}")
async def get_grammar(language: str, difficulty: str):
    """Get grammar topics for a language and difficulty level"""
    try:
        diff_level = DifficultyLevel(difficulty)
        topics = get_grammar_topics(language, diff_level)
        return {
            "language": language,
            "difficulty": difficulty,
            "grammar_topics": topics,
        }
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid difficulty level")


@app.get("/supported-languages")
async def get_supported_languages():
    """Get list of supported languages"""
    return {
        "languages": ["spanish", "french", "german", "italian", "portuguese"],
        "difficulties": ["beginner", "intermediate", "advanced"],
        "exercise_types": [
            "translation",
            "multiple_choice",
            "fill_blank",
            "sentence_construction",
            "listening_comprehension",
        ],
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8001)
