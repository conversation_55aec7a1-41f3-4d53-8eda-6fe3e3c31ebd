#!/usr/bin/env python
"""
Test script to debug C.A.R.E. content generation
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()


def test_content_generation():
    """Test the content generation pipeline"""
    print("🧪 Testing C.A.R.E. Content Generation")
    print("=" * 50)

    try:
        # Import content generation services
        from lessons.content_quality_gateway import (
            ContentQualityGateway,
            ContentGenerationRequest,
        )
        from ai_services.llm_flashcards import LLMFlashcardService
        from ai_services.llm_config import get_recommended_config

        print("✅ Imports successful")

        # Initialize the quality gateway with LLM service
        print("🔧 Initializing LLM service...")
        llm_service = LLMFlashcardService(get_recommended_config())
        print("✅ LLM service initialized")

        print("🔧 Initializing quality gateway...")
        gateway = ContentQualityGateway(llm_service)
        print("✅ Quality gateway initialized")

        # Create content generation request for this specific phase
        print("🔧 Creating content generation request...")
        request_data = ContentGenerationRequest(
            user_id=0,
            content_type="flashcard",
            target_language="spanish",
            difficulty_level="1",
            topic="daily_conversation",
            context="C.A.R.E. contextualize phase for current_events",
            quantity=3,
            session_type="focused_practice",
        )
        print("✅ Request created successfully")

        # Generate content using the quality gateway
        print("🚀 Generating content...")
        result = gateway.generate_quality_content(request_data)

        print(f"📊 Generation result: success={result.success}")
        if result.success:
            print(f"📝 Content items: {len(result.content)}")
            print(f"⭐ Quality score: {result.quality_score}")
            print(f"⏱️ Generation time: {result.generation_time:.2f}s")
            print(f"🔧 Issues fixed: {len(result.issues_fixed)}")
            print(f"🔄 Fallback used: {getattr(result, 'fallback_used', False)}")

            # Show first content item
            if result.content:
                first_item = result.content[0]
                print("\n📋 First generated item:")
                print(f"   Question: {first_item.get('question', 'N/A')}")
                print(f"   Answer: {first_item.get('correct_answer', 'N/A')}")
                print(f"   Explanation: {first_item.get('explanation', 'N/A')}")
        else:
            print("❌ Generation failed")
            print(f"🔍 Issues found: {result.issues_found}")
            print(f"🔧 Issues fixed: {result.issues_fixed}")
            print(f"⏱️ Generation time: {result.generation_time:.2f}s")
            print(f"🔄 Fallback used: {getattr(result, 'fallback_used', False)}")

    except Exception as e:
        print(f"💥 Error: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


def test_care_view():
    """Test the C.A.R.E. view directly"""
    print("\n🧪 Testing C.A.R.E. View")
    print("=" * 50)

    try:
        from django.test import RequestFactory
        from care.views import care_phase_data

        # Create a mock request
        factory = RequestFactory()
        request = factory.get(
            "/care/api/phase/contextualize/?lesson=current_events&topic=daily_conversation&difficulty=1"
        )

        print("🔧 Making request to care_phase_data...")
        response = care_phase_data(request, "contextualize")

        print(f"📊 Response status: {response.status_code}")

        if hasattr(response, "content"):
            import json

            content = json.loads(response.content.decode("utf-8"))
            print(f"📝 Response success: {content.get('success', False)}")

            if content.get("success"):
                print("✅ C.A.R.E. view working correctly")
                if "fallback_used" in content:
                    print(f"⚠️ Fallback used: {content['fallback_used']}")
                if "quality_score" in content:
                    print(f"⭐ Quality score: {content['quality_score']}")
            else:
                print(
                    f"❌ C.A.R.E. view failed: {content.get('error', 'Unknown error')}"
                )

    except Exception as e:
        print(f"💥 Error testing C.A.R.E. view: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    print("🚀 Starting C.A.R.E. Content Generation Tests")
    print("=" * 60)

    # Test 1: Content Generation Pipeline
    success1 = test_content_generation()

    # Test 2: C.A.R.E. View
    success2 = test_care_view()

    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"   Content Generation: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   C.A.R.E. View: {'✅ PASS' if success2 else '❌ FAIL'}")

    if success1 and success2:
        print("\n🎉 All tests passed! C.A.R.E. system should be working.")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
