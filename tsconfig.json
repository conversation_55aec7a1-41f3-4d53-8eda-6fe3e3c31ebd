{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "module": "ES2020", "outDir": "./talontalk/static/js/dist", "rootDir": "./src/typescript", "sourceMap": true, "declaration": true, "declarationMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true}, "include": ["src/typescript/**/*"], "exclude": ["node_modules", "talontalk/static/js/**/*", "staticfiles", "**/*.min.js", "**/*test*"]}