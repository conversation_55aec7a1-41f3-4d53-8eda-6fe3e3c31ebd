/**
 * Adaptive Learning System - TypeScript Edition
 * Intelligent content adaptation based on user performance and preferences
 */
class AdaptiveLearningSystem {
    constructor(userId) {
        this.sessionData = new Map();
        this.profile = this.loadProfile(userId);
        this.metrics = this.loadMetrics(userId);
        this.rules = this.getDefaultRules();
        this.init();
    }
    init() {
        this.startSession();
        this.setupPerformanceTracking();
    }
    startSession() {
        const sessionId = `session_${Date.now()}`;
        this.sessionData.set('sessionId', sessionId);
        this.sessionData.set('startTime', Date.now());
        this.sessionData.set('interactions', []);
        console.log(`📊 Adaptive Learning Session Started: ${sessionId}`);
    }
    setupPerformanceTracking() {
        // Track user interactions across the application
        document.addEventListener('care-exercise-complete', (event) => {
            const customEvent = event;
            this.trackExerciseCompletion(customEvent.detail);
        });
        document.addEventListener('care-phase-complete', (event) => {
            const customEvent = event;
            this.trackPhaseCompletion(customEvent.detail);
        });
        // Track time spent on different activities
        this.setupTimeTracking();
    }
    setupTimeTracking() {
        let phaseStartTime = null;
        let currentPhase = null;
        // Listen for phase changes
        document.addEventListener('care-phase-change', (event) => {
            const customEvent = event;
            const newPhase = customEvent.detail.phase;
            // Record time spent on previous phase
            if (phaseStartTime && currentPhase) {
                const timeSpent = Date.now() - phaseStartTime;
                this.trackTimeSpent(currentPhase, timeSpent);
            }
            // Start tracking new phase
            currentPhase = newPhase;
            phaseStartTime = Date.now();
        });
    }
    trackExerciseCompletion(data) {
        const interactions = this.sessionData.get('interactions') || [];
        interactions.push({
            interactionType: 'exercise',
            timestamp: Date.now(),
            ...data
        });
        this.sessionData.set('interactions', interactions);
        // Update metrics based on performance
        this.updateMetrics(data);
        // Check if adaptation is needed
        this.checkAdaptation();
    }
    trackPhaseCompletion(data) {
        const interactions = this.sessionData.get('interactions') || [];
        interactions.push({
            interactionType: 'phase',
            timestamp: Date.now(),
            ...data
        });
        this.sessionData.set('interactions', interactions);
        // Analyze phase performance
        this.analyzePhasePerformance(data);
    }
    trackTimeSpent(activity, timeSpent) {
        const interactions = this.sessionData.get('interactions') || [];
        interactions.push({
            interactionType: 'time_tracking',
            activity,
            timeSpent,
            timestamp: Date.now()
        });
        this.sessionData.set('interactions', interactions);
    }
    updateMetrics(exerciseData) {
        // Update accuracy
        const currentAccuracy = this.metrics.accuracy;
        const newAccuracy = (currentAccuracy * 0.9) + (exerciseData.correct ? 0.1 : 0);
        this.metrics.accuracy = Math.max(0, Math.min(1, newAccuracy));
        // Update speed (inverse of time spent)
        const normalizedTime = Math.min(exerciseData.timeSpent / 60000, 1); // Normalize to minutes
        const speedScore = 1 - normalizedTime;
        this.metrics.speed = (this.metrics.speed * 0.9) + (speedScore * 0.1);
        // Update engagement based on attempts (fewer attempts = better engagement)
        const engagementScore = Math.max(0, 1 - ((exerciseData.attempts - 1) * 0.2));
        this.metrics.engagement = (this.metrics.engagement * 0.9) + (engagementScore * 0.1);
        this.saveMetrics();
    }
    analyzePhasePerformance(phaseData) {
        const { phase, score, completionRate } = phaseData;
        // Identify weak areas
        if (score < 0.7 || completionRate < 0.8) {
            if (!this.profile.weakAreas.includes(phase)) {
                this.profile.weakAreas.push(phase);
            }
        }
        else if (score > 0.9 && completionRate > 0.9) {
            // Identify strong areas
            if (!this.profile.strongAreas.includes(phase)) {
                this.profile.strongAreas.push(phase);
            }
            // Remove from weak areas if it was there
            const weakIndex = this.profile.weakAreas.indexOf(phase);
            if (weakIndex > -1) {
                this.profile.weakAreas.splice(weakIndex, 1);
            }
        }
        this.saveProfile();
    }
    checkAdaptation() {
        const recentInteractions = this.getRecentInteractions(10);
        const recentAccuracy = this.calculateRecentAccuracy(recentInteractions);
        // Adjust difficulty based on performance
        if (recentAccuracy > this.rules.difficultyAdjustment.threshold + 0.2) {
            this.suggestDifficultyIncrease();
        }
        else if (recentAccuracy < this.rules.difficultyAdjustment.threshold - 0.2) {
            this.suggestDifficultyDecrease();
        }
        // Adjust pacing
        this.adjustPacing(recentInteractions);
    }
    getRecentInteractions(count) {
        const interactions = this.sessionData.get('interactions') || [];
        return interactions.slice(-count);
    }
    calculateRecentAccuracy(interactions) {
        const exerciseInteractions = interactions.filter(i => i.interactionType === 'exercise');
        if (exerciseInteractions.length === 0)
            return this.metrics.accuracy;
        const correctCount = exerciseInteractions.filter(i => i.correct).length;
        return correctCount / exerciseInteractions.length;
    }
    suggestDifficultyIncrease() {
        console.log('🔥 User performing well - suggesting difficulty increase');
        this.dispatchAdaptationEvent('difficulty_increase', {
            reason: 'High performance detected',
            metrics: this.metrics
        });
    }
    suggestDifficultyDecrease() {
        console.log('💡 User struggling - suggesting difficulty decrease');
        this.dispatchAdaptationEvent('difficulty_decrease', {
            reason: 'Performance below threshold',
            metrics: this.metrics
        });
    }
    adjustPacing(interactions) {
        const avgTimePerExercise = this.calculateAverageTime(interactions);
        if (avgTimePerExercise < this.rules.pacing.fastLearnerThreshold) {
            this.profile.preferredPace = 'fast';
            this.dispatchAdaptationEvent('pace_increase', { reason: 'Fast completion times' });
        }
        else if (avgTimePerExercise > this.rules.pacing.slowLearnerThreshold) {
            this.profile.preferredPace = 'slow';
            this.dispatchAdaptationEvent('pace_decrease', { reason: 'Slow completion times' });
        }
        this.saveProfile();
    }
    calculateAverageTime(interactions) {
        const exerciseInteractions = interactions.filter(i => i.interactionType === 'exercise' && i.timeSpent);
        if (exerciseInteractions.length === 0)
            return 30000; // Default 30 seconds
        const totalTime = exerciseInteractions.reduce((sum, i) => sum + i.timeSpent, 0);
        return totalTime / exerciseInteractions.length;
    }
    generateContentRecommendations() {
        const recommendations = [];
        // Recommend review content for weak areas
        this.profile.weakAreas.forEach(area => {
            recommendations.push({
                lessonId: `review_${area}`,
                title: `Review: ${area.charAt(0).toUpperCase() + area.slice(1)}`,
                difficulty: 'easy',
                estimatedDuration: 15,
                topics: [area],
                reason: 'Reinforcement needed',
                priority: 9
            });
        });
        // Recommend new content based on strong areas
        this.profile.strongAreas.forEach(area => {
            recommendations.push({
                lessonId: `advanced_${area}`,
                title: `Advanced ${area.charAt(0).toUpperCase() + area.slice(1)}`,
                difficulty: 'hard',
                estimatedDuration: 25,
                topics: [area],
                reason: 'Ready for advanced content',
                priority: 7
            });
        });
        // Add level-appropriate general recommendations
        recommendations.push(...this.getLevelBasedRecommendations());
        // Sort by priority and return top 5
        return recommendations
            .sort((a, b) => b.priority - a.priority)
            .slice(0, 5);
    }
    getLevelBasedRecommendations() {
        const levelContent = {
            beginner: [
                { id: 'basic_greetings', title: 'Basic Greetings', topics: ['greetings', 'introductions'] },
                { id: 'numbers_colors', title: 'Numbers and Colors', topics: ['numbers', 'colors'] },
                { id: 'family_relations', title: 'Family Relations', topics: ['family', 'relationships'] }
            ],
            intermediate: [
                { id: 'past_tense', title: 'Past Tense Mastery', topics: ['grammar', 'past_tense'] },
                { id: 'restaurant_ordering', title: 'Restaurant Conversations', topics: ['food', 'ordering'] },
                { id: 'travel_phrases', title: 'Travel Essentials', topics: ['travel', 'transportation'] }
            ],
            advanced: [
                { id: 'business_spanish', title: 'Business Spanish', topics: ['business', 'professional'] },
                { id: 'subjunctive_mood', title: 'Subjunctive Mood', topics: ['grammar', 'advanced'] },
                { id: 'cultural_nuances', title: 'Cultural Nuances', topics: ['culture', 'idioms'] }
            ]
        };
        const content = levelContent[this.profile.level] || levelContent.beginner;
        return content.map(item => ({
            lessonId: item.id,
            title: item.title,
            difficulty: this.profile.level === 'beginner' ? 'easy' :
                this.profile.level === 'intermediate' ? 'medium' : 'hard',
            estimatedDuration: 20,
            topics: item.topics,
            reason: `Appropriate for ${this.profile.level} level`,
            priority: 6
        }));
    }
    getPersonalizedSettings() {
        return {
            exerciseDifficulty: this.metrics.accuracy > 0.8 ? 'hard' :
                this.metrics.accuracy > 0.6 ? 'medium' : 'easy',
            contentPacing: this.profile.preferredPace,
            reviewFrequency: this.profile.weakAreas.length > 2 ? 3 : 2, // Reviews per week
            recommendedStudyTime: this.calculateOptimalStudyTime()
        };
    }
    calculateOptimalStudyTime() {
        const baseTime = this.profile.dailyGoalMinutes;
        const performanceAdjustment = (this.metrics.accuracy - 0.5) * 20; // ±10 minutes
        const consistencyBonus = this.profile.currentStreak > 7 ? 5 : 0;
        return Math.max(10, Math.min(60, baseTime + performanceAdjustment + consistencyBonus));
    }
    dispatchAdaptationEvent(type, data) {
        const event = new CustomEvent('adaptive-learning-update', {
            detail: { type, data, profile: this.profile, metrics: this.metrics }
        });
        document.dispatchEvent(event);
    }
    loadProfile(userId) {
        const defaultProfile = {
            userId,
            level: 'beginner',
            preferredPace: 'normal',
            weakAreas: [],
            strongAreas: [],
            learningStyle: 'mixed',
            dailyGoalMinutes: 20,
            completedLessons: [],
            currentStreak: 0,
            lastActivity: new Date().toISOString()
        };
        try {
            const stored = localStorage.getItem(`adaptive_profile_${userId}`);
            if (stored) {
                return { ...defaultProfile, ...JSON.parse(stored) };
            }
        }
        catch (error) {
            console.warn('Failed to load learning profile:', error);
        }
        return defaultProfile;
    }
    saveProfile() {
        try {
            localStorage.setItem(`adaptive_profile_${this.profile.userId}`, JSON.stringify(this.profile));
        }
        catch (error) {
            console.warn('Failed to save learning profile:', error);
        }
    }
    loadMetrics(userId) {
        const defaultMetrics = {
            accuracy: 0.5,
            speed: 0.5,
            retention: 0.5,
            engagement: 0.5,
            consistency: 0.5
        };
        try {
            const stored = localStorage.getItem(`adaptive_metrics_${userId}`);
            if (stored) {
                return { ...defaultMetrics, ...JSON.parse(stored) };
            }
        }
        catch (error) {
            console.warn('Failed to load performance metrics:', error);
        }
        return defaultMetrics;
    }
    saveMetrics() {
        try {
            localStorage.setItem(`adaptive_metrics_${this.profile.userId}`, JSON.stringify(this.metrics));
        }
        catch (error) {
            console.warn('Failed to save performance metrics:', error);
        }
    }
    getDefaultRules() {
        return {
            difficultyAdjustment: {
                threshold: 0.7,
                increment: 0.1
            },
            contentSelection: {
                reviewRatio: 0.3,
                newContentRatio: 0.7
            },
            pacing: {
                fastLearnerThreshold: 20000, // 20 seconds
                slowLearnerThreshold: 60000 // 60 seconds
            }
        };
    }
    // Public API
    getProfile() {
        return { ...this.profile };
    }
    getMetrics() {
        return { ...this.metrics };
    }
    updateProfile(updates) {
        this.profile = { ...this.profile, ...updates };
        this.saveProfile();
    }
    resetProgress() {
        this.metrics = {
            accuracy: 0.5,
            speed: 0.5,
            retention: 0.5,
            engagement: 0.5,
            consistency: 0.5
        };
        this.profile.weakAreas = [];
        this.profile.strongAreas = [];
        this.profile.completedLessons = [];
        this.profile.currentStreak = 0;
        this.saveProfile();
        this.saveMetrics();
    }
}
// Make AdaptiveLearningSystem globally available
window.AdaptiveLearningSystem = AdaptiveLearningSystem;
// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🧠 Initializing Adaptive Learning System (TypeScript)...');
    // Get user ID from DOM or use default
    const userElement = document.querySelector('[data-user-id]');
    const userId = userElement?.dataset.userId || 'anonymous';
    window.adaptiveLearning = new AdaptiveLearningSystem(userId);
});
export default AdaptiveLearningSystem;
//# sourceMappingURL=adaptive-learning.js.map