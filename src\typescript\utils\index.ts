/**
 * Utility functions for the TalonTalk C.A.R.E. system
 */

import type {
    Debounced,
    Throttled,
    UtilityFunctions,
    BrowserInfo
} from '../types/common.types.js';

/**
 * Debounce function calls
 */
export function debounce<T extends (...args: any[]) => any>(
    fn: T,
    delay: number
): Debounced<T> {
    let timeoutId: ReturnType<typeof setTimeout>;
    let lastArgs: Parameters<T>;

    const debounced = ((...args: Parameters<T>) => {
        lastArgs = args;
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => fn(...args), delay);
    }) as Debounced<T>;

    debounced.cancel = () => {
        clearTimeout(timeoutId);
    };

    debounced.flush = () => {
        clearTimeout(timeoutId);
        return fn(...lastArgs);
    };

    return debounced;
}

/**
 * Throttle function calls
 */
export function throttle<T extends (...args: any[]) => any>(
    fn: T,
    limit: number
): Throttled<T> {
    let inThrottle: boolean;

    const throttled = ((...args: Parameters<T>) => {
        if (!inThrottle) {
            fn(...args);
            inThrottle = true;
            setTimeout(() => { inThrottle = false; }, limit);
        }
    }) as Throttled<T>;

    throttled.cancel = () => {
        inThrottle = false;
    };

    return throttled;
}

/**
 * Format date using a simple format string
 */
export function formatDate(date: Date, format: string): string {
    const map: Record<string, string> = {
        'YYYY': date.getFullYear().toString(),
        'MM': (date.getMonth() + 1).toString().padStart(2, '0'),
        'DD': date.getDate().toString().padStart(2, '0'),
        'HH': date.getHours().toString().padStart(2, '0'),
        'mm': date.getMinutes().toString().padStart(2, '0'),
        'ss': date.getSeconds().toString().padStart(2, '0')
    };

    return Object.keys(map).reduce((formatted, key) => {
        return formatted.replace(new RegExp(key, 'g'), map[key]);
    }, format);
}

/**
 * Format seconds into readable time string
 */
export function formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}

/**
 * Generate unique ID with optional prefix
 */
export function generateId(prefix = 'id'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Convert string to URL-friendly slug
 */
export function slugify(text: string): string {
    return text
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .trim()
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-'); // Remove duplicate hyphens
}

/**
 * Capitalize first letter of string
 */
export function capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
}

/**
 * Truncate text with optional suffix
 */
export function truncate(text: string, length: number, suffix = '...'): string {
    if (text.length <= length) return text;
    return text.substring(0, length - suffix.length) + suffix;
}

/**
 * Parse URL query string into object
 */
export function parseQuery(search: string): Record<string, string> {
    const params = new URLSearchParams(search);
    const result: Record<string, string> = {};

    for (const [key, value] of params.entries()) {
        result[key] = value;
    }

    return result;
}

/**
 * Build query string from object
 */
export function buildQuery(params: Record<string, string | number | boolean>): string {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value));
    });

    return searchParams.toString();
}

/**
 * Escape HTML entities
 */
export function escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Get browser information
 */
export function getBrowserInfo(): BrowserInfo {
    const userAgent = navigator.userAgent;
    const isOpera = Boolean((window as any).opr?.addons) || Boolean((window as any).opera) || userAgent.indexOf(' OPR/') >= 0;
    const isFirefox = typeof (window as any).InstallTrigger !== 'undefined';
    const isSafari = /constructor/i.test((window as any).HTMLElement) || ((p): boolean => p.toString() === '[object SafariRemoteNotification]')(!(window as any)['safari'] || (typeof (window as any).safari !== 'undefined' && (window as any).safari.pushNotification));
    const isIE = /*@cc_on!@*/false || Boolean((window as any).document.documentMode);
    const isEdge = !isIE && Boolean((window as any).StyleMedia);
    const isChrome = Boolean((window as any).chrome) && Boolean((window as any).chrome.webstore) && !isOpera && !isEdge;

    let browserName = 'Unknown';
    if (isChrome) browserName = 'Chrome';
    else if (isFirefox) browserName = 'Firefox';
    else if (isSafari) browserName = 'Safari';
    else if (isOpera) browserName = 'Opera';
    else if (isEdge) browserName = 'Edge';
    else if (isIE) browserName = 'Internet Explorer';

    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

    return {
        name: browserName,
        version: 'Unknown', // Would need more complex detection
        os: navigator.platform,
        mobile: isMobile,
        webgl: Boolean((window as any).WebGLRenderingContext),
        localStorage: typeof Storage !== 'undefined',
        sessionStorage: typeof sessionStorage !== 'undefined',
        indexedDB: Boolean((window as any).indexedDB),
        webAudio: Boolean((window as any).AudioContext || (window as any).webkitAudioContext),
        speechSynthesis: 'speechSynthesis' in window
    };
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
    if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;

    const cloned = {} as T;
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}

/**
 * Check if device has touch capabilities
 */
export function isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * Get viewport dimensions
 */
export function getViewportSize(): { width: number; height: number } {
    return {
        width: window.innerWidth || document.documentElement.clientWidth,
        height: window.innerHeight || document.documentElement.clientHeight
    };
}

/**
 * Smooth scroll to element
 */
export function scrollToElement(element: Element, offset = 0): void {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;

    window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
    });
}

/**
 * Wait for specified milliseconds
 */
export function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Get all utility functions as a single object
 */
export const utils: UtilityFunctions = {
    debounce,
    throttle,
    formatDate,
    formatTime,
    generateId,
    slugify,
    capitalize,
    truncate,
    parseQuery,
    buildQuery
};

/**
 * Export all utilities as default
 */
export default {
    debounce,
    throttle,
    formatDate,
    formatTime,
    generateId,
    slugify,
    capitalize,
    truncate,
    parseQuery,
    buildQuery,
    escapeHtml,
    getBrowserInfo,
    deepClone,
    isTouchDevice,
    getViewportSize,
    scrollToElement,
    sleep,
    utils
};
