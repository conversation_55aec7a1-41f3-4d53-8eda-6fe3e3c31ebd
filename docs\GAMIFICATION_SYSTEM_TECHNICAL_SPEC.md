# TalonTalk Gamification System - Technical Specification

## Table of Contents
1. [System Overview](#system-overview)
2. [Core Components](#core-components)
3. [Database Schema](#database-schema)
4. [API Endpoints](#api-endpoints)
5. [Gamification Mechanics](#gamification-mechanics)
6. [Implementation Details](#implementation-details)
7. [AI Integration](#ai-integration)
8. [Performance Considerations](#performance-considerations)
9. [Future Enhancements](#future-enhancements)

---

## System Overview

The TalonTalk gamification system is designed to create an engaging, habit-forming language learning experience through multiple interconnected reward mechanisms. The system leverages psychological principles of motivation, progression, and achievement to maintain user engagement.

### Design Principles
- **Progressive Difficulty**: Content and rewards scale with user ability
- **Multiple Reward Paths**: Different ways to earn progress (lessons, streaks, challenges)
- **Social Elements**: Leaderboards and community achievements
- **Personalization**: AI-driven adaptive content based on performance
- **Habit Formation**: Daily goals and streak mechanics

---

## Core Components

### 1. Experience Points (XP) System
- **Base Unit**: All progress measured in XP
- **Sources**: Lessons, flashcards, daily goals, challenges, streaks
- **Scaling**: Exponential difficulty curve for level progression

### 2. Level System
- **Level Calculation**: Based on cumulative XP
- **Level Benefits**: Unlock new content, features, and customization options
- **Visual Progression**: Progress bars and level-up animations

### 3. Badge System
- **Achievement Types**: Learning milestones, consistency, mastery, special events
- **Rarity Levels**: Common, Rare, Epic, Legendary
- **Display**: Dashboard showcase and profile achievements

### 4. Streak System
- **Daily Activity**: Consecutive days of engagement
- **Streak Protection**: Grace periods and streak freezes
- **Milestone Rewards**: Special badges and XP bonuses for long streaks

### 5. Challenge System
- **Weekly Challenges**: Themed learning objectives
- **Competitive Elements**: User vs. user or community challenges
- **Seasonal Events**: Special limited-time challenges

---

## Database Schema

### Current Models (Existing)

```python
# gamification/models.py

class Badge(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.ImageField(upload_to="badges/", blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

class Achievement(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    badge = models.ForeignKey(Badge, on_delete=models.CASCADE)
    achieved_at = models.DateTimeField(auto_now_add=True)

class Streak(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    current_streak = models.PositiveIntegerField(default=0)
    longest_streak = models.PositiveIntegerField(default=0)
    last_active = models.DateField(auto_now=True)

class Level(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    level = models.PositiveIntegerField(default=1)
    xp = models.PositiveIntegerField(default=0)
    updated_at = models.DateTimeField(auto_now=True)
```

### Enhanced Models (To Be Implemented)

```python
# Enhanced Badge System
class BadgeCategory(models.Model):
    name = models.CharField(max_length=50)  # Learning, Consistency, Mastery, Special
    color = models.CharField(max_length=7, default="#2C3E50")  # Hex color
    icon = models.CharField(max_length=10, default="🏆")  # Emoji icon

class Badge(models.Model):  # Enhanced existing model
    RARITY_CHOICES = [
        ('common', 'Common'),
        ('rare', 'Rare'),
        ('epic', 'Epic'),
        ('legendary', 'Legendary'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    category = models.ForeignKey(BadgeCategory, on_delete=models.CASCADE)
    rarity = models.CharField(max_length=20, choices=RARITY_CHOICES, default='common')
    icon = models.CharField(max_length=10, default="🏆")  # Emoji
    xp_reward = models.PositiveIntegerField(default=0)
    unlock_criteria = models.JSONField()  # Flexible criteria system
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

# XP Tracking and Sources
class XPTransaction(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    amount = models.IntegerField()  # Can be positive or negative
    source = models.CharField(max_length=50)  # lesson, flashcard, streak, etc.
    source_id = models.PositiveIntegerField(null=True, blank=True)  # Related object ID
    description = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)

# Daily Goals System
class DailyGoal(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    date = models.DateField()
    target_xp = models.PositiveIntegerField(default=50)
    earned_xp = models.PositiveIntegerField(default=0)
    target_lessons = models.PositiveIntegerField(default=1)
    completed_lessons = models.PositiveIntegerField(default=0)
    is_completed = models.BooleanField(default=False)
    
    class Meta:
        unique_together = ('user', 'date')

# Challenge System
class Challenge(models.Model):
    CHALLENGE_TYPES = [
        ('daily', 'Daily Challenge'),
        ('weekly', 'Weekly Challenge'),
        ('monthly', 'Monthly Challenge'),
        ('seasonal', 'Seasonal Event'),
    ]
    
    title = models.CharField(max_length=200)
    description = models.TextField()
    challenge_type = models.CharField(max_length=20, choices=CHALLENGE_TYPES)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    criteria = models.JSONField()  # Completion criteria
    xp_reward = models.PositiveIntegerField()
    badge_reward = models.ForeignKey(Badge, null=True, blank=True, on_delete=models.SET_NULL)
    is_active = models.BooleanField(default=True)

class ChallengeParticipation(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    challenge = models.ForeignKey(Challenge, on_delete=models.CASCADE)
    progress = models.JSONField(default=dict)  # Flexible progress tracking
    is_completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    joined_at = models.DateTimeField(auto_now_add=True)

# Leaderboard System
class Leaderboard(models.Model):
    LEADERBOARD_TYPES = [
        ('weekly_xp', 'Weekly XP'),
        ('monthly_xp', 'Monthly XP'),
        ('streak', 'Current Streak'),
        ('lessons', 'Lessons Completed'),
    ]
    
    name = models.CharField(max_length=100)
    leaderboard_type = models.CharField(max_length=20, choices=LEADERBOARD_TYPES)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)

class LeaderboardEntry(models.Model):
    leaderboard = models.ForeignKey(Leaderboard, on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    score = models.PositiveIntegerField()
    rank = models.PositiveIntegerField()
    updated_at = models.DateTimeField(auto_now=True)

# AI Flashcard Integration
class FlashcardSession(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    session_id = models.UUIDField(default=uuid.uuid4, unique=True)
    language = models.CharField(max_length=10)  # Target language
    difficulty_level = models.CharField(max_length=20, default='beginner')
    questions_answered = models.PositiveIntegerField(default=0)
    correct_answers = models.PositiveIntegerField(default=0)
    xp_earned = models.PositiveIntegerField(default=0)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

class FlashcardAnswer(models.Model):
    session = models.ForeignKey(FlashcardSession, related_name='answers', on_delete=models.CASCADE)
    question = models.TextField()
    user_answer = models.TextField()
    correct_answer = models.TextField()
    is_correct = models.BooleanField()
    similarity_score = models.FloatField(null=True, blank=True)  # AI similarity rating
    time_taken = models.PositiveIntegerField()  # Seconds
    answered_at = models.DateTimeField(auto_now_add=True)
```

---

## API Endpoints

### Current Endpoints (Django REST Framework)
```
/api/gamification/badges/          # Badge CRUD
/api/gamification/achievements/    # Achievement CRUD
/api/gamification/streaks/         # Streak CRUD
/api/gamification/levels/          # Level CRUD
```

### Enhanced API Endpoints (To Be Implemented)

```python
# Gamification Core
GET    /api/gamification/user-stats/                    # Complete user gamification stats
POST   /api/gamification/award-xp/                      # Award XP to user
GET    /api/gamification/leaderboard/{type}/            # Get leaderboard data

# Daily Goals
GET    /api/gamification/daily-goal/                    # Today's goal
PUT    /api/gamification/daily-goal/                    # Update daily goal
POST   /api/gamification/daily-goal/complete/          # Mark goal complete

# Challenges
GET    /api/gamification/challenges/active/             # Active challenges
POST   /api/gamification/challenges/{id}/join/         # Join challenge
GET    /api/gamification/challenges/{id}/progress/     # Challenge progress

# Flashcards (Missing - Critical Issue)
POST   /api/gamification/flashcard/generate/           # Generate AI flashcard
POST   /api/gamification/flashcard/answer/             # Submit flashcard answer
GET    /api/gamification/flashcard/session/{id}/       # Get session data

# Achievement System
POST   /api/gamification/check-achievements/           # Check for new achievements
GET    /api/gamification/achievements/recent/          # Recent achievements

# Analytics
GET    /api/gamification/analytics/progress/           # Learning progress analytics
GET    /api/gamification/analytics/streaks/            # Streak analytics
```

---

## Gamification Mechanics

### XP Calculation System

```python
# XP Sources and Values
XP_VALUES = {
    'lesson_complete': 25,
    'flashcard_correct': 5,
    'flashcard_session_complete': 15,
    'daily_goal_complete': 30,
    'streak_day': 10,  # Per day in streak
    'challenge_complete': 50,
    'perfect_lesson': 40,  # 100% accuracy
    'speed_bonus': 5,     # Fast completion
}

# Level Progression Formula
def calculate_level(total_xp):
    """
    Exponential progression: Level = floor(sqrt(total_xp / 100))
    Level 1: 0-99 XP
    Level 2: 100-399 XP  
    Level 3: 400-899 XP
    Level 4: 900-1599 XP
    Level 5: 1600-2499 XP
    """
    return int((total_xp / 100) ** 0.5) + 1

def xp_for_level(level):
    """Calculate XP required for a specific level"""
    return (level - 1) ** 2 * 100
```

### Badge Achievement Criteria

```python
# Example Badge Criteria (JSON format)
BADGE_CRITERIA = {
    "first_lesson": {
        "type": "lesson_count",
        "value": 1,
        "description": "Complete your first lesson"
    },
    "streak_master": {
        "type": "streak_length",
        "value": 30,
        "description": "Maintain a 30-day learning streak"
    },
    "flashcard_ace": {
        "type": "flashcard_accuracy",
        "value": 0.9,
        "sessions": 10,
        "description": "90% accuracy over 10 flashcard sessions"
    },
    "speed_learner": {
        "type": "lesson_time",
        "max_time": 300,  # 5 minutes
        "count": 5,
        "description": "Complete 5 lessons in under 5 minutes each"
    }
}
```

### Streak Mechanics

```python
class StreakManager:
    @staticmethod
    def update_streak(user):
        """Update user's streak based on activity"""
        streak, created = Streak.objects.get_or_create(user=user)
        today = timezone.now().date()
        
        if streak.last_active == today:
            return streak  # Already updated today
        
        if streak.last_active == today - timedelta(days=1):
            # Consecutive day - extend streak
            streak.current_streak += 1
            streak.longest_streak = max(streak.longest_streak, streak.current_streak)
        elif streak.last_active < today - timedelta(days=1):
            # Streak broken - reset
            streak.current_streak = 1
        
        streak.last_active = today
        streak.save()
        
        # Award streak XP
        xp_amount = XP_VALUES['streak_day'] * streak.current_streak
        award_xp(user, xp_amount, 'streak', description=f'{streak.current_streak}-day streak')
        
        return streak
```

---

## Implementation Details

### Service Classes

```python
# gamification/services.py

class GamificationService:
    @staticmethod
    def award_xp(user, amount, source, source_id=None, description=""):
        """Award XP and check for level ups and achievements"""
        # Create XP transaction
        transaction = XPTransaction.objects.create(
            user=user,
            amount=amount,
            source=source,
            source_id=source_id,
            description=description
        )
        
        # Update user level
        level, created = Level.objects.get_or_create(user=user)
        old_level = level.level
        level.xp += amount
        level.level = calculate_level(level.xp)
        level.save()
        
        # Check for level up
        if level.level > old_level:
            LevelUpService.handle_level_up(user, old_level, level.level)
        
        # Check for new achievements
        AchievementService.check_achievements(user)
        
        return transaction

class AchievementService:
    @staticmethod
    def check_achievements(user):
        """Check and award new achievements for user"""
        available_badges = Badge.objects.filter(is_active=True)
        user_achievements = Achievement.objects.filter(user=user).values_list('badge_id', flat=True)
        
        new_achievements = []
        
        for badge in available_badges:
            if badge.id not in user_achievements:
                if AchievementService._check_criteria(user, badge.unlock_criteria):
                    achievement = Achievement.objects.create(user=user, badge=badge)
                    new_achievements.append(achievement)
                    
                    # Award badge XP
                    if badge.xp_reward > 0:
                        GamificationService.award_xp(
                            user, 
                            badge.xp_reward, 
                            'badge', 
                            badge.id,
                            f'Earned badge: {badge.name}'
                        )
        
        return new_achievements

class FlashcardService:
    @staticmethod
    def generate_flashcard(user, language, difficulty='auto'):
        """Generate AI-powered flashcard question"""
        from ai_services.llm_flashcards import LLMFlashcardService, FlashcardRequest
        
        # Auto-adjust difficulty based on user performance
        if difficulty == 'auto':
            difficulty = FlashcardService._calculate_difficulty(user)
        
        # Create flashcard request
        request = FlashcardRequest(
            target_language=language,
            native_language=user.profile.native_language,
            difficulty_level=difficulty,
            user_level=user.level.level,
            focus_areas=FlashcardService._get_user_focus_areas(user)
        )
        
        # Generate using AI service
        llm_service = LLMFlashcardService()
        response = llm_service.generate_flashcard(request)
        
        # Create session if new
        session = FlashcardSession.objects.create(
            user=user,
            language=language,
            difficulty_level=difficulty
        )
        
        return {
            'session_id': session.session_id,
            'question': response.question,
            'question_type': response.question_type,
            'options': response.options,
            'hint': response.hint,
            'correct_answer': response.correct_answer,
            'explanation': response.explanation
        }
```

### Signal Handlers

```python
# gamification/signals.py

from django.db.models.signals import post_save
from django.dispatch import receiver
from lessons.models import UserLessonProgress

@receiver(post_save, sender=UserLessonProgress)
def lesson_completed_handler(sender, instance, created, **kwargs):
    """Handle lesson completion events"""
    if instance.completed:
        # Award lesson XP
        xp_amount = XP_VALUES['lesson_complete']
        if instance.xp_earned == 0:  # Prevent double-awarding
            # Check for perfect score bonus
            if hasattr(instance, 'accuracy') and instance.accuracy >= 1.0:
                xp_amount += XP_VALUES['perfect_lesson']
            
            GamificationService.award_xp(
                instance.user,
                xp_amount,
                'lesson',
                instance.lesson.id,
                f'Completed lesson: {instance.lesson.title}'
            )
            
            instance.xp_earned = xp_amount
            instance.save()
        
        # Update streak
        StreakManager.update_streak(instance.user)
        
        # Update daily goal
        DailyGoalService.update_progress(instance.user, lessons=1)
```

---

## AI Integration

### Flashcard Generation API

```python
# gamification/views.py (Missing Implementation)

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
import json

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def generate_flashcard(request):
    """Generate AI flashcard for user"""
    try:
        user = request.user
        language = request.data.get('language', user.profile.target_language)
        difficulty = request.data.get('difficulty', 'auto')
        
        flashcard_data = FlashcardService.generate_flashcard(user, language, difficulty)
        
        return Response({
            'success': True,
            'flashcard': flashcard_data
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def submit_flashcard_answer(request):
    """Submit and grade flashcard answer"""
    try:
        session_id = request.data.get('session_id')
        user_answer = request.data.get('user_answer')
        correct_answer = request.data.get('correct_answer')
        time_taken = request.data.get('time_taken', 0)
        
        # Get session
        session = FlashcardSession.objects.get(session_id=session_id, user=request.user)
        
        # Grade answer (implement AI similarity scoring)
        is_correct, similarity = FlashcardService.grade_answer(user_answer, correct_answer)
        
        # Create answer record
        answer = FlashcardAnswer.objects.create(
            session=session,
            question=request.data.get('question'),
            user_answer=user_answer,
            correct_answer=correct_answer,
            is_correct=is_correct,
            similarity_score=similarity,
            time_taken=time_taken
        )
        
        # Update session stats
        session.questions_answered += 1
        if is_correct:
            session.correct_answers += 1
        
        # Award XP for correct answers
        if is_correct:
            xp_earned = XP_VALUES['flashcard_correct']
            # Speed bonus
            if time_taken < 10:  # Under 10 seconds
                xp_earned += XP_VALUES['speed_bonus']
            
            GamificationService.award_xp(
                request.user,
                xp_earned,
                'flashcard',
                session.id,
                'Correct flashcard answer'
            )
            session.xp_earned += xp_earned
        
        session.save()
        
        return Response({
            'success': True,
            'is_correct': is_correct,
            'similarity_score': similarity,
            'xp_earned': session.xp_earned
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)
```

### URL Configuration (Missing)

```python
# gamification/urls.py (Enhanced)

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    BadgeViewSet, AchievementViewSet, StreakViewSet, LevelViewSet,
    generate_flashcard, submit_flashcard_answer, user_stats, daily_goal
)

router = DefaultRouter()
router.register(r"badges", BadgeViewSet)
router.register(r"achievements", AchievementViewSet)
router.register(r"streaks", StreakViewSet)
router.register(r"levels", LevelViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('flashcard/', generate_flashcard, name='generate_flashcard'),
    path('flashcard/answer/', submit_flashcard_answer, name='submit_flashcard_answer'),
    path('user-stats/', user_stats, name='user_stats'),
    path('daily-goal/', daily_goal, name='daily_goal'),
]
```

---

## Performance Considerations

### Caching Strategy
```python
# Cache frequently accessed gamification data
from django.core.cache import cache

class CachedGamificationService:
    @staticmethod
    def get_user_stats(user_id):
        cache_key = f'user_gamification_stats_{user_id}'
        stats = cache.get(cache_key)
        
        if not stats:
            stats = GamificationService.calculate_user_stats(user_id)
            cache.set(cache_key, stats, timeout=300)  # 5 minutes
        
        return stats
    
    @staticmethod
    def invalidate_user_cache(user_id):
        cache_key = f'user_gamification_stats_{user_id}'
        cache.delete(cache_key)
```

### Database Optimization
- Index on user_id for all gamification tables
- Periodic cleanup of old XP transactions
- Leaderboard materialized views for performance
- Batch processing for achievement checks

---

## Future Enhancements

### Phase 1: Core System (Current Priority)
- [ ] Fix missing flashcard API endpoints
- [ ] Implement basic badge system
- [ ] Daily goal tracking
- [ ] XP transaction logging

### Phase 2: Social Features
- [ ] Leaderboards
- [ ] Friend system
- [ ] Challenge competitions
- [ ] Achievement sharing

### Phase 3: Advanced AI
- [ ] Adaptive difficulty adjustment
- [ ] Personalized challenge generation
- [ ] Predictive engagement modeling
- [ ] Smart notification timing

### Phase 4: Analytics & Insights
- [ ] Learning analytics dashboard
- [ ] Progress prediction
- [ ] Engagement optimization
- [ ] A/B testing framework

---

## Critical Issues to Address

### 1. Missing Flashcard API (HIGH PRIORITY)
- Dashboard calls `/gamification/flashcard/` which doesn't exist
- Need to implement `generate_flashcard` and `submit_flashcard_answer` views
- JavaScript expects specific response format

### 2. Achievement System Logic (MEDIUM PRIORITY)
- No automatic achievement checking
- Badge criteria system not implemented
- Missing signal handlers for events

### 3. Streak System Logic (MEDIUM PRIORITY)
- Manual streak updates only
- No automated daily processing
- Missing streak protection features

### 4. Daily Goals (LOW PRIORITY)
- Basic tracking exists but no goal setting
- No progress monitoring
- Missing completion rewards

---

## Implementation Priority

1. **Fix Flashcard API** - Critical for current dashboard functionality
2. **Implement XP Service** - Core gamification mechanic
3. **Add Achievement Logic** - User motivation and progression
4. **Create Daily Goals** - Habit formation
5. **Build Leaderboards** - Social engagement
6. **Add Challenge System** - Long-term engagement

This technical specification provides a complete roadmap for implementing a comprehensive gamification system that will significantly enhance user engagement and learning outcomes in TalonTalk.
