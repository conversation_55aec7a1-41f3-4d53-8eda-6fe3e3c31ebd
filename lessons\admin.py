"""
Admin interface for Lessons app with quality monitoring
"""

from django.contrib import admin
from django.utils.html import format_html

from .models import Lesson, ContentItem, UserLearningProfile
from .advanced_quality_engine import AdvancedQualityEngine


class ContentItemAdmin(admin.ModelAdmin):
    list_display = [
        "question_text",
        "type",
        "language",
        "difficulty",
        "created_at",
        "quality_status",
    ]
    list_filter = ["type", "language", "difficulty", "created_at"]
    search_fields = ["question_text", "explanation_text"]
    readonly_fields = ["created_at", "updated_at", "quality_status"]

    def quality_status(self, obj):
        """Show quality status with color coding"""
        try:
            quality_engine = AdvancedQualityEngine()
            content_dict = {
                "id": str(obj.id),
                "question": obj.question_text,
                "content_type": obj.type,
                "language": obj.language,
            }

            # Add optional fields if they exist
            if hasattr(obj, "explanation_text") and obj.explanation_text:
                content_dict["explanation"] = obj.explanation_text

            report = quality_engine.validate_content(content_dict, obj.language)
            score = report.overall_score
            level = report.quality_level.value

            color_map = {
                "excellent": "green",
                "good": "blue",
                "acceptable": "orange",
                "poor": "red",
                "rejected": "darkred",
            }

            color = color_map.get(level, "gray")
            return format_html(
                '<span style="color: {}; font-weight: bold;">{} ({}%)</span>',
                color,
                level.title(),
                int(score),
            )
        except Exception:
            return format_html('<span style="color: gray;">Unknown</span>')

    quality_status.short_description = "Quality Status"


class LessonAdmin(admin.ModelAdmin):
    list_display = [
        "title",
        "difficulty_level",
        "target_language",
        "is_active",
        "created_at",
    ]
    list_filter = ["difficulty_level", "target_language", "is_active", "created_at"]
    search_fields = ["title", "description"]
    readonly_fields = ["created_at", "updated_at"]


class UserLearningProfileAdmin(admin.ModelAdmin):
    list_display = [
        "user",
        "target_language",
        "current_cefr_level",
        "total_xp",
        "current_streak",
    ]
    list_filter = ["target_language", "current_cefr_level", "skill_level"]
    search_fields = ["user__username", "user__email"]
    readonly_fields = ["created_at", "updated_at"]


# Register models
admin.site.register(Lesson, LessonAdmin)
admin.site.register(ContentItem, ContentItemAdmin)
admin.site.register(UserLearningProfile, UserLearningProfileAdmin)

# Customize admin site
admin.site.site_header = "TalonTalk Administration"
admin.site.site_title = "TalonTalk Admin"
admin.site.index_title = "Welcome to TalonTalk Administration"
