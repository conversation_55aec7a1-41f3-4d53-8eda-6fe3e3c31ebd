#!/usr/bin/env python
"""
Simple Phase 2 verification test
"""

import os
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()

print("🎯 PHASE 2: SIMPLIFIED VERIFICATION")
print("=" * 50)

try:
    from lessons.services import content_generator, content_pipeline
    from lessons.models import ContentItem
    from users.models import User  # Correct user model

    print("✅ All services imported successfully!")

    # Test 1: Content Generation (already working)
    print("\n📝 Test 1: Content Generation")
    print("-" * 30)

    result = content_generator.generate_care_content_batch(
        language="spanish",
        difficulty_level=1,
        content_types=["flashcard"],
        batch_size=1,
    )

    print(f'✅ Content generation: {result.get("success", False)}')
    if result.get("content_items"):
        item = result["content_items"][0]
        print(f"   Generated: {item.question_text[:40]}...")

    # Test 2: Spaced Repetition (with correct user model)
    print("\n📊 Test 2: Spaced Repetition")
    print("-" * 30)

    try:
        from lessons.services.spaced_repetition import spaced_repetition

        # Create test user with correct model
        test_user, created = User.objects.get_or_create(
            username="test_sr_user", defaults={"email": "<EMAIL>"}
        )

        content_item = ContentItem.objects.filter(language="spanish").first()
        if content_item:
            next_review, easiness = spaced_repetition.calculate_next_review(
                user_id=test_user.id, content_item=content_item, quality=4
            )
            print(f"✅ Spaced repetition calculation successful")
            print(f'   Next review: {next_review.strftime("%Y-%m-%d")}')
            print(f"   Easiness: {easiness:.2f}")
        else:
            print("⚠️ No content items for testing")

    except Exception as e:
        print(f"❌ Spaced repetition error: {e}")

    # Test 3: Background Tasks
    print("\n🔄 Test 3: Background Task System")
    print("-" * 30)

    try:
        from lessons.services.background_tasks import background_tasks
        import django_rq

        # Check Redis connection
        try:
            redis_conn = django_rq.get_connection()
            redis_conn.ping()
            print("✅ Redis connection: ACTIVE")

            # Test queue lengths
            queues = ["content_high", "content_normal", "content_low"]
            for queue_name in queues:
                queue = django_rq.get_queue(queue_name)
                print(f"   {queue_name}: {len(queue)} jobs")

        except Exception as e:
            print(f"⚠️ Redis connection: {e}")

    except Exception as e:
        print(f"❌ Background task error: {e}")

    # Test 4: Database State
    print("\n💾 Test 4: Database State")
    print("-" * 30)

    total_content = ContentItem.objects.count()
    spanish_content = ContentItem.objects.filter(language="spanish").count()
    users_count = User.objects.count()

    print(f"✅ Database statistics:")
    print(f"   Total content items: {total_content}")
    print(f"   Spanish content: {spanish_content}")
    print(f"   Total users: {users_count}")

    print("\n🎉 PHASE 2 STATUS: OPERATIONAL")
    print("🚀 Ready for Phase 3!")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback

    traceback.print_exc()
