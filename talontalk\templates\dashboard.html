{% extends 'base.html' %}
{% load static %}

{% block navigation %}
<!-- Single Unified Header: Logo + Dashboard Title + Navigation + Gamification -->
<header class="bg-talon-blue border-b-4 border-falcon-yellow shadow-lg">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
    <div class="flex items-center justify-between">
      <!-- Left: Logo and Dashboard Title -->
      <div class="flex items-center gap-6">
        <a href="{% url 'landing' %}" class="text-2xl font-bold text-white flex items-center hover:text-falcon-yellow transition-colors">
          <span class="text-2xl mr-2">🦅</span> TalonTalk
        </a>
        <div class="hidden md:block w-px h-8 bg-white/30"></div>
        <h1 class="text-white text-xl font-semibold tracking-wide">{{ profile.target_language|title }} Learning Dashboard</h1>
      </div>
      
      <!-- Right: Navigation Links + Gamification Badges -->
      <div class="flex items-center gap-6">
        <!-- Navigation Links -->
        <div class="flex items-center gap-4 text-white">
          <a href="{% url 'dashboard' %}" class="hover:text-falcon-yellow transition-colors px-2">Dashboard</a>
          <a href="#" onclick="openSettingsModal()" class="hover:text-falcon-yellow transition-colors px-2">Settings</a>
          <a href="{% url 'account_logout' %}" class="hover:text-falcon-yellow transition-colors px-2">Logout</a>
          <span class="text-white/90 px-2">Welcome, {{ user.first_name|default:user.username }}!</span>
        </div>
        
        <!-- Gamification Badges integrated into header -->
        <div class="flex items-center gap-3">
          <div class="flex items-center gap-2 bg-falcon-yellow text-talon-blue px-4 py-2 rounded-xl shadow font-bold">
            <span class="text-lg">🏆</span>
            <span>Level {{ level.level }}</span>
          </div>
          <div class="flex items-center gap-2 bg-white/20 text-white px-4 py-2 rounded-xl shadow font-bold">
            <span class="text-lg">🔥</span>
            <span>{{ streak.current_streak }}-day Streak</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
{% endblock %}

{% block content %}

<!-- Main Content Grid -->
<main class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content (Left 2/3) -->
    <section class="lg:col-span-2 space-y-6">
      <!-- Enhanced Learning Overview Card -->
      <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-talon-blue to-blue-600 rounded-lg flex items-center justify-center">
            <span class="text-white text-xl">📈</span>
          </div>
          <h2 class="text-xl font-bold text-talon-blue">Your Learning Journey</h2>
        </div>
        
        <!-- Key Progress Metrics -->
        <div class="grid md:grid-cols-4 gap-4 mb-6">
          <div class="text-center p-4 bg-blue-50 rounded-xl border border-blue-100">
            <div class="text-2xl font-bold text-talon-blue mb-1">{{ completed_lessons }}</div>
            <div class="text-sm font-medium text-talon-blue flex items-center justify-center gap-1">
              <span class="text-talon-blue text-lg">📚</span>Lessons
            </div>
          </div>
          <div class="text-center p-4 bg-yellow-50 rounded-xl border border-yellow-100">
            <div class="text-2xl font-bold text-talon-blue mb-1">{{ profile.xp }}</div>
            <div class="text-sm font-medium text-talon-blue flex items-center justify-center gap-1">
              <span style="color: #FFC300;" class="text-lg">⚡</span>Total XP
            </div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-xl border border-purple-100">
            <div class="text-2xl font-bold text-talon-blue mb-1">{{ achievements.count }}</div>
            <div class="text-sm font-medium text-talon-blue flex items-center justify-center gap-1">
              <span class="text-talon-blue text-lg">🏅</span>Badges
            </div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-xl border border-green-100">
            <div class="text-2xl font-bold text-green-700 mb-1">{{ words_learned }}</div>
            <div class="text-sm font-medium text-talon-blue flex items-center justify-center gap-1">
              <span class="text-talon-blue text-lg">📖</span>Words
            </div>
          </div>
        </div>
        
        <!-- Level Progress Bar -->
        <div class="bg-gray-50 rounded-xl p-4">
          <div class="flex justify-between items-center text-sm font-medium text-talon-blue mb-3">
            <span>Level {{ level.level }} Progress</span>
            <span>{{ level.xp }}/{{ xp_for_next_level }} XP</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-4 shadow-inner">
            <div class="bg-falcon-yellow h-4 rounded-full transition-all duration-500 shadow-lg" style="width: {{ progress_percentage }}%"></div>
          </div>
          <div class="text-xs text-gray-600 mt-2 text-center">
            {{ xp_to_next_level }} XP to Level {{ level.level|add:"1" }}
          </div>
        </div>
      </div>
      <!-- AI Flashcard Practice Card -->
      <div class="bg-talon-blue rounded-2xl shadow-xl p-6 text-white relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-falcon-yellow opacity-10 rounded-full -mr-16 -mt-16"></div>
        <div class="relative">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center space-x-3 mb-3">
                <span class="text-3xl">🧠</span>
                <h2 class="text-2xl font-bold">AI Flashcard Practice</h2>
              </div>
              <p class="text-blue-100 text-lg mb-2">Master {{ profile.target_language|title }} vocabulary with AI-powered flashcards</p>
              <p class="text-blue-200 text-sm">Personalized questions • Instant feedback • Smart progression</p>
            </div>
            <div class="flex flex-col space-y-3">
              <a href="{% url 'flashcard_practice' %}" class="bg-falcon-yellow hover:bg-yellow-400 text-talon-blue px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200 text-center block">
                Start Practice
              </a>
            </div>
          </div>
        </div>
      </div>
      

      
      <!-- Continue Learning Lesson List -->
      <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-10 h-10 rounded-lg flex items-center justify-center" style="background: linear-gradient(135deg, #10b981, #059669);">
            <span class="text-white text-xl">📚</span>
          </div>
          <h2 class="text-xl font-bold" style="color: #2C3E50;">Continue Learning</h2>
        </div>
        <div class="space-y-4">
          {% for lesson in lessons %}
          <div class="border-2 border-gray-100 rounded-xl p-6 transition-all duration-200 bg-white group cursor-pointer hover:-translate-y-1 hover:shadow-2xl hover:border-talon-blue" style="border-color: #e5e7eb;">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="w-14 h-14 rounded-xl flex items-center justify-center shadow-lg" style="background: linear-gradient(135deg, #60a5fa, #2C3E50);">
                  <span class="text-white font-bold text-lg">{{ lesson.order }}</span>
                </div>
                <div>
                  <h3 class="font-bold text-lg" style="color: #2C3E50;">{{ lesson.title }}</h3>
                  <p class="text-gray-600 mb-2">{{ lesson.description }}</p>
                  <div class="flex items-center space-x-4 text-sm">
                    <span class="px-3 py-1 rounded-full font-medium transition-all duration-200 group-hover:bg-talon-blue group-hover:text-white" style="background-color: #DEE2E6; color: #2C3E50;">
                      📝 {{ lesson.vocabularies.count }} vocabulary words
                    </span>
                    {% if lesson.id in completed_lesson_ids %}
                      <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">
                        ✅ Completed
                      </span>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                {% if lesson.id in completed_lesson_ids %}
                  <div class="text-green-600 font-bold text-lg">✓</div>
                {% else %}
                  <a href="{% url 'lesson_detail' lesson.id %}" class="text-white px-6 py-3 rounded-xl font-bold shadow-lg transform hover:scale-105 transition-all duration-200" style="background-color: #2C3E50;" onmouseover="this.style.backgroundColor='#1e40af'" onmouseout="this.style.backgroundColor='#2C3E50'">
                    Start Lesson →
                  </a>
                {% endif %}
              </div>
            </div>
          </div>
          {% empty %}
          <div class="text-center py-12 bg-gray-50 rounded-xl">
            <span class="text-6xl mb-4 block">📚</span>
            <h3 class="text-xl font-bold mb-2" style="color: #2C3E50;">Lessons Coming Soon!</h3>
            <p class="text-gray-600">We're preparing amazing content for you. Check back soon!</p>
          </div>
          {% endfor %}
        </div>
      </div>
    </section>
    <!-- Sidebar (Right 1/3) -->
    <aside class="space-y-6">
      <!-- Enhanced Daily Goal -->
      <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-falcon-yellow to-amber-500 rounded-lg flex items-center justify-center">
            <span class="text-talon-blue text-lg">🎯</span>
          </div>
          <h3 class="text-lg font-bold text-talon-blue">Today's Goal</h3>
        </div>
        <div class="text-center">
          <div class="w-24 h-24 mx-auto bg-falcon-yellow rounded-full flex items-center justify-center text-talon-blue text-2xl font-bold mb-4 shadow-lg border-4 border-falcon-yellow/30">
            15
          </div>
          <p class="font-medium text-talon-blue mb-1">Study for 15 minutes</p>
          <p class="text-sm text-gray-600 mb-4">You're doing great! Keep it up! 💪</p>
          <div class="bg-gray-100 rounded-full h-3 mb-2">
            <div class="bg-falcon-yellow h-3 rounded-full shadow-sm" style="width: 60%"></div>
          </div>
          <p class="text-xs font-medium" style="color: #495057;">9 minutes completed today</p>
        </div>
      </div>
      
      <!-- Smart Learning Insights Card -->
      {% if user.is_authenticated %}
      <div id="adaptive-insights" class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <span class="text-white text-lg">🧠</span>
          </div>
          <h3 class="text-lg font-bold text-talon-blue">Smart Insights</h3>
        </div>
        
        <!-- Study Time Today -->
        <div class="bg-blue-50 rounded-lg p-3 mb-4 border border-blue-100">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-gray-700">⏱️ Study Time Today</span>
            <span class="font-bold text-blue-600">{{ minutes_studied }}min</span>
          </div>
        </div>
        
        <!-- Language Learning -->
        <div class="bg-talon-blue rounded-lg p-3 mb-4">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium text-white">🎯 Learning</span>
            <span class="font-bold text-white">{{ profile.target_language }}</span>
          </div>
        </div>
        
        <!-- Dynamic Content Section -->
        <div id="insights-content">
          <!-- Today's Performance (will be populated by JS) -->
          <div id="session-stats" class="bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg p-3 mb-4 border border-blue-100 hidden">
            <h4 class="font-semibold text-gray-700 mb-2 text-sm">📈 Today's Performance</h4>
            <div class="grid grid-cols-3 gap-2 text-center">
              <div>
                <div class="text-sm font-bold text-blue-600" data-progress="accuracy">--</div>
                <div class="text-xs text-gray-600">Accuracy</div>
              </div>
              <div>
                <div class="text-sm font-bold text-green-600" data-progress="streak">--</div>
                <div class="text-xs text-gray-600">Streak</div>
              </div>
              <div>
                <div class="text-sm font-bold text-purple-600" data-progress="questions">--</div>
                <div class="text-xs text-gray-600">Questions</div>
              </div>
            </div>
          </div>
          
          <!-- AI Recommendations (will be populated by JS) -->
          <div id="learning-recommendations" class="mb-4 hidden">
            <h4 class="font-semibold text-gray-700 mb-2 text-sm">💡 AI Suggestions</h4>
            <div id="recommendations-content" class="space-y-1 text-xs">
              <!-- Recommendations will be populated by JavaScript -->
            </div>
          </div>
          
          <!-- Quick Action -->
          <button onclick="loadPersonalizedContent()" class="w-full bg-gradient-to-r from-purple-500 to-blue-600 text-white px-3 py-2 rounded-lg font-medium hover:opacity-90 transition-all hover:scale-105 text-sm shadow">
            🎯 Get AI Recommendations
          </button>
        </div>
      </div>
      {% endif %}
      
      <div>
      </div>
      <!-- Enhanced Recent Achievements -->
      <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-lg flex items-center justify-center">
            <span class="text-talon-blue text-2xl">🏅</span>
          </div>
          <h3 class="text-lg font-bold text-talon-blue">Recent Achievements</h3>
        </div>
        {% if achievements %}
          <div class="space-y-4">
            {% for achievement in achievements %}
            <div class="flex items-center space-x-3 p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl border border-yellow-200">
              <div class="w-12 h-12 bg-gradient-to-br from-falcon-yellow to-amber-500 rounded-full flex items-center justify-center shadow-lg">
                <span class="text-talon-blue text-2xl">🏆</span>
              </div>
              <div>
                <div class="font-bold text-talon-blue">{{ achievement.badge.name }}</div>
                <div class="text-sm text-amber-700">{{ achievement.badge.description }}</div>
              </div>
            </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-6 bg-gray-50 rounded-xl">
            <span class="text-4xl mb-3 block">🎯</span>
            <p class="font-medium text-talon-blue mb-1">Start Learning!</p>
            <p class="text-sm text-gray-600">Complete lessons to earn your first badges!</p>
          </div>
        {% endif %}
      </div>
    </aside>
  </div>
</main>

<!-- Enhanced Flashcard Practice Modal -->
<div id="flashcardModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 flex items-center justify-center backdrop-blur-sm">
  <div class="bg-white rounded-2xl shadow-2xl max-w-3xl w-full mx-4 transform transition-all border-4 border-talon-blue">
    
    <!-- Enhanced Modal Header -->
    <div class="flex items-center justify-between p-6 bg-gradient-to-r from-talon-blue to-blue-600 text-white rounded-t-2xl">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-falcon-yellow rounded-lg flex items-center justify-center">
          <span class="text-talon-blue text-xl">🧠</span>
        </div>
        <div>
          <h3 class="text-xl font-bold">Flashcard Practice</h3>
          <p class="text-blue-100 text-sm">{{ profile.target_language|title }} vocabulary mastery</p>
        </div>
      </div>
      <button onclick="closeFlashcardModal()" class="text-blue-200 hover:text-white p-2 hover:bg-white/20 rounded-lg transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Flashcard Content -->
    <div id="flashcardContent" class="p-6">
      
      <!-- Loading State -->
      <div id="flashcardLoading" class="text-center py-16">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-talon-blue border-t-falcon-yellow mx-auto mb-6"></div>
        <p class="text-talon-blue font-medium text-lg">Generating your personalized flashcard...</p>
        <p class="text-gray-600 text-sm mt-2">Our AI is creating the perfect question for you!</p>
      </div>

      <!-- Flashcard Display -->
      <div id="flashcardDisplay" class="hidden">
        <!-- Enhanced Progress Bar -->
        <div class="mb-8">
          <div class="flex justify-between items-center text-sm font-medium text-talon-blue mb-3">
            <span>Progress</span>
            <span id="flashcardProgress" class="bg-blue-100 px-3 py-1 rounded-full">1/5</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
            <div id="flashcardProgressBar" class="bg-gradient-to-r from-talon-blue to-falcon-yellow h-3 rounded-full transition-all duration-500 shadow-lg" style="width: 20%"></div>
          </div>
        </div>

        <!-- Enhanced Question -->
        <div class="bg-gradient-to-r from-blue-50 via-purple-50 to-blue-50 rounded-xl p-8 mb-8 border-2 border-blue-100">
          <div class="flex items-center space-x-3 mb-4">
            <span class="text-2xl">❓</span>
            <h4 class="text-lg font-bold text-talon-blue">Question</h4>
          </div>
          <p id="flashcardQuestion" class="text-2xl font-medium text-talon-blue mb-3"></p>
          <div id="flashcardHint" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4 hidden">
            <div class="flex items-center space-x-2">
              <span class="text-xl">💡</span>
              <div>
                <div class="font-medium text-yellow-800">Hint:</div>
                <div class="text-yellow-700"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Answer Options (Multiple Choice) -->
        <div id="multipleChoiceOptions" class="space-y-3 mb-8 hidden">
          <div class="grid gap-3">
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option A
            </button>
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option B
            </button>
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option C
            </button>
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option D
            </button>
          </div>
        </div>

        <!-- Enhanced Text Input (Translation) -->
        <div id="textInputArea" class="mb-8 hidden">
          <label class="block text-sm font-bold text-talon-blue mb-3">Your Answer:</label>
          <input id="userAnswer" type="text" class="w-full p-4 border-2 border-gray-200 rounded-xl focus:border-talon-blue focus:outline-none text-lg" placeholder="Type your answer here...">
        </div>

        <!-- Enhanced Action Buttons -->
        <div class="flex justify-between items-center">
          <button id="showHintBtn" onclick="showHint()" class="flex items-center space-x-2 text-falcon-yellow hover:text-amber-500 font-bold transition-colors">
            <span class="text-xl">💡</span>
            <span>Show Hint</span>
          </button>
          <div class="space-x-4">
            <button id="submitAnswerBtn" onclick="submitAnswer()" class="bg-gradient-to-r from-talon-blue to-blue-600 hover:from-blue-600 hover:to-purple-600 text-white px-8 py-3 rounded-xl font-bold shadow-lg transform hover:scale-105 transition-all duration-200">
              Submit Answer
            </button>
            <button id="nextQuestionBtn" onclick="nextQuestion()" class="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-emerald-600 hover:to-green-700 text-white px-8 py-3 rounded-xl font-bold shadow-lg transform hover:scale-105 transition-all duration-200 hidden">
              Next Question →
            </button>
          </div>
        </div>

        <!-- Enhanced Feedback Area -->
        <div id="feedbackArea" class="mt-8 p-6 rounded-xl hidden">
          <div id="feedbackContent"></div>
          <div id="explanationContent" class="mt-4 text-sm text-gray-700 bg-gray-50 p-4 rounded-lg"></div>
        </div>
      </div>

      <!-- Enhanced Session Complete -->
      <div id="sessionComplete" class="text-center py-16 hidden">
        <div class="text-8xl mb-6">🎉</div>
        <h3 class="text-3xl font-bold text-talon-blue mb-3">Practice Complete!</h3>
        <p class="text-gray-600 text-lg mb-8">Outstanding work! You've completed this flashcard session.</p>
        <div id="sessionStats" class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 mb-8 border border-blue-200">
          <div class="grid grid-cols-3 gap-6 text-center">
            <div>
              <div id="totalAnswered" class="text-3xl font-bold text-talon-blue mb-1">5</div>
              <div class="text-sm font-medium text-blue-700">Answered</div>
            </div>
            <div>
              <div id="correctAnswers" class="text-3xl font-bold text-green-600 mb-1">4</div>
              <div class="text-sm font-medium text-green-700">Correct</div>
            </div>
            <div>
              <div id="accuracyRate" class="text-3xl font-bold text-purple-600 mb-1">80%</div>
              <div class="text-sm font-medium text-purple-700">Accuracy</div>
            </div>
          </div>
        </div>
        <button onclick="startNewSession()" class="bg-gradient-to-r from-talon-blue to-purple-600 hover:from-purple-600 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
          Practice Again
        </button>
      </div>

      <!-- Enhanced Tutorial Flow -->
      <div id="tutorialFlow" class="hidden">
        <!-- Tutorial Step 1: Welcome -->
        <div id="tutorialStep1" class="text-center py-12">
          <div class="text-8xl mb-8">👋</div>
          <h3 class="text-3xl font-bold text-talon-blue mb-4">Welcome to {{ profile.target_language|title }} Learning!</h3>
          <p class="text-gray-600 text-lg mb-2">Let's start with a quick story to introduce you to your first words.</p>
          <p class="text-blue-600 font-medium mb-8">This will take just 2 minutes! 🚀</p>
          <button onclick="showTutorialStory()" class="bg-gradient-to-r from-talon-blue to-purple-600 hover:from-purple-600 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
            Start Learning Journey
          </button>
        </div>

        <!-- Tutorial Step 2: Story Introduction -->
        <div id="tutorialStep2" class="hidden py-6">
          <div class="flex items-center space-x-3 mb-6">
            <span class="text-3xl">📖</span>
            <h3 class="text-2xl font-bold text-talon-blue">Your First {{ profile.target_language|title }} Story</h3>
          </div>
          <div class="bg-gradient-to-r from-blue-50 via-purple-50 to-blue-50 rounded-2xl p-8 mb-8 border-2 border-blue-100">
            <div class="space-y-6">
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Hola, me llamo María.</div>
                <div class="native-text text-gray-600 italic">Hello, my name is María.</div>
              </div>
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Buenos días. ¿Cómo estás?</div>
                <div class="native-text text-gray-600 italic">Good morning. How are you?</div>
              </div>
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Muy bien, gracias.</div>
                <div class="native-text text-gray-600 italic">Very well, thank you.</div>
              </div>
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Hasta luego.</div>
                <div class="native-text text-gray-600 italic">See you later.</div>
              </div>
            </div>
          </div>
          <div class="text-center">
            <p class="text-talon-blue font-medium text-lg mb-2">Congratulations! You just learned 8 important words! 🎉</p>
            <p class="text-gray-600 mb-6">Let's practice them now to make sure you remember.</p>
            <button onclick="startTutorialPractice()" class="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-emerald-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
              Practice These Words
            </button>
          </div>
        </div>

        <!-- Tutorial Step 3: Guided Practice -->
        <div id="tutorialStep3" class="hidden py-6">
          <div class="flex items-center space-x-3 mb-6">
            <span class="text-3xl">🎯</span>
            <h3 class="text-2xl font-bold text-talon-blue">Let's Practice!</h3>
          </div>
          <div class="bg-gradient-to-r from-yellow-50 to-amber-50 border-2 border-yellow-200 rounded-xl p-6 mb-8">
            <div class="flex items-center">
              <span class="text-3xl mr-4">💡</span>
              <div>
                <div class="font-bold text-yellow-800 text-lg">How it works:</div>
                <div class="text-yellow-700">Read the question, then click the correct answer. Don't worry about mistakes - they help you learn!</div>
              </div>
            </div>
          </div>
          
          <!-- Guided question -->
          <div class="bg-gradient-to-r from-blue-50 via-purple-50 to-blue-50 rounded-xl p-8 mb-8 border-2 border-blue-100">
            <h4 class="text-lg font-bold text-talon-blue mb-4">Question 1/3</h4>
            <p class="text-2xl font-medium text-talon-blue mb-6">What does "Hola" mean in English?</p>
            <div class="grid gap-4">
              <button class="tutorial-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium" data-answer="correct">
                Hello
              </button>
              <button class="tutorial-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium" data-answer="wrong">
                Goodbye
              </button>
              <button class="tutorial-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium" data-answer="wrong">
                Thank you
              </button>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<!-- Clarity/contrast fixes for main cards and text -->
<style>
  /* Ensure all card titles and important stats use brand colors and high contrast */
  .dashboard-card-title { color: #2C3E50; font-weight: bold; }
  .dashboard-card-subtitle { color: #495057; }
  .dashboard-xp { color: #FFC300; font-weight: bold; }
  .dashboard-badges { color: #B8860B; font-weight: bold; }
  .dashboard-progress-label { color: #2C3E50; }
  .dashboard-progress-bar { background: #FFC300; }
  .dashboard-section-bg { background: #F8FAFC; }
  .dashboard-card-bg { background: #fff; }
  .dashboard-card { box-shadow: 0 2px 12px 0 rgba(44,62,80,0.06); }
  
  /* Adaptive Learning UI Enhancements */
  .adaptive-recommendation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 16px;
    color: white;
    margin-bottom: 16px;
  }
  
  .performance-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
  }
  
  .performance-excellent { background: #10B981; color: white; }
  .performance-good { background: #F59E0B; color: white; }
  .performance-needs-work { background: #EF4444; color: white; }
</style>

<script>
// Enhanced Flashcard System with TalonTalk branding and Adaptive Learning
let currentFlashcard = null;
let currentQuestionIndex = 0;
let totalQuestions = 5;
let sessionStats = {
    totalAnswered: 0,
    correctAnswers: 0,
    partialCredit: 0
};
let isInTutorial = false;
let questionStartTime = null;

function startFlashcardPractice() {
    document.getElementById('flashcardModal').classList.remove('hidden');
    document.getElementById('flashcardLoading').classList.remove('hidden');
    document.getElementById('flashcardDisplay').classList.add('hidden');
    document.getElementById('sessionComplete').classList.add('hidden');
    document.getElementById('tutorialFlow').classList.add('hidden');
    
    // Reset session stats
    sessionStats = { totalAnswered: 0, correctAnswers: 0, partialCredit: 0 };
    currentQuestionIndex = 0;
    
    // Skip tutorial check and adaptive learning for faster start
    generateFlashcard();
}

function showTutorialFlow() {
    isInTutorial = true;
    document.getElementById('flashcardLoading').classList.add('hidden');
    document.getElementById('tutorialFlow').classList.remove('hidden');
    document.getElementById('tutorialStep1').classList.remove('hidden');
}

function showTutorialStory() {
    document.getElementById('tutorialStep1').classList.add('hidden');
    document.getElementById('tutorialStep2').classList.remove('hidden');
}

function startTutorialPractice() {
    document.getElementById('tutorialStep2').classList.add('hidden');
    document.getElementById('tutorialStep3').classList.remove('hidden');
    
    // Add click handlers for tutorial options
    document.querySelectorAll('.tutorial-option').forEach(option => {
        option.addEventListener('click', function() {
            const isCorrect = this.dataset.answer === 'correct';
            
            // Remove previous styling
            document.querySelectorAll('.tutorial-option').forEach(opt => {
                opt.classList.remove('border-green-500', 'bg-green-100', 'border-red-500', 'bg-red-100');
            });
            
            if (isCorrect) {
                this.classList.add('border-green-500', 'bg-green-100');
                setTimeout(() => {
                    localStorage.setItem('tutorialCompleted', 'true');
                    showTutorialComplete();
                }, 1500);
            } else {
                this.classList.add('border-red-500', 'bg-red-100');
                // Highlight correct answer
                document.querySelector('[data-answer="correct"]').classList.add('border-green-500', 'bg-green-100');
                setTimeout(() => {
                    localStorage.setItem('tutorialCompleted', 'true');
                    showTutorialComplete();
                }, 2000);
            }
        });
    });
}

function showTutorialComplete() {
    document.getElementById('tutorialFlow').classList.add('hidden');
    document.getElementById('flashcardLoading').classList.remove('hidden');
    isInTutorial = false;
    setTimeout(() => {
        generateFlashcard();
    }, 1000);
}

function resetTutorial() {
    localStorage.removeItem('tutorialCompleted');
    showNotification('Tutorial reset! Next practice session will show the tutorial again.', 'success');
}

function closeFlashcardModal() {
    document.getElementById('flashcardModal').classList.add('hidden');
}

function generateFlashcard() {
    console.log('DEBUG: Starting flashcard generation');
    document.getElementById('flashcardLoading').classList.remove('hidden');
    document.getElementById('flashcardDisplay').classList.add('hidden');
    
    // Get user difficulty level from profile or use default
    const userDifficulty = '{{ profile.skill_level|default:"beginner"|lower }}';
    
    // Vary question types based on difficulty and randomness for variety
    let questionType;
    if (userDifficulty === 'beginner') {
        // Beginners get more multiple choice (70% of the time)
        questionType = Math.random() < 0.7 ? 'multiple_choice' : 'fill_blank';
    } else if (userDifficulty === 'intermediate') {
        // Intermediate gets balanced mix (50/50)
        questionType = Math.random() < 0.5 ? 'multiple_choice' : 'fill_blank';
    } else {
        // Advanced gets more fill-in-blank (70% of the time)
        questionType = Math.random() < 0.3 ? 'multiple_choice' : 'fill_blank';
    }
    
    // Build URL with parameters for variety
    const params = new URLSearchParams({
        'difficulty': userDifficulty,
        'type': questionType,
        'language': '{{ profile.target_language|default:"spanish"|lower }}'
    });
    
    console.log('DEBUG: Making API call to /api/flashcard/ with params:', params.toString());
    fetch(`/api/flashcard/?${params.toString()}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => {
        console.log('DEBUG: API response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('DEBUG: API response data:', data);
        if (data.success) {
            currentFlashcard = data.flashcard;
            console.log('DEBUG: Current flashcard set:', currentFlashcard);
            displayFlashcard();
        } else {
            console.error('DEBUG: API returned error:', data.error);
            showNotification('Error generating flashcard. Please try again.', 'error');
            closeFlashcardModal();
        }
    })
    .catch(error => {
        console.error('DEBUG: API call failed:', error);
        showNotification('Error generating flashcard. Please try again.', 'error');
        closeFlashcardModal();
    });
}

function displayFlashcard() {
    document.getElementById('flashcardLoading').classList.add('hidden');
    document.getElementById('flashcardDisplay').classList.remove('hidden');
    
    // Update progress
    currentQuestionIndex++;
    document.getElementById('flashcardProgress').textContent = `${currentQuestionIndex}/${totalQuestions}`;
    document.getElementById('flashcardProgressBar').style.width = `${(currentQuestionIndex / totalQuestions) * 100}%`;
    
    // Display question
    document.getElementById('flashcardQuestion').textContent = currentFlashcard.question;
    
    // Hide hint initially
    document.getElementById('flashcardHint').classList.add('hidden');
    document.getElementById('showHintBtn').classList.remove('hidden');
    
    // Show appropriate input type
    if (currentFlashcard.question_type === 'multiple_choice') {
        console.log('DEBUG: Processing multiple choice question');
        console.log('DEBUG: Options received:', currentFlashcard.options);
        document.getElementById('multipleChoiceOptions').classList.remove('hidden');
        document.getElementById('textInputArea').classList.add('hidden');
        
        const options = document.querySelectorAll('.answer-option');
        console.log('DEBUG: Found answer-option elements:', options.length);
        options.forEach((option, index) => {
            if (currentFlashcard.options && currentFlashcard.options[index]) {
                console.log(`DEBUG: Setting option ${index} to:`, currentFlashcard.options[index]);
                option.textContent = currentFlashcard.options[index];
                option.style.display = 'block';
                option.onclick = () => selectMultipleChoice(option, currentFlashcard.options[index]);
                // Reset styling
                option.classList.remove('border-green-500', 'bg-green-100', 'border-red-500', 'bg-red-100', 'selected');
            } else {
                console.log(`DEBUG: Hiding option ${index} - no data available`);
                option.style.display = 'none';
            }
        });
    } else {
        document.getElementById('multipleChoiceOptions').classList.add('hidden');
        document.getElementById('textInputArea').classList.remove('hidden');
        document.getElementById('userAnswer').value = '';
        document.getElementById('userAnswer').focus();
    }
    
    // Reset buttons
    document.getElementById('submitAnswerBtn').classList.remove('hidden');
    document.getElementById('nextQuestionBtn').classList.add('hidden');
    document.getElementById('feedbackArea').classList.add('hidden');
}

let selectedAnswer = null;

function selectMultipleChoice(element, answer) {
    // Remove selection from all options
    document.querySelectorAll('.answer-option').forEach(opt => {
        opt.classList.remove('selected', 'border-talon-blue', 'bg-blue-100');
    });
    
    // Select this option
    element.classList.add('selected', 'border-talon-blue', 'bg-blue-100');
    selectedAnswer = answer;
}

function showHint() {
    const hintElement = document.getElementById('flashcardHint');
    hintElement.querySelector('.text-yellow-700').textContent = currentFlashcard.hint;
    hintElement.classList.remove('hidden');
    document.getElementById('showHintBtn').classList.add('hidden');
}

function submitAnswer() {
    let userAnswer;
    
    if (currentFlashcard.question_type === 'multiple_choice') {
        if (!selectedAnswer) {
            showNotification('Please select an answer first.', 'warning');
            return;
        }
        userAnswer = selectedAnswer;
    } else {
        userAnswer = document.getElementById('userAnswer').value.trim();
        if (!userAnswer) {
            showNotification('Please enter an answer first.', 'warning');
            return;
        }
    }
    
    // Enhanced instant feedback with Levenshtein distance
    const feedback = checkAnswerInstantly(userAnswer, currentFlashcard.correct_answer);
    displayFeedback(feedback);
    
    // Track with adaptive learning system
    trackAdaptiveAnswer(userAnswer, feedback.isCorrect || feedback.partialCredit);
    
    // Update session stats
    sessionStats.totalAnswered++;
    if (feedback.isCorrect) {
        sessionStats.correctAnswers++;
    } else if (feedback.partialCredit) {
        sessionStats.partialCredit++;
    }
    
    // Hide submit button, show next button
    document.getElementById('submitAnswerBtn').classList.add('hidden');
    
    if (currentQuestionIndex < totalQuestions) {
        document.getElementById('nextQuestionBtn').classList.remove('hidden');
    } else {
        setTimeout(() => {
            showSessionComplete();
        }, 2000);
    }
    
    // Send analytics in background (non-blocking)
    sendAnswerAnalytics(userAnswer, feedback);
}

function checkAnswerInstantly(userAnswer, correctAnswer) {
    const normalizedUser = userAnswer.toLowerCase().trim();
    const normalizedCorrect = correctAnswer.toLowerCase().trim();
    
    // Exact match
    if (normalizedUser === normalizedCorrect) {
        return {
            isCorrect: true,
            partialCredit: false,
            similarity: 1.0,
            message: "Perfect! Excellent work! 🎉"
        };
    }
    
    // Calculate Levenshtein distance for similarity
    const similarity = calculateSimilarity(normalizedUser, normalizedCorrect);
    
    if (similarity >= 0.8) {
        return {
            isCorrect: true,
            partialCredit: true,
            similarity: similarity,
            message: "Very close! Great job! 👏"
        };
    } else if (similarity >= 0.6) {
        return {
            isCorrect: false,
            partialCredit: true,
            similarity: similarity,
            message: "Close, but not quite. Keep trying! 💪"
        };
    } else {
        return {
            isCorrect: false,
            partialCredit: false,
            similarity: similarity,
            message: "Not quite right, but don't give up! 🚀"
        };
    }
}

function calculateSimilarity(str1, str2) {
    const len1 = str1.length;
    const len2 = str2.length;
    
    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;
    
    const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
    
    for (let i = 0; i <= len1; i++) matrix[i][0] = i;
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;
    
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            if (str1[i - 1] === str2[j - 1]) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j - 1] + 1
                );
            }
        }
    }
    
    const maxLen = Math.max(len1, len2);
    return (maxLen - matrix[len1][len2]) / maxLen;
}

function displayFeedback(feedback) {
    const feedbackArea = document.getElementById('feedbackArea');
    const feedbackContent = document.getElementById('feedbackContent');
    const explanationContent = document.getElementById('explanationContent');
    
    if (feedback.isCorrect) {
        feedbackArea.className = 'mt-8 p-6 rounded-xl bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200';
        feedbackContent.innerHTML = `
            <div class="flex items-center space-x-3">
                <span class="text-3xl">✅</span>
                <div>
                    <div class="font-bold text-green-800 text-lg">${feedback.message}</div>
                    <div class="text-green-700">Correct answer: ${currentFlashcard.correct_answer}</div>
                </div>
            </div>
        `;
    } else {
        feedbackArea.className = 'mt-8 p-6 rounded-xl bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200';
        feedbackContent.innerHTML = `
            <div class="flex items-center space-x-3">
                <span class="text-3xl">❌</span>
                <div>
                    <div class="font-bold text-red-800 text-lg">${feedback.message}</div>
                    <div class="text-red-700">Correct answer: ${currentFlashcard.correct_answer}</div>
                </div>
            </div>
        `;
    }
    
    if (currentFlashcard.explanation) {
        explanationContent.innerHTML = `
            <div class="flex items-start space-x-3">
                <span class="text-xl">💡</span>
                <div>
                    <div class="font-medium text-gray-800">Explanation:</div>
                    <div class="text-gray-700">${currentFlashcard.explanation}</div>
                </div>
            </div>
        `;
    } else {
        explanationContent.innerHTML = '';
    }
    
    feedbackArea.classList.remove('hidden');
    
    // Visual feedback for multiple choice
    if (currentFlashcard.question_type === 'multiple_choice') {
        document.querySelectorAll('.answer-option').forEach(option => {
            if (option.textContent === currentFlashcard.correct_answer) {
                option.classList.add('border-green-500', 'bg-green-100');
            } else if (option.classList.contains('selected') && !feedback.isCorrect) {
                option.classList.add('border-red-500', 'bg-red-100');
            }
        });
    }
}

function sendAnswerAnalytics(userAnswer, feedback) {
    // Non-blocking analytics call
    fetch('/api/answer/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            flashcard_id: currentFlashcard.id,
            user_answer: userAnswer,
            is_correct: feedback.isCorrect,
            similarity_score: feedback.similarity
        })
    }).catch(error => {
        console.log('Analytics error (non-critical):', error);
    });
}

function nextQuestion() {
    currentQuestionIndex++;
    
    // Use adaptive flashcards if available
    if (window.adaptiveFlashcards && currentQuestionIndex < window.adaptiveFlashcards.length) {
        loadNextAdaptiveFlashcard();
    } else if (currentQuestionIndex < totalQuestions) {
        generateFlashcard();
    } else {
        showSessionComplete();
    }
}

function showSessionComplete() {
    document.getElementById('flashcardDisplay').classList.add('hidden');
    document.getElementById('sessionComplete').classList.remove('hidden');
    
    // Update session stats display
    const accuracy = sessionStats.totalAnswered > 0 ? 
        Math.round(((sessionStats.correctAnswers + sessionStats.partialCredit * 0.5) / sessionStats.totalAnswered) * 100) : 0;
    
    document.getElementById('totalAnswered').textContent = sessionStats.totalAnswered;
    document.getElementById('correctAnswers').textContent = sessionStats.correctAnswers;
    document.getElementById('accuracyRate').textContent = `${accuracy}%`;
    
    // Track session completion with adaptive learning
    if (window.talonTalkAL) {
        window.talonTalkAL.trackPerformance();
    }
}

function completeSession() {
    showSessionComplete();
}

function startNewSession() {
    sessionStats = { totalAnswered: 0, correctAnswers: 0, partialCredit: 0 };
    currentQuestionIndex = 0;
    document.getElementById('sessionComplete').classList.add('hidden');
    generateFlashcard();
}

// Enhanced notification system with TalonTalk branding
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    const notification = document.createElement('div');
    notification.className = 'notification fixed top-4 right-4 z-50 p-4 rounded-xl shadow-xl transform translate-x-full transition-transform duration-300';
    
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-gradient-to-r from-green-500 to-emerald-600';
            textColor = 'text-white';
            icon = '✅';
            break;
        case 'error':
            bgColor = 'bg-gradient-to-r from-red-500 to-pink-600';
            textColor = 'text-white';
            icon = '❌';
            break;
        case 'warning':
            bgColor = 'bg-gradient-to-r from-falcon-yellow to-amber-500';
            textColor = 'text-talon-blue';
            icon = '⚠️';
            break;
        default:
            bgColor = 'bg-gradient-to-r from-talon-blue to-blue-600';
            textColor = 'text-white';
            icon = 'ℹ️';
    }
    
    notification.className += ` ${bgColor} ${textColor}`;
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <span class="text-xl">${icon}</span>
            <span class="font-medium">${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out after 4 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

// Handle Enter key for text input
document.addEventListener('DOMContentLoaded', function() {
    const userAnswerInput = document.getElementById('userAnswer');
    if (userAnswerInput) {
        userAnswerInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submitAnswerBtn').classList.contains('hidden')) {
                submitAnswer();
            }
        });
    }
});

function loadAdaptiveFlashcards(contentData) {
    console.log('🎯 Loading adaptive flashcards:', contentData);
    
    // If we have preloaded flashcards, use them
    if (contentData.flashcards && contentData.flashcards.length > 0) {
        window.adaptiveFlashcards = contentData.flashcards;
        currentQuestionIndex = 0;
        loadNextAdaptiveFlashcard();
    } else {
        // Fallback to regular generation
        console.log('No adaptive flashcards available, using fallback');
        generateFlashcard();
    }
}

function loadNextAdaptiveFlashcard() {
    if (window.adaptiveFlashcards && currentQuestionIndex < window.adaptiveFlashcards.length) {
        currentFlashcard = window.adaptiveFlashcards[currentQuestionIndex];
        questionStartTime = Date.now();
        displayFlashcard();
    } else {
        // No more adaptive flashcards, complete session
        completeSession();
    }
}

function getCurrentLanguage() {
    // Get current language from UI or localStorage
    return localStorage.getItem('preferred_language') || 'spanish';
}

function trackAdaptiveAnswer(userAnswer, isCorrect) {
    if (window.talonTalkAL && currentFlashcard && questionStartTime) {
        const responseTime = Date.now() - questionStartTime;
        const usedHint = document.getElementById('hintDisplay').style.display !== 'none';
        
        window.talonTalkAL.recordQuestionAttempt(
            currentFlashcard,
            userAnswer,
            isCorrect,
            responseTime,
            usedHint
        );
    }
}

// Adaptive Learning UI Functions
function loadPersonalizedContent() {
    if (window.talonTalkAL) {
        showNotification('🎯 Loading personalized content...', 'info');
        
        window.talonTalkAL.getContentRecommendations().then(recommendations => {
            if (recommendations && recommendations.length > 0) {
                displayContentRecommendations(recommendations);
                showNotification('✅ Personalized content loaded!', 'success');
            } else {
                showNotification('ℹ️ No new recommendations available', 'info');
            }
        }).catch(error => {
            console.warn('Failed to load personalized content:', error);
            showNotification('⚠️ Failed to load personalized content', 'warning');
        });
    } else {
        showNotification('🔄 Adaptive learning system initializing...', 'info');
    }
}

function viewDetailedAnalytics() {
    if (window.talonTalkAL) {
        window.talonTalkAL.getLearningAnalytics().then(analytics => {
            if (analytics) {
                displayAnalyticsModal(analytics);
            }
        }).catch(error => {
            console.warn('Failed to load analytics:', error);
            showNotification('⚠️ Analytics temporarily unavailable', 'warning');
        });
    }
}

function displayContentRecommendations(recommendations) {
    const container = document.getElementById('recommendations-content');
    if (!container) return;
    
    const html = recommendations.slice(0, 3).map(rec => `
        <div class="adaptive-recommendation">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-semibold text-white">${rec.title || rec.content_type}</h4>
                    <p class="text-blue-100 text-sm">${rec.description || rec.reasons?.join(', ') || 'Recommended for you'}</p>
                </div>
                <div class="text-right">
                    <div class="text-xs text-blue-200">Match: ${Math.round((rec.suitability_score || 0.8) * 100)}%</div>
                    <div class="text-xs text-blue-200">${rec.estimated_duration || '10 min'}</div>
                </div>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = html;
}

function displayAnalyticsModal(analytics) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-2xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-800">Your Learning Analytics</h2>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Performance Summary -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-blue-800">Total Sessions</h3>
                    <p class="text-2xl font-bold text-blue-600">${analytics.performance_summary?.total_sessions || 0}</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-green-800">Overall Accuracy</h3>
                    <p class="text-2xl font-bold text-green-600">${Math.round((analytics.performance_summary?.overall_accuracy || 0) * 100)}%</p>
                </div>
            </div>
            
            <!-- Strengths and Improvements -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">Strengths</h3>
                    <div class="space-y-1">
                        ${(analytics.topic_analysis?.strong_topics || []).slice(0, 3).map(topic => 
                            `<span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-sm">${topic}</span>`
                        ).join('')}
                    </div>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-700 mb-2">Areas to Improve</h3>
                    <div class="space-y-1">
                        ${(analytics.topic_analysis?.weak_topics || []).slice(0, 3).map(topic => 
                            `<span class="inline-block bg-red-100 text-red-800 px-2 py-1 rounded text-sm">${topic}</span>`
                        ).join('')}
                    </div>
                </div>
            </div>
            
            <!-- Learning Trend -->
            <div class="mb-6">
                <h3 class="font-semibold text-gray-700 mb-2">Learning Trend</h3>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-600">
                        ${analytics.trends?.trend === 'improving' ? '📈 Your performance is improving!' :
                          analytics.trends?.trend === 'stable' ? '📊 Your performance is stable.' :
                          analytics.trends?.trend === 'declining' ? '📉 Focus on review and practice.' :
                          '📋 Keep practicing to see trends.'}
                    </p>
                </div>
            </div>
            
            <button onclick="this.closest('.fixed').remove()" class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Close Analytics
            </button>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Initialize adaptive learning insights on page load
document.addEventListener('DOMContentLoaded', function() {
    // Simple initialization without complex dependencies
    if (document.body.dataset.authenticated === 'true') {
        // Allow users to manually trigger insights loading
        console.log('Dashboard loaded for authenticated user');
    }
});

// Simplified loadPersonalizedContent function
function loadPersonalizedContent() {
    const sessionStats = document.getElementById('session-stats');
    const recommendations = document.getElementById('learning-recommendations');
    
    // Show loading state
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = '🔄 Loading...';
    button.disabled = true;
    
    // Make API call to get analytics
    fetch('/api/analytics/', {
        method: 'GET',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        // Show performance stats if available
        if (data.today_stats && sessionStats) {
            sessionStats.classList.remove('hidden');
            if (data.today_stats.accuracy !== undefined) {
                sessionStats.querySelector('[data-progress="accuracy"]').textContent = Math.round(data.today_stats.accuracy * 100) + '%';
            }
            if (data.today_stats.questions_answered !== undefined) {
                sessionStats.querySelector('[data-progress="questions"]').textContent = data.today_stats.questions_answered;
            }
            if (data.today_stats.streak !== undefined) {
                sessionStats.querySelector('[data-progress="streak"]').textContent = data.today_stats.streak;
            }
        }
        
        // Show recommendations if available
        if (data.recommendations && data.recommendations.length > 0 && recommendations) {
            recommendations.classList.remove('hidden');
            const content = recommendations.querySelector('#recommendations-content');
            content.innerHTML = data.recommendations.slice(0, 3).map(rec => 
                `<div class="text-xs text-gray-700 bg-gray-50 p-2 rounded">${rec}</div>`
            ).join('');
        }
        
        // Restore button
        button.textContent = '✓ Loaded';
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
        }, 2000);
    })
    .catch(error => {
        console.log('Analytics not available:', error);
        button.textContent = '⚠️ Try Again';
        button.disabled = false;
    });
}

// Simple Settings Modal
function openSettingsModal() {
    // Create modal if it doesn't exist
    let modal = document.getElementById('settings-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'settings-modal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold text-gray-900">Settings</h2>
                    <button onclick="closeSettingsModal()" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Learning Language</label>
                        <select id="settings-language" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="spanish">🇪🇸 Spanish</option>
                            <option value="french">🇫🇷 French</option>
                            <option value="german">🇩🇪 German</option>
                            <option value="italian">🇮🇹 Italian</option>
                            <option value="portuguese">🇵🇹 Portuguese</option>
                        </select>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeSettingsModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                            Cancel
                        </button>
                        <button onclick="saveSettings()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Save Changes
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Load current language
    const currentLang = localStorage.getItem('preferred_language') || 'spanish';
    document.getElementById('settings-language').value = currentLang;

    modal.classList.remove('hidden');
}

function closeSettingsModal() {
    const modal = document.getElementById('settings-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function saveSettings() {
    const language = document.getElementById('settings-language').value;

    // Show loading notification
    const loadingNotification = document.createElement('div');
    loadingNotification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
    loadingNotification.textContent = 'Updating language preference...';
    document.body.appendChild(loadingNotification);

    // Call backend API to update language preference
    fetch('/api/language/user/set/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
        body: JSON.stringify({
            language: language
        })
    })
    .then(response => response.json())
    .then(data => {
        // Remove loading notification
        document.body.removeChild(loadingNotification);

        if (data.success) {
            // Update localStorage
            localStorage.setItem('preferred_language', language);

            // Show success notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            notification.textContent = `Language updated to ${data.language.name}! Refreshing page...`;
            document.body.appendChild(notification);

            setTimeout(() => {
                // Refresh page to apply language changes
                window.location.reload();
            }, 1500);
        } else {
            // Show error notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            notification.textContent = data.error || 'Failed to update language preference';
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }
    })
    .catch(error => {
        // Remove loading notification
        if (document.body.contains(loadingNotification)) {
            document.body.removeChild(loadingNotification);
        }

        console.error('Error updating language:', error);

        // Show error notification
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        notification.textContent = 'Network error. Please try again.';
        document.body.appendChild(notification);

        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    });

    closeSettingsModal();
}
</script>

<!-- Include Enhanced Welcome Modal -->
{% include 'components/enhanced_welcome_modal.html' %}

<!-- Adaptive Learning System -->
<script src="{% static 'js/adaptive-learning.js' %}?v=2"></script>

<!-- Welcome Modal Integration Script -->
<script src="{% static 'js/welcome-modal-integration.js' %}"></script>

<!-- Mark user as authenticated for adaptive learning -->
<script>
    document.body.dataset.authenticated = '{{ user.is_authenticated|yesno:"true,false" }}';
</script>

{% csrf_token %}
{% endblock %}
