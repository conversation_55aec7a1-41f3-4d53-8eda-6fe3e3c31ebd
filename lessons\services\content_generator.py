"""
Content Generator Service for TalonTalk Language Learning
Uses C.A.R.E. framework to generate contextual, adaptive content
"""

import json
import logging
from typing import Dict, List, Optional, Union
from django.utils import timezone
from openai import OpenAI

from lessons.models import ContentItem, Lesson
from ai_services.llm_config import get_recommended_config
from ai_services.llm_flashcards import DifficultyLevel
from lessons.advanced_quality_engine import AdvancedQualityEngine, QualityLevel


logger = logging.getLogger(__name__)


class ContentGeneratorService:
    """
    Generates educational content using AI with C.A.R.E. framework
    """

    def __init__(self):
        self.config = get_recommended_config()
        self.client = self._setup_client()
        self.quality_engine = AdvancedQualityEngine()

    def _setup_client(self):
        """Setup OpenAI-compatible client with current config"""
        try:
            client = OpenAI(api_key=self.config.api_key, base_url=self.config.base_url)
            logger.info(
                f"✅ AI Client initialized: {self.config.provider.value} - {self.config.model_name}"
            )
            return client
        except Exception as e:
            logger.error(f"❌ Failed to initialize AI client: {e}")
            return None

    def generate_care_content_batch(
        self,
        language: str,
        difficulty_level: int,
        content_types: List[str],
        batch_size: int = 5,
        topic: Optional[str] = None,
    ) -> Dict:
        """
        Generate a batch of content items using C.A.R.E. framework

        Args:
            language: Target language (e.g., 'spanish', 'french')
            difficulty_level: 1-5 difficulty scale
            content_types: List of content types ['flashcard', 'mcq', 'fill_blank']
            batch_size: Number of items to generate
            topic: Optional topic focus

        Returns:
            Dict with success status and generated content items
        """
        if not self.client:
            return {
                "success": False,
                "error": "AI client not initialized",
                "content_items": [],
                "batch_size": 0,
            }

        try:
            # Generate content using AI
            prompt = self._build_care_prompt(
                language, difficulty_level, content_types, batch_size, topic
            )

            response = self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert language learning content creator using the C.A.R.E. framework (Contextual, Adaptive, Reflective, Engaging). Generate high-quality educational content.",
                    },
                    {"role": "user", "content": prompt},
                ],
                max_tokens=(
                    self.config.model_config.max_tokens
                    if self.config.model_config
                    else 2000
                ),
                temperature=0.7,
            )

            # Parse AI response
            content_data = self._parse_ai_response(response.choices[0].message.content)

            # Create ContentItem objects
            raw_content_items = self._create_content_items(
                content_data, language, difficulty_level
            )

            # Validate content quality
            validated_items = []
            rejected_count = 0

            for item in raw_content_items:
                content_dict = {
                    "id": str(item.id) if item.id else "new",
                    "question": item.question_text,
                    "content_type": item.type,
                    "language": item.language,
                }

                # Add optional fields
                if hasattr(item, "explanation_text") and item.explanation_text:
                    content_dict["explanation"] = item.explanation_text
                if hasattr(item, "choices_json") and item.choices_json:
                    content_dict["options"] = item.choices_json
                if hasattr(item, "answer_text") and item.answer_text:
                    content_dict["correct_answer"] = item.answer_text

                # Validate quality
                quality_report = self.quality_engine.validate_content(
                    content_dict, language
                )

                if (
                    quality_report.overall_score >= 60
                    and quality_report.quality_level != QualityLevel.REJECTED
                ):
                    validated_items.append(item)
                else:
                    rejected_count += 1
                    logger.warning(
                        f"Rejected content item with quality score {quality_report.overall_score:.1f}: "
                        f"{item.question_text[:50]}..."
                    )

            logger.info(
                f"✅ Generated {len(validated_items)} quality content items for {language} "
                f"(rejected {rejected_count} poor quality items)"
            )

            return {
                "success": True,
                "content_items": validated_items,
                "batch_size": len(validated_items),
                "rejected_count": rejected_count,
                "ai_provider": self.config.provider.value,
                "model_used": self.config.model_name,
            }

        except Exception as e:
            logger.error(f"❌ Content generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "content_items": [],
                "batch_size": 0,
            }

    def _build_care_prompt(
        self,
        language: str,
        difficulty_level: int,
        content_types: List[str],
        batch_size: int,
        topic: Optional[str] = None,
    ) -> str:
        """Build AI prompt using C.A.R.E. framework"""

        difficulty_map = {
            1: "Beginner (A1) - Basic words and phrases",
            2: "Elementary (A2) - Simple sentences and common topics",
            3: "Intermediate (B1) - Complex sentences and varied topics",
            4: "Upper-Intermediate (B2) - Advanced grammar and nuanced concepts",
            5: "Advanced (C1) - Sophisticated language and abstract ideas",
        }

        topic_text = f" focused on the topic: {topic}" if topic else ""

        prompt = f"""
Generate {batch_size} language learning content items for {language.title()} at {difficulty_map.get(difficulty_level, 'Intermediate')} level{topic_text}.

Use the C.A.R.E. framework:
- **Contextual**: Include real-world context and cultural relevance
- **Adaptive**: Provide progressive hints for different skill levels
- **Reflective**: Include explanations that promote understanding
- **Engaging**: Make content interesting and memorable

Content types to generate: {', '.join(content_types)}

Return ONLY a valid JSON array with this exact structure:
[
  {{
    "type": "flashcard|mcq|fill_blank|translation",
    "question_text": "The main question or prompt",
    "correct_answer": "The correct answer",
    "options": ["option1", "option2", "option3", "option4"],
    "hints": ["Progressive hint 1", "Progressive hint 2", "Progressive hint 3"],
    "explanation": "Clear explanation of the answer and grammar/vocabulary involved",
    "context": "Real-world context or cultural note",
    "difficulty_factors": ["grammar_point", "vocabulary_level", "cultural_concept"]
  }}
]

Examples by type:
- **flashcard**: Question in {language}, answer in English (or vice versa)
- **mcq**: Multiple choice question with 4 options
- **fill_blank**: Sentence with blank to fill in
- **translation**: Translate phrase/sentence between languages

Ensure all content is:
1. Culturally appropriate and accurate
2. Grammatically correct in both languages
3. Appropriate for the specified difficulty level
4. Engaging and memorable
5. Includes practical, everyday usage
"""

        return prompt

    def _parse_ai_response(self, response_text: str) -> List[Dict]:
        """Parse AI response into structured data"""
        try:
            # Clean response text (remove markdown formatting if present)
            cleaned_text = response_text.strip()
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3]
            cleaned_text = cleaned_text.strip()

            # Parse JSON
            content_data = json.loads(cleaned_text)

            if not isinstance(content_data, list):
                logger.warning("AI response not a list, wrapping in array")
                content_data = [content_data]

            return content_data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response as JSON: {e}")
            logger.error(f"Response text: {response_text[:500]}...")
            return []
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return []

    def _create_content_items(
        self, content_data: List[Dict], language: str, difficulty_level: int
    ) -> List[ContentItem]:
        """Create ContentItem objects from parsed data"""
        content_items = []

        try:
            for item_data in content_data:
                try:
                    # Create ContentItem
                    content_item = ContentItem.objects.create(
                        type=item_data.get("type", "flashcard"),
                        question_text=item_data.get("question_text", ""),
                        answer_text=item_data.get("correct_answer", ""),
                        choices_json=item_data.get("options", []),
                        hint_text=item_data.get("hint", ""),
                        explanation_text=item_data.get("explanation", ""),
                        difficulty=difficulty_level,
                        language=language,
                        tags=item_data.get("tags", []),
                    )

                    content_items.append(content_item)
                    logger.debug(
                        f"Created content item: {content_item.type} - {content_item.question_text[:50]}..."
                    )

                except Exception as e:
                    logger.error(f"Failed to create content item: {e}")
                    logger.error(f"Item data: {item_data}")
                    continue

        except Exception as e:
            logger.error(f"Error creating content items: {e}")

        return content_items

    def generate_single_content(
        self,
        content_type: str,
        language: str,
        difficulty_level: int,
        topic: Optional[str] = None,
        user_context: Optional[Dict] = None,
    ) -> Optional[ContentItem]:
        """Generate a single content item with user-specific context"""

        result = self.generate_care_content_batch(
            language=language,
            difficulty_level=difficulty_level,
            content_types=[content_type],
            batch_size=1,
            topic=topic,
        )

        if result["success"] and result["content_items"]:
            return result["content_items"][0]

        return None

    def regenerate_content_with_feedback(
        self,
        original_item: ContentItem,
        feedback: str,
        user_performance: Optional[Dict] = None,
    ) -> Optional[ContentItem]:
        """Regenerate content based on user feedback and performance"""

        # Build feedback-aware prompt
        prompt = f"""
The user provided feedback on this {original_item.type} content:
Original Question: {original_item.question_text}
Original Answer: {original_item.correct_answer}
User Feedback: {feedback}

Please create an improved version that addresses this feedback while maintaining the same learning objectives and difficulty level.

Use the C.A.R.E. framework and return the same JSON structure as before.
"""

        try:
            response = self.client.chat.completions.create(
                model=self.config.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert language learning content creator. Improve content based on user feedback.",
                    },
                    {"role": "user", "content": prompt},
                ],
                max_tokens=1000,
                temperature=0.8,
            )

            content_data = self._parse_ai_response(response.choices[0].message.content)

            if content_data:
                improved_items = self._create_content_items(
                    content_data,
                    original_item.language,
                    original_item.difficulty_level.level,
                )
                if improved_items:
                    return improved_items[0]

        except Exception as e:
            logger.error(f"Failed to regenerate content with feedback: {e}")

        return None
