<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C.A.R.E. JavaScript Debug Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">C.A.R.E. JavaScript Debug Test</h1>

        <div class="space-y-4">
            <div class="bg-blue-50 p-4 rounded">
                <h2 class="font-bold">Test 1: Basic Button Click</h2>
                <button id="testBtn1" class="bg-blue-500 text-white px-4 py-2 rounded">
                    Click Me - Basic Test
                </button>
                <span id="result1" class="ml-4"></span>
            </div>

            <div class="bg-green-50 p-4 rounded">
                <h2 class="font-bold">Test 2: Next Phase Button (Same Class)</h2>
                <button class="next-phase-btn bg-green-500 text-white px-4 py-2 rounded">
                    Next Phase Button Test
                </button>
                <span id="result2" class="ml-4"></span>
            </div>

            <div class="bg-purple-50 p-4 rounded">
                <h2 class="font-bold">Test 3: C.A.R.E. Manager Status</h2>
                <div id="careStatus" class="font-mono text-sm"></div>
            </div>

            <div class="bg-yellow-50 p-4 rounded">
                <h2 class="font-bold">Test 4: API Test</h2>
                <button id="apiTestBtn" class="bg-yellow-500 text-white px-4 py-2 rounded">
                    Test API Call
                </button>
                <div id="apiResult" class="mt-2 text-sm"></div>
            </div>
        </div>
    </div>

    <script>
        console.log('🔧 Debug script loading...');

        // Test 1: Basic button functionality
        document.getElementById('testBtn1').addEventListener('click', function () {
            console.log('✅ Basic button click works!');
            document.getElementById('result1').textContent = '✅ Working!';
            document.getElementById('result1').className = 'ml-4 text-green-600 font-bold';
        });

        // Test 2: Check if C.A.R.E. manager exists
        document.addEventListener('DOMContentLoaded', function () {
            setTimeout(() => {
                const statusDiv = document.getElementById('careStatus');
                if (window.careManager) {
                    statusDiv.innerHTML = `
                        ✅ C.A.R.E. Manager Found<br>
                        Current Phase: ${window.careManager.currentPhase}<br>
                        Phases: ${window.careManager.phases.join(', ')}
                    `;
                    statusDiv.className = 'font-mono text-sm text-green-600';
                } else {
                    statusDiv.innerHTML = '❌ C.A.R.E. Manager NOT Found';
                    statusDiv.className = 'font-mono text-sm text-red-600';
                }
            }, 1000);
        });

        // Test 3: Check next-phase-btn class handling
        document.addEventListener('click', function (e) {
            if (e.target.classList.contains('next-phase-btn')) {
                console.log('🔧 next-phase-btn clicked!');
                document.getElementById('result2').textContent = '✅ Event delegation works!';
                document.getElementById('result2').className = 'ml-4 text-green-600 font-bold';

                if (window.careManager) {
                    console.log('📞 Calling careManager.nextPhase()');
                    // Don't actually call it to avoid navigation
                    document.getElementById('result2').textContent += ' (C.A.R.E. Manager found)';
                } else {
                    document.getElementById('result2').textContent += ' (C.A.R.E. Manager missing!)';
                    document.getElementById('result2').className = 'ml-4 text-red-600 font-bold';
                }
            }
        });

        // Test 4: API test
        document.getElementById('apiTestBtn').addEventListener('click', async function () {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = 'Testing API...';

            try {
                const response = await fetch('/care/api/phase/contextualize/');
                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `✅ API Working!<br>Content size: ${JSON.stringify(data.content).length} chars`;
                    resultDiv.className = 'mt-2 text-sm text-green-600';
                } else {
                    resultDiv.innerHTML = `❌ API Error: ${data.error}`;
                    resultDiv.className = 'mt-2 text-sm text-red-600';
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
                resultDiv.className = 'mt-2 text-sm text-red-600';
            }
        });

        console.log('🔧 Debug script loaded successfully');
    </script>

    <script type="module" src="/static/js/dist/care-lesson.js"></script>
</body>

</html>