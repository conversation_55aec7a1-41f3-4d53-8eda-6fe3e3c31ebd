"""
Quality Control System for TalonTalk Content Generation
Validates and improves AI-generated flashcards and lessons
"""

import logging
import re
from typing import Dict, List, Optional, Tuple
from enum import Enum

logger = logging.getLogger(__name__)


class QualityIssue(Enum):
    CIRCULAR_LOGIC = "circular_logic"
    CAPITALIZATION_MISMATCH = "capitalization_mismatch"
    POOR_QUESTION_FORMAT = "poor_question_format"
    MISSING_CORRECT_ANSWER = "missing_correct_answer"
    CONFUSING_OPTIONS = "confusing_options"
    LANGUAGE_MIXING = "language_mixing"
    TOO_EASY = "too_easy"
    TOO_HARD = "too_hard"


class FlashcardQualityValidator:
    """
    Enhanced Quality Control System for TalonTalk Content Generation
    Implements the Content Quality Gateway with automated validation
    """

    def __init__(self):
        self.quality_issues = []
        self.validation_rules = self._initialize_validation_rules()

    def _initialize_validation_rules(self) -> Dict:
        """Initialize comprehensive validation rules"""
        return {
            "spanish": {
                "question_markers": ["¿", "?"],
                "common_greetings": [
                    "hola",
                    "buenos días",
                    "buenas tardes",
                    "buenas noches",
                ],
                "forbidden_patterns": [
                    r"qué palabra significa.*\?",  # Avoid "What word means X?" format
                    r"cuál es.*palabra.*\?",  # Avoid "What is the word for X?"
                ],
                "required_capitalization": {
                    "sentence_start": True,
                    "proper_nouns": True,
                    "all_caps": False,
                },
            }
        }

    def validate_flashcard(self, flashcard: Dict) -> Tuple[bool, List[str], Dict]:
        """
        Validate a flashcard and return:
        - is_valid: boolean
        - issues: list of issue descriptions
        - fixed_flashcard: corrected version
        """
        self.quality_issues = []
        fixed_flashcard = flashcard.copy()

        # Check for circular logic
        self._check_circular_logic(fixed_flashcard)

        # Check capitalization consistency
        self._check_capitalization(fixed_flashcard)

        # Check question format quality
        self._check_question_format(fixed_flashcard)

        # Check multiple choice options
        self._check_multiple_choice_options(fixed_flashcard)

        # Check for language mixing issues
        self._check_language_consistency(fixed_flashcard)

        # NEW: Advanced validation checks
        self._check_json_structure(fixed_flashcard)
        self._check_pedagogical_quality(fixed_flashcard)
        self._check_answer_logic(fixed_flashcard)

        is_valid = len(self.quality_issues) == 0
        issue_descriptions = [issue.value for issue in self.quality_issues]

        return is_valid, issue_descriptions, fixed_flashcard

    def _check_json_structure(self, flashcard: Dict):
        """Validate JSON structure and required fields"""
        required_fields = ["question", "correct_answer", "options"]

        for field in required_fields:
            if field not in flashcard or not flashcard[field]:
                self.quality_issues.append(QualityIssue.MISSING_CORRECT_ANSWER)

        # Check options structure
        options = flashcard.get("options", [])
        if not isinstance(options, list) or len(options) != 4:
            self.quality_issues.append(QualityIssue.CONFUSING_OPTIONS)

        # Ensure correct answer is in options
        correct_answer = flashcard.get("correct_answer", "")
        if correct_answer not in options:
            self.quality_issues.append(QualityIssue.MISSING_CORRECT_ANSWER)

    def _check_pedagogical_quality(self, flashcard: Dict):
        """Check pedagogical soundness of the flashcard"""
        question = flashcard.get("question", "").lower()

        # Check for forbidden question patterns
        language = flashcard.get("language", "spanish")
        if language in self.validation_rules:
            forbidden_patterns = self.validation_rules[language]["forbidden_patterns"]
            for pattern in forbidden_patterns:
                if re.search(pattern, question):
                    self.quality_issues.append(QualityIssue.POOR_QUESTION_FORMAT)
                    break

    def _check_answer_logic(self, flashcard: Dict):
        """Check for circular logic and answer quality"""
        question = flashcard.get("question", "").lower()
        correct_answer = flashcard.get("correct_answer", "").lower()

        # Check for circular logic - question and answer shouldn't be too similar
        question_words = set(question.split())
        answer_words = set(correct_answer.split())

        # If more than 50% of words overlap, it might be circular
        if len(question_words & answer_words) / max(len(question_words), 1) > 0.5:
            self.quality_issues.append(QualityIssue.CIRCULAR_LOGIC)

    def _check_circular_logic(self, flashcard: Dict):
        """Check for circular logic in questions"""
        question = flashcard.get("question", "").lower()
        correct_answer = flashcard.get("correct_answer", "").lower()

        # Bad: "¿Qué palabra en español significa 'comida'?" -> "comida"
        if "qué palabra" in question and "significa" in question:
            # Extract what the question is asking about
            match = re.search(r"significa ['\"]([^'\"]+)['\"]", question)
            if match:
                asked_word = match.group(1).lower()
                if asked_word == correct_answer:
                    self.quality_issues.append(QualityIssue.CIRCULAR_LOGIC)
                    # Fix: Convert to "How do you say X" format
                    english_word = asked_word
                    flashcard["question"] = (
                        f"¿Cómo se dice '{english_word}' en español?"
                    )

    def _check_capitalization(self, flashcard: Dict):
        """Check capitalization consistency"""
        correct_answer = flashcard.get("correct_answer", "")
        options = flashcard.get("options", [])

        if options and correct_answer:
            # Find the correct answer in options (case insensitive)
            correct_in_options = None
            for option in options:
                if option.lower() == correct_answer.lower():
                    correct_in_options = option
                    break

            if correct_in_options and correct_in_options != correct_answer:
                self.quality_issues.append(QualityIssue.CAPITALIZATION_MISMATCH)
                # Fix: Use the option's capitalization
                flashcard["correct_answer"] = correct_in_options

    def _check_question_format(self, flashcard: Dict):
        """Check if question format is pedagogically sound"""
        question = flashcard.get("question", "")
        language = flashcard.get("language", "spanish")

        # Preferred format: "¿Cómo se dice 'X' en español?"
        # Avoid: "¿Qué palabra significa X?" when answer is also in target language
        if language == "spanish":
            if "qué palabra" in question.lower() and "significa" in question.lower():
                self.quality_issues.append(QualityIssue.POOR_QUESTION_FORMAT)
                # Don't auto-fix here as it requires more context

    def _check_multiple_choice_options(self, flashcard: Dict):
        """Check multiple choice option quality"""
        options = flashcard.get("options", [])
        correct_answer = flashcard.get("correct_answer", "")
        question_type = flashcard.get("question_type", "")

        if question_type == "multiple_choice" and options:
            # Check if correct answer is in options (case insensitive)
            correct_in_options = any(
                opt.lower() == correct_answer.lower() for opt in options
            )

            if not correct_in_options:
                self.quality_issues.append(QualityIssue.MISSING_CORRECT_ANSWER)
                # Fix: Replace a random option with correct answer
                import random

                random_index = random.randint(0, len(options) - 1)
                options[random_index] = correct_answer
                flashcard["options"] = options

            # Check for duplicate options
            unique_options = list(set(opt.lower() for opt in options))
            if len(unique_options) < len(options):
                self.quality_issues.append(QualityIssue.CONFUSING_OPTIONS)

    def _check_language_consistency(self, flashcard: Dict):
        """Check for inappropriate language mixing"""
        question = flashcard.get("question", "")
        options = flashcard.get("options", [])
        language = flashcard.get("language", "spanish")

        if language == "spanish":
            # Check for English words in Spanish questions (bad mixing)
            english_words = [
                "what",
                "does",
                "mean",
                "english",
                "how",
                "which",
                "where",
                "when",
                "why",
            ]
            question_lower = question.lower()

            # Bad pattern: "¿What does X mean in English??"
            if any(word in question_lower for word in english_words):
                # Check if it's a pedagogically inappropriate mix
                if ("what does" in question_lower and "mean" in question_lower) or (
                    "how do you say" in question_lower and question.startswith("¿")
                ):
                    self.quality_issues.append(QualityIssue.LANGUAGE_MIXING)
                    # Fix: Convert to proper Spanish format
                    # Extract the word being asked about
                    if "'" in question:
                        import re

                        match = re.search(r"'([^']+)'", question)
                        if match:
                            target_word = match.group(1)
                            flashcard["question"] = (
                                f"¿Cómo se dice '{target_word}' en español?"
                            )

            # Question should have Spanish markers (¿, ?)
            if not ("¿" in question and "?" in question):
                if not any(
                    eng_word in question.lower()
                    for eng_word in ["how", "what", "which"]
                ):
                    self.quality_issues.append(QualityIssue.LANGUAGE_MIXING)


def improve_flashcard_quality(flashcard: Dict) -> Dict:
    """
    Improve flashcard quality using predefined patterns
    """
    improved = flashcard.copy()

    # Fix common question formats
    question = improved.get("question", "")
    language = improved.get("language", "spanish")

    if language == "spanish":
        # Fix: "¿What does X mean in English??" -> "¿Cómo se dice 'X' en español?"
        if "what does" in question.lower() and "mean" in question.lower():
            import re

            match = re.search(r"'([^']+)'", question)
            if match:
                word = match.group(1)
                improved["question"] = f"¿Cómo se dice '{word}' en español?"

        # Convert poor formats to better ones
        if "qué palabra en español significa" in question.lower():
            # Extract the English word being asked about
            match = re.search(r"significa ['\"]([^'\"]+)['\"]", question, re.IGNORECASE)
            if match:
                english_word = match.group(1)
                improved["question"] = f"¿Cómo se dice '{english_word}' en español?"

        # Ensure proper Spanish question format
        if not question.startswith("¿"):
            if question.lower().startswith("how"):
                # Convert English question to Spanish
                improved["question"] = f"¿{question}?"
            else:
                improved["question"] = f"¿{question}?"

        # Fix double question marks
        if question.endswith("??"):
            improved["question"] = question.rstrip("?") + "?"

    return improved


def generate_high_quality_demo_flashcards(
    language: str = "spanish", count: int = 10
) -> List[Dict]:
    """Generate high-quality demo flashcards as examples"""

    if language == "spanish":
        base_flashcards = [
            {
                "question": "¿Cómo se dice 'Hello' en español?",
                "options": ["Hola", "Adiós", "Gracias", "Por favor"],
                "correct_answer": "Hola",
                "hint": "It's a common greeting used anytime.",
                "explanation": "'Hola' is the most common way to say 'Hello' in Spanish.",
                "example_sentence": "Hola, ¿cómo estás?",
                "pronunciation_guide": "OH-lah",
            },
            {
                "question": "¿Cómo se dice 'Thank you' en español?",
                "options": ["Hola", "Adiós", "Gracias", "Por favor"],
                "correct_answer": "Gracias",
                "hint": "Used to show appreciation.",
                "explanation": "'Gracias' expresses gratitude in Spanish.",
                "example_sentence": "Gracias por tu ayuda.",
                "pronunciation_guide": "GRAH-see-ahs",
            },
            {
                "question": "¿Cómo se dice 'Water' en español?",
                "options": ["Leche", "Agua", "Jugo", "Café"],
                "correct_answer": "Agua",
                "hint": "Essential for life.",
                "explanation": "'Agua' is the Spanish word for water.",
                "example_sentence": "Necesito un vaso de agua.",
                "pronunciation_guide": "AH-gwah",
            },
            {
                "question": "¿Cómo se dice 'Food' en español?",
                "options": ["Comida", "Bebida", "Ropa", "Casa"],
                "correct_answer": "Comida",
                "hint": "What you eat to survive.",
                "explanation": "'Comida' means food in Spanish.",
                "example_sentence": "La comida está deliciosa.",
                "pronunciation_guide": "ko-MEE-dah",
            },
            {
                "question": "¿Cómo se dice 'House' en español?",
                "options": ["Casa", "Carro", "Gato", "Árbol"],
                "correct_answer": "Casa",
                "hint": "Where you live.",
                "explanation": "'Casa' means house or home in Spanish.",
                "example_sentence": "Mi casa es muy cómoda.",
                "pronunciation_guide": "KAH-sah",
            },
        ]

        # Add more advanced flashcards
        advanced_flashcards = [
            {
                "question": "¿Cuál es la forma correcta del verbo 'ser' para 'nosotros'?",
                "options": ["soy", "eres", "somos", "son"],
                "correct_answer": "somos",
                "hint": "Think about 'we are' in Spanish.",
                "explanation": "'Somos' is the conjugation of 'ser' for 'nosotros' (we).",
                "example_sentence": "Nosotros somos estudiantes.",
                "pronunciation_guide": "SOH-mohs",
            },
            {
                "question": "¿Cómo se dice 'I am hungry' en español?",
                "options": [
                    "Tengo hambre",
                    "Soy hambre",
                    "Estoy hambre",
                    "Hago hambre",
                ],
                "correct_answer": "Tengo hambre",
                "hint": "In Spanish, you 'have' hunger, not 'are' hungry.",
                "explanation": "Spanish uses 'tener' (to have) with 'hambre' (hunger).",
                "example_sentence": "Tengo hambre, vamos a comer.",
                "pronunciation_guide": "TEN-go AHM-bre",
            },
        ]

        all_flashcards = base_flashcards + advanced_flashcards

        # Add metadata to each flashcard
        for i, fc in enumerate(all_flashcards):
            fc.update(
                {
                    "id": f"demo_{i+1}",
                    "question_type": "multiple_choice",
                    "difficulty": "beginner" if i < 5 else "intermediate",
                    "language": language,
                    "ai_generated": False,
                    "quality_checked": True,
                }
            )

        return all_flashcards[:count]

    return []


# Quality metrics for monitoring
class QualityMetrics:
    """Track quality metrics for content generation"""

    def __init__(self):
        self.total_generated = 0
        self.quality_issues_found = 0
        self.auto_fixed = 0
        self.rejected = 0

    def record_generation(self, flashcard: Dict, issues: List[str], was_fixed: bool):
        """Record metrics for a generated flashcard"""
        self.total_generated += 1

        if issues:
            self.quality_issues_found += 1

        if was_fixed:
            self.auto_fixed += 1

    def get_quality_score(self) -> float:
        """Get overall quality score (0-1)"""
        if self.total_generated == 0:
            return 1.0

        return 1.0 - (self.quality_issues_found / self.total_generated)

    def get_metrics_summary(self) -> Dict:
        """Get summary of quality metrics"""
        return {
            "total_generated": self.total_generated,
            "quality_score": self.get_quality_score(),
            "issues_found": self.quality_issues_found,
            "auto_fixed": self.auto_fixed,
            "rejection_rate": self.rejected / max(self.total_generated, 1),
        }


# Global quality metrics instance
quality_metrics = QualityMetrics()
