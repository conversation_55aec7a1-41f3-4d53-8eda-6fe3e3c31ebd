"""
Content Pipeline for TalonTalk C.A.R.E. Framework
Asynchronous content generation system using Django-RQ and Ollama
"""

import logging
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from django.core.cache import cache

from .models import ContentItem, UserLearningProfile, UserLessonQueue
from ai_services.llm_flashcards import LLMFlashcardService, FlashcardRequest
from ai_services.llm_config import LLMConfig, LLMProvider

logger = logging.getLogger(__name__)


class ContentPipeline:
    """
    Manages asynchronous content generation for the C.A.R.E. framework:
    - Contextualize: Cultural context and real-world scenarios
    - Acquire: Progressive difficulty with multiple input modalities
    - Reinforce: Spaced repetition and adaptive review
    - Extend: Creative application and personalized challenges
    """

    def __init__(self):
        # Configure for local Ollama development
        self.llm_config = LLMConfig(
            provider=LLMProvider.OLLAMA,
            model_tier="mistral",  # Best for language learning content
        )
        self.llm_service = LLMFlashcardService(self.llm_config)

    def generate_care_content_batch(
        self,
        language: str = "spanish",
        difficulty_level: int = 1,
        content_types: List[str] = None,
        batch_size: int = 20,
    ) -> Dict:
        """
        Generate a batch of C.A.R.E.-aligned content for the living dataset
        """
        if content_types is None:
            content_types = ["flashcard", "mcq", "translation", "grammar"]

        logger.info(
            f"Generating {batch_size} content items for {language} (difficulty {difficulty_level})"
        )

        generated_content = []
        errors = []

        # C.A.R.E. Framework Templates
        care_templates = self._get_care_templates(language, difficulty_level)

        for i in range(batch_size):
            try:
                content_type = content_types[i % len(content_types)]
                care_phase = list(care_templates.keys())[
                    i % 4
                ]  # Rotate through C.A.R.E.

                template = care_templates[care_phase]

                # Generate using AI with C.A.R.E. context
                content_item = self._generate_single_content_item(
                    content_type=content_type,
                    care_phase=care_phase,
                    template=template,
                    language=language,
                    difficulty=difficulty_level,
                )

                if content_item:
                    generated_content.append(content_item)
                    logger.info(f"Generated {care_phase} {content_type} item")

            except Exception as e:
                error_msg = f"Error generating content item {i}: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)

        return {
            "success": True,
            "generated_count": len(generated_content),
            "content_items": generated_content,
            "errors": errors,
            "timestamp": timezone.now().isoformat(),
        }

    def _get_care_templates(self, language: str, difficulty: int) -> Dict:
        """
        Get C.A.R.E. framework templates for content generation
        """
        return {
            "contextualize": {
                "focus": "Real-world scenarios and cultural context",
                "prompt_suffix": f"""
                Create content that places {language} learning in authentic cultural contexts.
                Include real-world scenarios like ordering food, asking directions, or social interactions.
                Difficulty level: {difficulty}/5
                """,
                "tags": ["cultural_context", "real_world", "scenarios"],
            },
            "acquire": {
                "focus": "Progressive skill acquisition with multiple input modalities",
                "prompt_suffix": f"""
                Create content that introduces new {language} concepts progressively.
                Use visual, auditory, and kinesthetic learning approaches.
                Build from simple to complex patterns.
                Difficulty level: {difficulty}/5
                """,
                "tags": ["skill_acquisition", "progressive", "multimodal"],
            },
            "reinforce": {
                "focus": "Spaced repetition and memory consolidation",
                "prompt_suffix": f"""
                Create content optimized for spaced repetition and memory reinforcement.
                Focus on patterns, exceptions, and commonly confused concepts in {language}.
                Difficulty level: {difficulty}/5
                """,
                "tags": ["spaced_repetition", "memory", "consolidation"],
            },
            "extend": {
                "focus": "Creative application and personalized challenges",
                "prompt_suffix": f"""
                Create content that encourages creative use of {language} knowledge.
                Include personalized challenges, storytelling, and practical application.
                Difficulty level: {difficulty}/5
                """,
                "tags": ["creative_application", "personalized", "challenges"],
            },
        }

    def _generate_single_content_item(
        self,
        content_type: str,
        care_phase: str,
        template: Dict,
        language: str,
        difficulty: int,
    ) -> Optional[ContentItem]:
        """
        Generate a single content item using AI with C.A.R.E. framework
        """
        try:
            # Create enhanced prompt for C.A.R.E. framework
            enhanced_prompt = f"""
            Generate a {content_type} for {language} learning using the C.A.R.E. framework.
            
            C.A.R.E. Phase: {care_phase.upper()}
            Focus: {template['focus']}
            {template['prompt_suffix']}
            
            Requirements:
            - Difficulty: {difficulty}/5
            - Language: {language}
            - Content Type: {content_type}
            - Include cultural nuances and context
            - Provide progressive hints for learning
            - Include explanations for deeper understanding
            
            Return JSON with: question_text, answer_text, hint_text, explanation_text, choices_json (if MCQ)
            """

            # Generate content using LLM service
            request = FlashcardRequest(
                target_language=language,
                native_language="english",
                difficulty_level=self._map_difficulty(difficulty),
                focus_areas=template["tags"],
                custom_prompt=enhanced_prompt,
            )

            response = self.llm_service.generate_flashcard(request)

            # Create ContentItem with C.A.R.E. enhancements
            content_item = ContentItem.objects.create(
                type=content_type,
                question_text=response.question,
                answer_text=response.correct_answer,
                hint_text=response.hint or "",
                explanation_text=response.explanation or "",
                choices_json=response.options if content_type == "mcq" else [],
                difficulty=difficulty,
                language=language,
                tags=template["tags"]
                + [care_phase, f"ai_generated_{timezone.now().strftime('%Y%m%d')}"],
                is_active=True,
            )

            logger.info(f"Created ContentItem {content_item.id} for {care_phase} phase")
            return content_item

        except Exception as e:
            logger.error(f"Error generating {care_phase} {content_type}: {str(e)}")
            return None

    def _map_difficulty(self, level: int) -> str:
        """Map numeric difficulty to string"""
        mapping = {
            1: "beginner",
            2: "elementary",
            3: "intermediate",
            4: "upper-intermediate",
            5: "advanced",
        }
        return mapping.get(level, "beginner")

    def populate_user_queue(self, user_id: int, queue_type: str = "daily") -> Dict:
        """
        Populate a user's learning queue with C.A.R.E.-optimized content
        """
        try:
            from django.contrib.auth import get_user_model

            User = get_user_model()

            user = User.objects.get(id=user_id)
            profile, created = UserLearningProfile.objects.get_or_create(user=user)

            # Get user's learning preferences and performance data
            user_language = getattr(user.profile, "target_language", "spanish")
            user_difficulty = profile.preferred_difficulty

            # Get adaptive content recommendations
            content_ids = self._get_adaptive_content_ids(user, queue_type)

            # Create or update user's queue
            queue, created = UserLessonQueue.objects.get_or_create(
                user=user,
                queue_type=queue_type,
                status="active",
                defaults={
                    "ordered_content_ids": content_ids,
                    "total_items": len(content_ids),
                    "expires_at": timezone.now() + timedelta(days=1),
                },
            )

            if not created:
                # Update existing queue
                queue.reset_queue(content_ids)
                queue.expires_at = timezone.now() + timedelta(days=1)
                queue.save()

            logger.info(
                f"Populated {queue_type} queue for user {user_id} with {len(content_ids)} items"
            )

            return {
                "success": True,
                "queue_id": queue.id,
                "content_count": len(content_ids),
                "queue_type": queue_type,
                "expires_at": queue.expires_at.isoformat(),
            }

        except Exception as e:
            logger.error(f"Error populating queue for user {user_id}: {str(e)}")
            return {"success": False, "error": str(e)}

    def _get_adaptive_content_ids(self, user, queue_type: str) -> List[int]:
        """
        Get adaptive content IDs based on user performance and C.A.R.E. framework
        """
        profile = user.adaptive_learning_profile

        # Base content query
        content_query = ContentItem.objects.filter(
            language=getattr(user.profile, "target_language", "spanish"), is_active=True
        )

        if queue_type == "daily":
            # Mix of all C.A.R.E. phases for balanced learning
            content_items = list(
                content_query.filter(
                    difficulty__lte=profile.preferred_difficulty + 1
                ).order_by("?")[:20]
            )

        elif queue_type == "review":
            # Focus on reinforcement phase for spaced repetition
            content_items = list(
                content_query.filter(
                    tags__contains=["spaced_repetition"],
                    difficulty__lte=profile.preferred_difficulty,
                ).order_by("?")[:15]
            )

        elif queue_type == "weak_areas":
            # Get content for areas where user is struggling
            weak_performances = user.content_performances.filter(
                proficiency_score__lt=0.5
            ).values_list("content_item_id", flat=True)

            content_items = list(content_query.filter(id__in=weak_performances)[:10])

        elif queue_type == "new_content":
            # Focus on acquire and extend phases for new learning
            content_items = list(
                content_query.filter(
                    tags__contains=["skill_acquisition", "creative_application"]
                )
                .exclude(
                    id__in=user.content_performances.values_list(
                        "content_item_id", flat=True
                    )
                )
                .order_by("difficulty")[:15]
            )

        else:
            # Default balanced queue
            content_items = list(content_query.order_by("?")[:20])

        return [item.id for item in content_items]

    def cleanup_expired_queues(self) -> Dict:
        """
        Clean up expired user queues and maintain system performance
        """
        try:
            expired_count = UserLessonQueue.objects.filter(
                expires_at__lt=timezone.now(), status="active"
            ).update(status="completed")

            logger.info(f"Cleaned up {expired_count} expired queues")

            return {
                "success": True,
                "expired_queues_cleaned": expired_count,
                "timestamp": timezone.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error cleaning up expired queues: {str(e)}")
            return {"success": False, "error": str(e)}


# Global instance for use across the application
content_pipeline = ContentPipeline()
