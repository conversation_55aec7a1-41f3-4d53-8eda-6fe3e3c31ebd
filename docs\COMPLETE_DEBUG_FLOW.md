# Complete TalonTalk Debug Flow & Commands

This document provides a comprehensive debug flow for TalonTalk that you can copy and paste directly into your terminal or browser.

## 🏃 Quick Start Debug Commands

### 1. Check Django Server Status
```bash
cd c:\Users\<USER>\Desktop\SaaS\LinguaJoy
python manage.py runserver 0.0.0.0:8000
```

### 2. Database Check Commands
```bash
# Check if database tables exist
python manage.py dbshell
.tables
.quit

# Check user data
python manage.py shell
from django.contrib.auth.models import User
from profiles.models import UserProfile
from lessons.models import Lesson, Vocabulary
from gamification.models import Badge, Achievement

# Check users
users = User.objects.all()
print(f"Total users: {users.count()}")
for user in users:
    print(f"User: {user.username} (ID: {user.id})")

# Check profiles
profiles = UserProfile.objects.all()
print(f"Total profiles: {profiles.count()}")
for profile in profiles:
    print(f"Profile: {profile.user.username} - XP: {profile.total_xp}")

# Check lessons
lessons = Lesson.objects.all()
print(f"Total lessons: {lessons.count()}")
for lesson in lessons[:5]:
    print(f"Lesson: {lesson.title} (Level: {lesson.difficulty_level})")

# Check vocabulary
vocab = Vocabulary.objects.all()
print(f"Total vocabulary: {vocab.count()}")
for word in vocab[:5]:
    print(f"Word: {word.spanish_word} -> {word.english_translation}")

exit()
```

## 🌐 Browser Debug URLs

Copy and paste these URLs directly into your browser:

### Main Pages
```
http://localhost:8000/                          # Landing page
http://localhost:8000/dashboard/                # Main dashboard
http://localhost:8000/debug-dashboard/          # Debug dashboard
http://localhost:8000/lessons/                  # Lessons list
```

### API Endpoints (Direct URLs)
```
http://localhost:8000/api/gamification/flashcard/    # GET flashcard
http://localhost:8000/api/gamification/answer/       # POST answer (use browser dev tools)
```

### Admin Panel
```
http://localhost:8000/admin/                    # Django admin
```

## 🔧 JavaScript Console Debug Commands

Open browser dev tools (F12) and paste these commands:

### Test Flashcard API
```javascript
// Test GET flashcard endpoint
fetch('/api/gamification/flashcard/', {
    method: 'GET',
    headers: {
        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        'Content-Type': 'application/json',
    },
    credentials: 'same-origin'
})
.then(response => response.json())
.then(data => {
    console.log('Flashcard API Response:', data);
    window.testFlashcard = data.flashcard; // Store for next test
})
.catch(error => console.error('Flashcard API Error:', error));
```

### Test Answer Submission
```javascript
// First run the flashcard test above, then use this:
if (window.testFlashcard) {
    fetch('/api/gamification/answer/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify({
            flashcard_id: window.testFlashcard.id,
            user_answer: "Hola",
            is_correct: true,
            similarity_score: 1.0
        })
    })
    .then(response => response.json())
    .then(data => console.log('Answer API Response:', data))
    .catch(error => console.error('Answer API Error:', error));
} else {
    console.log('Run flashcard test first to get test data');
}
```

### Test Dashboard Modal
```javascript
// Test if flashcard modal opens correctly
if (typeof openFlashcardModal === 'function') {
    console.log('✅ Flashcard modal function exists');
    // openFlashcardModal(); // Uncomment to test modal
} else {
    console.log('❌ Flashcard modal function not found');
}

// Check if required elements exist
const elements = [
    'flashcard-modal',
    'flashcard-question',
    'flashcard-options',
    'submit-answer-btn',
    'close-modal-btn'
];

elements.forEach(id => {
    const element = document.getElementById(id);
    console.log(`${element ? '✅' : '❌'} Element #${id}:`, element);
});
```

## 🐛 Common Debug Scenarios

### Scenario 1: "Dashboard not loading"
```bash
# 1. Check server is running
python manage.py runserver 0.0.0.0:8000

# 2. Check for template errors
# Look for errors in the terminal output

# 3. Test direct URL
# Go to: http://localhost:8000/dashboard/
```

### Scenario 2: "Flashcard modal not working"
```javascript
// 1. Check in browser console:
console.log('Dashboard functions:', Object.keys(window).filter(key => key.includes('flashcard')));

// 2. Check modal HTML exists:
console.log('Modal element:', document.getElementById('flashcard-modal'));

// 3. Test API directly:
fetch('/api/gamification/flashcard/')
.then(r => r.json())
.then(data => console.log('API works:', data));
```

### Scenario 3: "Lessons not displaying"
```python
# In Django shell:
python manage.py shell

from lessons.models import Lesson
from django.contrib.auth.models import User

# Check lesson data
lessons = Lesson.objects.all()
print(f"Lessons count: {lessons.count()}")

# Check if user has profile
user = User.objects.first()
if hasattr(user, 'userprofile'):
    print(f"User has profile with XP: {user.userprofile.total_xp}")
else:
    print("User has no profile - this might be the issue!")

exit()
```

### Scenario 4: "CSS not loading"
```bash
# 1. Check static files exist
ls talontalk/static/css/
# Should see: input.css, output.css

# 2. Rebuild Tailwind CSS
npx tailwindcss -i talontalk/static/css/input.css -o talontalk/static/css/output.css --watch

# 3. Check Django static settings
python manage.py collectstatic --noinput
```

## 🚀 Quick Fix Commands

### Reset Database (if needed)
```bash
rm db.sqlite3
python manage.py migrate
python manage.py createsuperuser
python manage.py shell
# Then run the data creation script from IMPLEMENTATION_SUMMARY.md
```

### Rebuild CSS
```bash
npx tailwindcss -i talontalk/static/css/input.css -o talontalk/static/css/output.css
```

### Create Test Data
```python
python manage.py shell
# Copy and paste the data creation script from IMPLEMENTATION_SUMMARY.md
```

## 📋 Debug Checklist

### ✅ Pre-Debug Checklist
- [ ] Django server is running on port 8000
- [ ] No migration warnings in terminal
- [ ] User is logged in (check /admin/)
- [ ] Static CSS files exist
- [ ] Browser dev tools open (F12)

### ✅ API Debug Checklist
- [ ] `/api/gamification/flashcard/` returns JSON
- [ ] `/api/gamification/answer/` accepts POST
- [ ] CSRF token is present in page
- [ ] User is authenticated
- [ ] No 404/500 errors in Network tab

### ✅ Frontend Debug Checklist
- [ ] Modal HTML elements exist
- [ ] JavaScript functions are defined
- [ ] No console errors
- [ ] Event listeners are attached
- [ ] CSS classes are loading

## 🔍 Advanced Debug Techniques

### Monitor Django Logs
```bash
# Add this to any view for detailed logging:
import logging
logger = logging.getLogger(__name__)
logger.debug(f"Debug info: {variable}")
```

### Monitor JavaScript Events
```javascript
// Add event monitoring to dashboard:
document.addEventListener('click', function(e) {
    if (e.target.id.includes('flashcard')) {
        console.log('Flashcard element clicked:', e.target);
    }
});
```

### Check Network Requests
1. Open browser Dev Tools (F12)
2. Go to Network tab
3. Click flashcard button
4. Check for any failed API calls
5. Look at Response tab for error details

## 🎯 Success Indicators

### ✅ Everything Working Correctly
- Dashboard loads without errors
- Flashcard modal opens when button clicked
- API returns demo flashcard data
- Answer submission returns success
- No console errors
- CSS styles loading correctly

### ❌ Common Error Patterns
- `404 Not Found` = URL routing issue
- `403 Forbidden` = CSRF/authentication issue
- `500 Internal Server Error` = Django view error
- Modal not opening = JavaScript error
- Styles not loading = CSS build/path issue

## 📞 Quick Support Commands

If you need to share debug info, run these and copy the output:

```bash
# Django version and status
python --version
python manage.py --version
python manage.py check

# Database status
python manage.py showmigrations

# Static files status
python manage.py findstatic css/output.css

# Server status (when running)
curl http://localhost:8000/api/gamification/flashcard/
```

This debug flow should cover 99% of issues you might encounter. Copy and paste the relevant sections based on what you're debugging!
