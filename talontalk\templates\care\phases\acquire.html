<!-- Acquire Phase: Learn New Concepts -->
<div class="care-phase-content acquire-phase hidden" data-phase="acquire">
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-8 text-white mb-8">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <i class="fas fa-lightbulb text-3xl"></i>
            </div>
            <div>
                <h2 class="text-3xl font-bold">Acquire</h2>
                <p class="text-blue-100">Let's learn the essential vocabulary and phrases</p>
            </div>
        </div>
        <p class="text-lg text-blue-50">Master the key language elements step by step</p>
    </div>

    <!-- Vocabulary Learning Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Essential Vocabulary -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-book text-blue-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">Essential Café Vocabulary</h3>
            </div>
            <div id="vocabularyCards" class="space-y-3">
                <!-- Dynamic vocabulary cards -->
                <div class="vocab-card p-4 border border-gray-200 rounded-lg hover:border-blue-500 transition-colors cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900">café</div>
                            <div class="text-sm text-gray-500">coffee</div>
                        </div>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                <div class="vocab-card p-4 border border-gray-200 rounded-lg hover:border-blue-500 transition-colors cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900">con leche</div>
                            <div class="text-sm text-gray-500">with milk</div>
                        </div>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                <div class="vocab-card p-4 border border-gray-200 rounded-lg hover:border-blue-500 transition-colors cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900">cortado</div>
                            <div class="text-sm text-gray-500">espresso with a little milk</div>
                        </div>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                <div class="vocab-card p-4 border border-gray-200 rounded-lg hover:border-blue-500 transition-colors cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900">croissant</div>
                            <div class="text-sm text-gray-500">croissant</div>
                        </div>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                <div class="vocab-card p-4 border border-gray-200 rounded-lg hover:border-blue-500 transition-colors cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium text-gray-900">la cuenta</div>
                            <div class="text-sm text-gray-500">the bill</div>
                        </div>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">Progress</span>
                    <span class="text-blue-600 font-medium">5/10 learned</span>
                </div>
                <div class="w-full h-2 bg-gray-200 rounded-full mt-2">
                    <div class="h-full bg-blue-600 rounded-full" style="width: 50%"></div>
                </div>
            </div>
        </div>

        <!-- Key Phrases -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-comments text-blue-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">Essential Phrases</h3>
            </div>
            <div id="phrasesCards" class="space-y-4">
                <div class="phrase-card p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-blue-800">Ordering</span>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                    <div class="text-lg font-medium text-gray-900 mb-1">Me gustaría un café, por favor</div>
                    <div class="text-sm text-gray-600">I would like a coffee, please</div>
                </div>
                <div class="phrase-card p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-blue-800">Asking for the bill</span>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                    <div class="text-lg font-medium text-gray-900 mb-1">¿Puede traerme la cuenta?</div>
                    <div class="text-sm text-gray-600">Can you bring me the bill?</div>
                </div>
                <div class="phrase-card p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-blue-800">Being polite</span>
                        <button class="play-audio text-blue-600 hover:text-blue-700">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                    <div class="text-lg font-medium text-gray-900 mb-1">Gracias y que tenga un buen día</div>
                    <div class="text-sm text-gray-600">Thank you and have a good day</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grammar Focus -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-cogs text-blue-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">Grammar Focus: Polite Requests</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Using "Me gustaría" (I would like)</h4>
                <div class="space-y-2">
                    <div class="p-3 bg-blue-50 rounded-lg">
                        <div class="font-medium text-blue-900">Me gustaría + noun</div>
                        <div class="text-sm text-blue-700">Me gustaría un café</div>
                    </div>
                    <div class="p-3 bg-blue-50 rounded-lg">
                        <div class="font-medium text-blue-900">More polite than "Quiero"</div>
                        <div class="text-sm text-blue-700">Perfect for formal situations</div>
                    </div>
                </div>
            </div>
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Adding "por favor" (please)</h4>
                <div class="space-y-2">
                    <div class="p-3 bg-green-50 rounded-lg">
                        <div class="font-medium text-green-900">Always adds politeness</div>
                        <div class="text-sm text-green-700">Un café, por favor</div>
                    </div>
                    <div class="p-3 bg-green-50 rounded-lg">
                        <div class="font-medium text-green-900">Can go at the beginning or end</div>
                        <div class="text-sm text-green-700">Por favor, un café</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Learning Activity -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-gamepad text-blue-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">Practice What You've Learned</h3>
        </div>
        <p class="text-gray-600 mb-6">Let's practice building sentences with your new vocabulary!</p>
        
        <div id="sentenceBuilder" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Build this sentence: "I would like a coffee with milk, please"</label>
                <div class="sentence-construction">
                    <div class="flex flex-wrap gap-2 mb-4 p-4 bg-gray-50 rounded-lg min-h-[60px] border-2 border-dashed border-gray-300" id="sentenceTarget">
                        <!-- Dropped words will appear here -->
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="Me">Me</div>
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="gustaría">gustaría</div>
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="un">un</div>
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="café">café</div>
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="con">con</div>
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="leche,">leche,</div>
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="por">por</div>
                        <div class="word-option px-3 py-2 bg-blue-100 text-blue-800 rounded-lg cursor-move hover:bg-blue-200 transition-colors" draggable="true" data-word="favor">favor</div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between items-center">
                <button id="checkSentence" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" disabled>
                    Check Answer
                </button>
                <button id="resetSentence" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    Reset
                </button>
            </div>
            
            <div id="sentenceResult" class="hidden p-4 rounded-lg">
                <!-- Result will be shown here -->
            </div>
        </div>
    </div>
</div>

<script>
// Acquire phase interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Vocabulary card interactions
    const vocabCards = document.querySelectorAll('.vocab-card');
    vocabCards.forEach(card => {
        card.addEventListener('click', function() {
            this.classList.toggle('border-blue-500');
            this.classList.toggle('bg-blue-50');
        });
    });

    // Audio playback simulation
    document.querySelectorAll('.play-audio').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            // Simulate audio playback
            this.innerHTML = '<i class="fas fa-pause"></i>';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-volume-up"></i>';
            }, 1000);
        });
    });

    // Sentence building drag and drop
    const wordOptions = document.querySelectorAll('.word-option');
    const sentenceTarget = document.getElementById('sentenceTarget');
    const checkButton = document.getElementById('checkSentence');
    const resetButton = document.getElementById('resetSentence');
    const resultDiv = document.getElementById('sentenceResult');
    
    let draggedElement = null;
    const correctOrder = ['Me', 'gustaría', 'un', 'café', 'con', 'leche,', 'por', 'favor'];

    wordOptions.forEach(word => {
        word.addEventListener('dragstart', function(e) {
            draggedElement = this;
            this.style.opacity = '0.5';
        });

        word.addEventListener('dragend', function(e) {
            this.style.opacity = '1';
        });

        word.addEventListener('click', function() {
            // Alternative to drag and drop - click to add
            if (this.parentElement.classList.contains('word-option') || this.parentElement === sentenceTarget.parentElement) {
                sentenceTarget.appendChild(this);
                updateSentenceCheck();
            }
        });
    });

    sentenceTarget.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('border-blue-500', 'bg-blue-50');
    });

    sentenceTarget.addEventListener('dragleave', function(e) {
        this.classList.remove('border-blue-500', 'bg-blue-50');
    });

    sentenceTarget.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('border-blue-500', 'bg-blue-50');
        if (draggedElement) {
            this.appendChild(draggedElement);
            updateSentenceCheck();
        }
    });

    function updateSentenceCheck() {
        const wordsInTarget = Array.from(sentenceTarget.children).map(el => el.dataset.word);
        checkButton.disabled = wordsInTarget.length === 0;
        
        if (wordsInTarget.length === correctOrder.length) {
            checkButton.disabled = false;
            checkButton.classList.remove('opacity-50');
        }
    }

    checkButton.addEventListener('click', function() {
        const wordsInTarget = Array.from(sentenceTarget.children).map(el => el.dataset.word);
        const isCorrect = JSON.stringify(wordsInTarget) === JSON.stringify(correctOrder);
        
        resultDiv.classList.remove('hidden');
        if (isCorrect) {
            resultDiv.className = 'p-4 rounded-lg bg-green-50 border border-green-200';
            resultDiv.innerHTML = `
                <div class="flex items-center gap-2 text-green-800">
                    <i class="fas fa-check-circle"></i>
                    <span class="font-medium">Perfect! You've built the sentence correctly.</span>
                </div>
                <p class="text-green-700 text-sm mt-1">Your sentence: "${wordsInTarget.join(' ')}"</p>
            `;
            // Enable next phase
            const nextBtn = document.getElementById('nextPhaseBtn');
            if (nextBtn) {
                nextBtn.disabled = false;
                nextBtn.classList.remove('opacity-50');
            }
        } else {
            resultDiv.className = 'p-4 rounded-lg bg-red-50 border border-red-200';
            resultDiv.innerHTML = `
                <div class="flex items-center gap-2 text-red-800">
                    <i class="fas fa-times-circle"></i>
                    <span class="font-medium">Not quite right. Try again!</span>
                </div>
                <p class="text-red-700 text-sm mt-1">Hint: Start with "Me gustaría..."</p>
            `;
        }
    });

    resetButton.addEventListener('click', function() {
        const wordsContainer = sentenceTarget.parentElement.querySelector('.flex.flex-wrap.gap-2:last-child');
        Array.from(sentenceTarget.children).forEach(word => {
            wordsContainer.appendChild(word);
        });
        resultDiv.classList.add('hidden');
        checkButton.disabled = true;
        checkButton.classList.add('opacity-50');
    });
});
</script>
