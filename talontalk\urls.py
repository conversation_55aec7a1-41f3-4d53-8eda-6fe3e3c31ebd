"""
URL configuration for talontalk project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from . import views

# Import C.A.R.E. testing views
from care_test_views import (
    care_contextualize,
    care_acquire,
    care_reinforce,
    care_extend,
)

urlpatterns = [
    path("", views.landing_view, name="landing"),
    path("dashboard/", views.dashboard_view, name="dashboard"),
    path("debug-dashboard/", views.debug_dashboard_view, name="debug_dashboard"),
    path(
        "practice/flashcards/", views.flashcard_practice_view, name="flashcard_practice"
    ),
    path("lesson/<int:lesson_id>/", views.lesson_detail_view, name="lesson_detail"),
    path("login-success/", views.login_success_handler, name="login_success"),
    path(
        "api/check-welcome-modal/",
        views.check_welcome_modal,
        name="check_welcome_modal",
    ),
    path("api/platform-stats/", views.platform_stats_api, name="platform_stats"),
    path("admin/", admin.site.urls),
    path("api/", include("users.urls")),
    path("api/", include("profiles.urls")),
    path("api/", include("lessons.urls")),
    path("api/", include("gamification.urls")),
    path("api/language/", include("lessons.language_urls")),
    path("gamification/", include("gamification.urls")),
    path("care/", include("care.urls")),
    path("accounts/", include("allauth.urls")),
    path("onboarding/", views.onboarding_view, name="onboarding"),
    path(
        "onboarding/complete/",
        views.onboarding_complete_view,
        name="onboarding_complete",
    ),
    # C.A.R.E. Framework Testing Endpoints
    path("api/care/contextualize/", care_contextualize, name="care_contextualize"),
    path("api/care/acquire/", care_acquire, name="care_acquire"),
    path("api/care/reinforce/", care_reinforce, name="care_reinforce"),
    path("api/care/extend/", care_extend, name="care_extend"),
]

if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
