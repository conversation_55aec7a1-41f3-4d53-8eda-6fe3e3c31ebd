"""
Custom Social Account Adapter for Better Error Handling
"""

from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.core.exceptions import ImmediateHttpResponse
from django.shortcuts import render
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.contrib import messages
import logging

logger = logging.getLogger(__name__)


class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    """Custom adapter to handle social authentication errors gracefully"""

    def authentication_error(
        self, request, provider_id, error=None, exception=None, extra_context=None
    ):
        """Handle authentication errors with user-friendly messages"""

        logger.error(
            f"Social auth error for provider {provider_id}: {error} | Exception: {exception}"
        )

        # Create user-friendly error messages
        error_messages = {
            "access_denied": "You cancelled the login process. Please try again if you'd like to sign in.",
            "invalid_client": "There's a configuration issue with our login system. Please try again later or contact support.",
            "redirect_uri_mismatch": "There's a technical issue with the login redirect. Please try again or contact support.",
            "invalid_request": "There was an issue with the login request. Please try again.",
            "unauthorized_client": "The login app is not properly configured. Please contact support.",
            "unsupported_response_type": "There's a technical issue with the login system. Please contact support.",
            "invalid_scope": "The login permissions are not configured correctly. Please contact support.",
            "server_error": "The authentication server is experiencing issues. Please try again later.",
            "temporarily_unavailable": "The authentication service is temporarily unavailable. Please try again later.",
        }

        # Default message
        user_message = error_messages.get(
            error,
            "An unexpected error occurred during login. Please try again or contact support.",
        )

        # Add provider-specific context
        provider_names = {
            "google": "Google",
            "facebook": "Facebook",
            "apple": "Apple",
            "github": "GitHub",
        }

        provider_name = provider_names.get(provider_id, provider_id.title())
        full_message = f"{provider_name} login failed: {user_message}"

        # Add message to Django messages framework
        messages.error(request, full_message)

        # Log detailed error for debugging
        logger.error(
            f"Social authentication failed - Provider: {provider_id}, Error: {error}, Exception: {exception}, Extra: {extra_context}"
        )

        # Redirect to login page with error context
        redirect_url = reverse("account_login")
        return HttpResponseRedirect(redirect_url)

    def pre_social_login(self, request, sociallogin):
        """Handle pre-login logic and validation"""

        # Log the attempt
        logger.info(
            f"Social login attempt: {sociallogin.account.provider} for {sociallogin.account.extra_data.get('email', 'unknown email')}"
        )

        # You can add custom validation here
        # For example, check if the email domain is allowed
        email = sociallogin.account.extra_data.get("email")
        if email:
            # Add any custom email validation logic here
            pass

    def save_user(self, request, sociallogin, form=None):
        """Customize user saving process"""

        user = super().save_user(request, sociallogin, form)

        # Log successful user creation/login
        logger.info(
            f"Social login successful: {user.email} via {sociallogin.account.provider}"
        )

        # Create profile if it doesn't exist
        try:
            from profiles.models import Profile

            profile, created = Profile.objects.get_or_create(
                user=user,
                defaults={
                    "target_language": "spanish",
                    "skill_level": "beginner",
                    "native_language": "english",
                },
            )

            if created:
                logger.info(
                    f"Created default profile for social login user: {user.email}"
                )

        except Exception as e:
            logger.error(
                f"Failed to create profile for social login user {user.email}: {e}"
            )

        return user
