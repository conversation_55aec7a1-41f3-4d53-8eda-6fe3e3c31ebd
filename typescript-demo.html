<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C.A.R.E. TypeScript Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .care-transition {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .care-phase-enter {
            opacity: 0;
            transform: translateY(20px);
        }

        .care-phase-active {
            opacity: 1;
            transform: translateY(0);
        }

        .contextualize-theme {
            background: linear-gradient(135deg, #10b981, #34d399);
        }

        .acquire-theme {
            background: linear-gradient(135deg, #3b82f6, #60a5fa);
        }

        .reinforce-theme {
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
        }

        .extend-theme {
            background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-6xl mx-auto px-4 py-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">C.A.R.E. TypeScript Demo</h1>
                        <p class="text-gray-600 mt-1">Complete TypeScript implementation with type safety</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <div class="text-sm">
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fab fa-js-square mr-1"></i> TypeScript
                            </span>
                        </div>
                        <button id="aiTutorBtn"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                            <i class="fas fa-robot mr-2"></i>AI Tutor
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="bg-white border-b">
            <div class="max-w-6xl mx-auto px-4 py-4">
                <div class="flex items-center justify-between mb-2">
                    <h2 class="text-lg font-semibold text-gray-800">Restaurant Ordering in Spanish</h2>
                    <span class="text-sm text-gray-600">Progress: <span id="progressText">25%</span></span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar"
                        class="bg-gradient-to-r from-emerald-500 to-amber-500 h-2 rounded-full transition-all duration-500"
                        style="width: 25%"></div>
                </div>
            </div>
        </div>

        <!-- Phase Navigation -->
        <div class="bg-white border-b">
            <div class="max-w-6xl mx-auto px-4">
                <nav class="flex space-x-8">
                    <button class="care-nav-item py-4 px-2 border-b-2 border-emerald-500 text-emerald-600 font-medium"
                        data-phase="contextualize">
                        <i class="fas fa-globe-americas mr-2"></i>Contextualize
                    </button>
                    <button
                        class="care-nav-item py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700"
                        data-phase="acquire">
                        <i class="fas fa-lightbulb mr-2"></i>Acquire
                    </button>
                    <button
                        class="care-nav-item py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700"
                        data-phase="reinforce">
                        <i class="fas fa-dumbbell mr-2"></i>Reinforce
                    </button>
                    <button
                        class="care-nav-item py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700"
                        data-phase="extend">
                        <i class="fas fa-rocket mr-2"></i>Extend
                    </button>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 max-w-6xl mx-auto w-full px-4 py-8">
            <!-- Phase Content Containers -->
            <div id="contextualizePhase" class="care-phase-content">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">Loading Contextualize phase...</p>
                </div>
            </div>

            <div id="acquirePhase" class="care-phase-content hidden">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">Loading Acquire phase...</p>
                </div>
            </div>

            <div id="reinforcePhase" class="care-phase-content hidden">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">Loading Reinforce phase...</p>
                </div>
            </div>

            <div id="extendPhase" class="care-phase-content hidden">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
                    <p class="mt-4 text-gray-600">Loading Extend phase...</p>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t py-6">
            <div class="max-w-6xl mx-auto px-4 text-center">
                <p class="text-gray-600">
                    <i class="fas fa-code mr-2"></i>
                    Powered by TypeScript • Built with type safety and modern JavaScript
                </p>
                <div class="mt-2 flex justify-center items-center gap-4 text-sm text-gray-500">
                    <span><i class="fas fa-check text-green-500 mr-1"></i> Type Safe</span>
                    <span><i class="fas fa-check text-green-500 mr-1"></i> Modern ES Modules</span>
                    <span><i class="fas fa-check text-green-500 mr-1"></i> Better IDE Support</span>
                    <span><i class="fas fa-check text-green-500 mr-1"></i> Compile-time Validation</span>
                </div>
            </div>
        </footer>
    </div>

    <!-- AI Tutor Modal -->
    <div id="aiTutorModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl max-w-md w-full max-h-96 flex flex-col">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold">AI Tutor (TypeScript)</h3>
                    <button id="closeTutorBtn" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="tutorChatArea" class="flex-1 p-6 overflow-y-auto">
                    <div class="tutor-message bg-blue-50 p-4 rounded-lg mb-4">
                        <p>Hello! I'm your AI tutor. I'm running on the TypeScript implementation with full type safety.
                            How can I help you today?</p>
                    </div>
                </div>
                <div class="p-6 border-t">
                    <div class="flex gap-2">
                        <input id="tutorInput" type="text" placeholder="Ask me anything..."
                            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button id="sendTutorBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSRF Token -->
    <input type="hidden" name="csrfmiddlewaretoken" value="demo-token">

    <!-- Load TypeScript Compiled JavaScript -->
    <script type="module">
        // Mock API responses for demo
        window.mockAPI = true;

        // Override fetch for demo purposes
        const originalFetch = window.fetch;
        window.fetch = async function (url, options) {
            if (url.includes('/care/api/phase/') && window.mockAPI) {
                // Simulate API delay
                await new Promise(resolve => setTimeout(resolve, 500));

                const phase = url.split('/')[4]; // Extract phase name
                const mockContent = {
                    contextualize: {
                        scenario: {
                            title: "Spanish Café Experience",
                            description: "You're visiting a cozy café in Barcelona's Gothic Quarter. The morning rush is starting, and you want to order your favorite coffee and pastry while practicing authentic Spanish interactions.",
                            location: "Barcelona, Spain"
                        },
                        cultural_context: {
                            title: "Spanish Café Culture",
                            facts: [
                                "Spanish cafés open early, around 6-7 AM",
                                "Locals often stand at the bar for quick coffee",
                                "Tipping is appreciated but not mandatory (5-10%)",
                                "Coffee is typically strong and served in small cups"
                            ]
                        },
                        key_phrases: [
                            {
                                spanish: "Buenos días",
                                english: "Good morning",
                                pronunciation: "BWAY-nohs DEE-ahs"
                            },
                            {
                                spanish: "Un café con leche, por favor",
                                english: "A coffee with milk, please",
                                pronunciation: "oon kah-FEH kon LEH-cheh por fah-VOR"
                            },
                            {
                                spanish: "¿Cuánto cuesta?",
                                english: "How much does it cost?",
                                pronunciation: "KWAN-toh KWES-tah"
                            }
                        ]
                    },
                    acquire: {
                        vocabulary: [
                            {
                                word: "café",
                                translation: "coffee",
                                pronunciation: "kah-FEH",
                                example: "Me gusta el café fuerte",
                                example_translation: "I like strong coffee"
                            },
                            {
                                word: "leche",
                                translation: "milk",
                                pronunciation: "LEH-cheh",
                                example: "Café con leche es muy popular",
                                example_translation: "Coffee with milk is very popular"
                            },
                            {
                                word: "azúcar",
                                translation: "sugar",
                                pronunciation: "ah-SOO-kar",
                                example: "¿Quiere azúcar?",
                                example_translation: "Do you want sugar?"
                            }
                        ],
                        grammar: {
                            topic: "Polite Requests",
                            structures: [
                                {
                                    pattern: "Por favor + [request]",
                                    meaning: "Please + [request]",
                                    examples: [
                                        "Un café, por favor",
                                        "La cuenta, por favor"
                                    ]
                                }
                            ]
                        }
                    },
                    reinforce: {
                        exercises: [
                            {
                                type: "multiple_choice",
                                question: "How do you say 'Good morning' in Spanish?",
                                options: ["Buenos días", "Buenas tardes", "Buenas noches"],
                                correct: 0,
                                explanation: "Buenos días is used for morning greetings until around noon."
                            },
                            {
                                type: "translation",
                                question: "Translate: 'A coffee with milk, please'",
                                answer: "un café con leche, por favor",
                                explanation: "This is a common order in Spanish cafés."
                            },
                            {
                                type: "pronunciation",
                                phrase: "¿Cuánto cuesta?",
                                translation: "How much does it cost?",
                                phonetic: "KWAN-toh KWES-tah"
                            }
                        ]
                    },
                    extend: {
                        real_world_applications: [
                            {
                                title: "Café Ordering Challenge",
                                description: "Practice ordering different drinks and snacks",
                                scenario: "Local café in Madrid",
                                tasks: [
                                    "Order a cortado and croissant",
                                    "Ask for the price",
                                    "Pay and say thank you"
                                ]
                            }
                        ],
                        expansion_topics: [
                            {
                                topic: "Spanish Breakfast Items",
                                vocabulary: ["tostada", "croissant", "magdalena"],
                                phrases: [
                                    "Para desayunar",
                                    "Con mantequilla",
                                    "Sin azúcar"
                                ]
                            }
                        ],
                        homework: {
                            title: "Café Visit Assignment",
                            description: "Visit a local Spanish-speaking café or restaurant",
                            steps: [
                                "Practice the phrases at home",
                                "Visit a café",
                                "Order using Spanish phrases",
                                "Reflect on the experience"
                            ]
                        }
                    }
                };

                return {
                    ok: true,
                    json: async () => ({
                        success: true,
                        content: mockContent[phase] || {}
                    })
                };
            }

            return originalFetch.call(this, url, options);
        };

        console.log('🚀 Loading TypeScript C.A.R.E. implementation...');
    </script>

    <!-- Load the compiled TypeScript -->
    <script type="module" src="./talontalk/static/js/dist/care/CARELessonManager.js"></script>
</body>

</html>