"""
Advanced Quality Engine for TalonTalk
Multi-layered content validation and quality assurance system
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

from django.core.cache import cache
from django.db import transaction

from .language_manager import LanguageManager
from .models import ContentItem, UserLearningProfile

logger = logging.getLogger(__name__)


class QualityLevel(Enum):
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    REJECTED = "rejected"


class ValidationSeverity(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class QualityIssue:
    """Represents a quality issue found in content"""

    issue_type: str
    severity: ValidationSeverity
    description: str
    suggestion: Optional[str] = None
    auto_fixable: bool = False
    field_name: Optional[str] = None

    def to_dict(self) -> Dict:
        return {
            "type": self.issue_type,
            "severity": self.severity.value,
            "description": self.description,
            "suggestion": self.suggestion,
            "auto_fixable": self.auto_fixable,
            "field": self.field_name,
        }


@dataclass
class QualityReport:
    """Comprehensive quality assessment report"""

    content_id: str
    overall_score: float
    quality_level: QualityLevel
    issues: List[QualityIssue] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    auto_fixes_applied: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict:
        return {
            "content_id": self.content_id,
            "overall_score": self.overall_score,
            "quality_level": self.quality_level.value,
            "issues": [issue.to_dict() for issue in self.issues],
            "metrics": self.metrics,
            "recommendations": self.recommendations,
            "auto_fixes_applied": self.auto_fixes_applied,
            "timestamp": self.timestamp.isoformat(),
        }


class AdvancedQualityEngine:
    """
    Multi-layered quality validation system for TalonTalk content
    """

    def __init__(self):
        self.language_manager = LanguageManager()
        self.validation_rules = self._load_validation_rules()
        self.quality_thresholds = {
            QualityLevel.EXCELLENT: 90,
            QualityLevel.GOOD: 75,
            QualityLevel.ACCEPTABLE: 60,
            QualityLevel.POOR: 40,
            QualityLevel.REJECTED: 0,
        }

    def _load_validation_rules(self) -> Dict:
        """Load language-specific validation rules"""
        return {
            "spanish": {
                "question_patterns": [
                    r"¿.*\?",  # Proper Spanish question format
                    r"¿Cómo.*\?",
                    r"¿Qué.*\?",
                    r"¿Cuál.*\?",
                    r"¿Dónde.*\?",
                ],
                "forbidden_patterns": [
                    r"What.*means.*\?",  # English in Spanish content
                    r"How.*do.*you.*say.*\?",  # English questions
                    r"^[A-Z][a-z]*$",  # Single English words
                ],
                "required_elements": {
                    "accents": ["á", "é", "í", "ó", "ú", "ñ"],
                    "articles": ["el", "la", "los", "las", "un", "una"],
                },
                "grammar_rules": {
                    "gender_agreement": True,
                    "verb_conjugation": True,
                    "accent_marks": True,
                },
            },
            "french": {
                "question_patterns": [
                    r"Qu'est-ce que.*\?",
                    r"Comment.*\?",
                    r"Où.*\?",
                    r"Quand.*\?",
                ],
                "forbidden_patterns": [
                    r"What.*means.*\?",
                    r"How.*do.*you.*say.*\?",
                ],
                "required_elements": {
                    "accents": ["à", "é", "è", "ê", "ç", "ô"],
                    "articles": ["le", "la", "les", "un", "une", "des"],
                },
                "grammar_rules": {
                    "gender_agreement": True,
                    "liaison": True,
                    "accent_marks": True,
                },
            },
            "german": {
                "question_patterns": [
                    r"Was.*\?",
                    r"Wie.*\?",
                    r"Wo.*\?",
                    r"Wann.*\?",
                ],
                "forbidden_patterns": [
                    r"What.*means.*\?",
                    r"How.*do.*you.*say.*\?",
                ],
                "required_elements": {
                    "umlauts": ["ä", "ö", "ü", "ß"],
                    "articles": ["der", "die", "das", "ein", "eine"],
                },
                "grammar_rules": {
                    "case_system": True,
                    "capitalization": True,
                    "compound_words": True,
                },
            },
        }

    def validate_content(
        self, content: Dict, language: str = "spanish"
    ) -> QualityReport:
        """
        Comprehensive content validation with multi-layer checks
        """
        content_id = content.get("id", "unknown")
        report = QualityReport(
            content_id=content_id, overall_score=0.0, quality_level=QualityLevel.POOR
        )

        try:
            # Layer 1: Basic Structure Validation
            self._validate_structure(content, report)

            # Layer 2: Language-Specific Validation
            self._validate_language_rules(content, language, report)

            # Layer 3: Educational Quality Validation
            self._validate_educational_quality(content, report)

            # Layer 4: C.A.R.E. Framework Validation
            self._validate_care_framework(content, report)

            # Layer 5: Accessibility and UX Validation
            self._validate_accessibility(content, report)

            # Calculate overall quality score
            report.overall_score = self._calculate_quality_score(report)
            report.quality_level = self._determine_quality_level(report.overall_score)

            # Generate recommendations
            report.recommendations = self._generate_recommendations(report)

            # Apply auto-fixes if possible
            self._apply_auto_fixes(content, report)

        except Exception as e:
            logger.error(f"Quality validation failed for content {content_id}: {e}")
            report.issues.append(
                QualityIssue(
                    issue_type="validation_error",
                    severity=ValidationSeverity.CRITICAL,
                    description=f"Quality validation failed: {str(e)}",
                )
            )
            report.quality_level = QualityLevel.REJECTED

        return report

    def _validate_structure(self, content: Dict, report: QualityReport) -> None:
        """Validate basic content structure"""
        required_fields = ["question", "content_type", "language"]

        for field in required_fields:
            if not content.get(field):
                report.issues.append(
                    QualityIssue(
                        issue_type="missing_field",
                        severity=ValidationSeverity.CRITICAL,
                        description=f"Required field '{field}' is missing",
                        field_name=field,
                        auto_fixable=False,
                    )
                )

        # Validate content type specific fields
        content_type = content.get("content_type", "")
        if content_type == "multiple_choice":
            if not content.get("options") or len(content.get("options", [])) < 2:
                report.issues.append(
                    QualityIssue(
                        issue_type="insufficient_options",
                        severity=ValidationSeverity.HIGH,
                        description="Multiple choice questions need at least 2 options",
                        field_name="options",
                    )
                )

            if not content.get("correct_answer"):
                report.issues.append(
                    QualityIssue(
                        issue_type="missing_answer",
                        severity=ValidationSeverity.CRITICAL,
                        description="Multiple choice questions must have a correct answer",
                        field_name="correct_answer",
                    )
                )

    def _validate_language_rules(
        self, content: Dict, language: str, report: QualityReport
    ) -> None:
        """Validate language-specific rules"""
        if language not in self.validation_rules:
            report.issues.append(
                QualityIssue(
                    issue_type="unsupported_language",
                    severity=ValidationSeverity.HIGH,
                    description=f"Language '{language}' validation rules not available",
                )
            )
            return

        rules = self.validation_rules[language]
        question = content.get("question", "")

        # Check question patterns
        if rules.get("question_patterns"):
            pattern_match = any(
                re.search(pattern, question, re.IGNORECASE)
                for pattern in rules["question_patterns"]
            )
            if not pattern_match and question:
                report.issues.append(
                    QualityIssue(
                        issue_type="invalid_question_format",
                        severity=ValidationSeverity.MEDIUM,
                        description=f"Question doesn't follow {language} question patterns",
                        suggestion=f"Use proper {language} question format",
                        field_name="question",
                    )
                )

        # Check forbidden patterns
        if rules.get("forbidden_patterns"):
            for pattern in rules["forbidden_patterns"]:
                if re.search(pattern, question, re.IGNORECASE):
                    report.issues.append(
                        QualityIssue(
                            issue_type="forbidden_pattern",
                            severity=ValidationSeverity.HIGH,
                            description=f"Content contains forbidden pattern: {pattern}",
                            suggestion=f"Remove English text from {language} content",
                            field_name="question",
                        )
                    )

    def _validate_educational_quality(
        self, content: Dict, report: QualityReport
    ) -> None:
        """Validate educational effectiveness"""
        question = content.get("question", "")

        # Check question length
        if len(question) < 10:
            report.issues.append(
                QualityIssue(
                    issue_type="question_too_short",
                    severity=ValidationSeverity.MEDIUM,
                    description="Question is too short to be educational",
                    suggestion="Provide more context in the question",
                )
            )
        elif len(question) > 200:
            report.issues.append(
                QualityIssue(
                    issue_type="question_too_long",
                    severity=ValidationSeverity.LOW,
                    description="Question might be too long for learners",
                    suggestion="Consider breaking into smaller parts",
                )
            )

        # Check for explanation
        if not content.get("explanation"):
            report.issues.append(
                QualityIssue(
                    issue_type="missing_explanation",
                    severity=ValidationSeverity.MEDIUM,
                    description="Content lacks educational explanation",
                    suggestion="Add explanation to help learners understand",
                )
            )

    def _validate_care_framework(self, content: Dict, report: QualityReport) -> None:
        """Validate C.A.R.E. framework compliance"""
        care_phase = content.get("care_phase", "")

        if not care_phase:
            report.issues.append(
                QualityIssue(
                    issue_type="missing_care_phase",
                    severity=ValidationSeverity.HIGH,
                    description="Content not assigned to C.A.R.E. phase",
                    suggestion="Assign content to appropriate C.A.R.E. phase",
                )
            )
            return

        # Validate phase-specific requirements
        phase_requirements = {
            "contextualize": ["cultural_context", "real_world_scenario"],
            "acquire": ["progressive_difficulty", "clear_learning_objective"],
            "reinforce": ["repetition_element", "spaced_review"],
            "extend": ["creative_application", "personalization"],
        }

        if care_phase in phase_requirements:
            for requirement in phase_requirements[care_phase]:
                if not content.get(requirement):
                    report.issues.append(
                        QualityIssue(
                            issue_type=f"missing_{requirement}",
                            severity=ValidationSeverity.MEDIUM,
                            description=f"Missing {requirement} for {care_phase} phase",
                            suggestion=f"Add {requirement} to align with C.A.R.E. framework",
                        )
                    )

    def _validate_accessibility(self, content: Dict, report: QualityReport) -> None:
        """Validate accessibility and UX considerations"""
        # Check for alt text on images
        if content.get("image_url") and not content.get("image_alt_text"):
            report.issues.append(
                QualityIssue(
                    issue_type="missing_alt_text",
                    severity=ValidationSeverity.MEDIUM,
                    description="Image missing alt text for accessibility",
                    suggestion="Add descriptive alt text for images",
                    auto_fixable=True,
                )
            )

        # Check for pronunciation guide
        if not content.get("pronunciation_guide"):
            report.issues.append(
                QualityIssue(
                    issue_type="missing_pronunciation",
                    severity=ValidationSeverity.LOW,
                    description="Missing pronunciation guide",
                    suggestion="Add pronunciation guide for better learning",
                )
            )

    def _calculate_quality_score(self, report: QualityReport) -> float:
        """Calculate overall quality score based on issues"""
        base_score = 100.0

        severity_penalties = {
            ValidationSeverity.CRITICAL: 25,
            ValidationSeverity.HIGH: 15,
            ValidationSeverity.MEDIUM: 8,
            ValidationSeverity.LOW: 3,
            ValidationSeverity.INFO: 1,
        }

        for issue in report.issues:
            penalty = severity_penalties.get(issue.severity, 5)
            base_score -= penalty

        return max(0.0, min(100.0, base_score))

    def _determine_quality_level(self, score: float) -> QualityLevel:
        """Determine quality level based on score"""
        for level, threshold in self.quality_thresholds.items():
            if score >= threshold:
                return level
        return QualityLevel.REJECTED

    def _generate_recommendations(self, report: QualityReport) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []

        critical_issues = [
            i for i in report.issues if i.severity == ValidationSeverity.CRITICAL
        ]
        if critical_issues:
            recommendations.append("Fix critical issues before publishing content")

        if report.overall_score < 60:
            recommendations.append("Content needs significant improvement before use")
        elif report.overall_score < 80:
            recommendations.append(
                "Consider improving content quality for better learning outcomes"
            )

        # Add specific suggestions from issues
        for issue in report.issues:
            if issue.suggestion and issue.suggestion not in recommendations:
                recommendations.append(issue.suggestion)

        return recommendations[:5]  # Limit to top 5 recommendations

    def _apply_auto_fixes(self, content: Dict, report: QualityReport) -> None:
        """Apply automatic fixes where possible"""
        auto_fixes_applied = []

        for issue in report.issues:
            if issue.auto_fixable:
                if issue.issue_type == "missing_alt_text" and content.get("image_url"):
                    content["image_alt_text"] = "Learning content image"
                    auto_fixes_applied.append("Added default alt text")

        report.auto_fixes_applied = auto_fixes_applied


class QualityMonitor:
    """
    Quality monitoring and analytics system
    """

    def __init__(self):
        self.quality_engine = AdvancedQualityEngine()

    def get_quality_metrics(self, time_period: int = 7) -> Dict:
        """Get quality metrics for the specified time period (days)"""
        from django.utils import timezone

        end_date = timezone.now()
        start_date = end_date - timedelta(days=time_period)

        # Get recent content items
        recent_content = ContentItem.objects.filter(
            created_at__gte=start_date, created_at__lte=end_date
        )

        metrics = {
            "total_content": recent_content.count(),
            "quality_distribution": {},
            "common_issues": {},
            "language_breakdown": {},
            "average_quality_score": 0.0,
            "improvement_suggestions": [],
        }

        if not recent_content.exists():
            return metrics

        # Analyze quality distribution
        quality_scores = []
        issue_counts = {}
        language_counts = {}

        for content_item in recent_content:
            content_dict = {
                "id": str(content_item.id),
                "question": content_item.question,
                "content_type": content_item.content_type,
                "language": content_item.language,
                "care_phase": content_item.tags[0] if content_item.tags else "acquire",
            }

            # Add optional fields
            if hasattr(content_item, "options") and content_item.options:
                content_dict["options"] = content_item.options
            if hasattr(content_item, "correct_answer") and content_item.correct_answer:
                content_dict["correct_answer"] = content_item.correct_answer
            if hasattr(content_item, "explanation") and content_item.explanation:
                content_dict["explanation"] = content_item.explanation

            report = self.quality_engine.validate_content(
                content_dict, content_item.language
            )
            quality_scores.append(report.overall_score)

            # Count issues
            for issue in report.issues:
                issue_type = issue.issue_type
                issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1

            # Count languages
            lang = content_item.language
            language_counts[lang] = language_counts.get(lang, 0) + 1

            # Quality level distribution
            level = report.quality_level.value
            metrics["quality_distribution"][level] = (
                metrics["quality_distribution"].get(level, 0) + 1
            )

        # Calculate averages and summaries
        if quality_scores:
            metrics["average_quality_score"] = sum(quality_scores) / len(quality_scores)

        # Top 5 most common issues
        metrics["common_issues"] = dict(
            sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        )

        metrics["language_breakdown"] = language_counts

        # Generate improvement suggestions
        metrics["improvement_suggestions"] = self._generate_improvement_suggestions(
            metrics["common_issues"], metrics["average_quality_score"]
        )

        return metrics

    def _generate_improvement_suggestions(
        self, common_issues: Dict, avg_score: float
    ) -> List[str]:
        """Generate system-wide improvement suggestions"""
        suggestions = []

        if avg_score < 70:
            suggestions.append(
                "Overall content quality is below acceptable threshold. Review content generation prompts."
            )

        # Issue-specific suggestions
        issue_suggestions = {
            "missing_explanation": "Add explanation templates to content generation",
            "invalid_question_format": "Improve language-specific question formatting rules",
            "missing_care_phase": "Ensure all content is properly tagged with C.A.R.E. phases",
            "question_too_short": "Set minimum question length requirements",
            "forbidden_pattern": "Strengthen language mixing detection in prompts",
        }

        for issue_type, count in common_issues.items():
            if issue_type in issue_suggestions and count > 5:
                suggestions.append(
                    f"{issue_suggestions[issue_type]} (affects {count} items)"
                )

        return suggestions[:3]  # Top 3 suggestions

    def generate_quality_report(self, language: Optional[str] = None) -> Dict:
        """Generate comprehensive quality report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "metrics": self.get_quality_metrics(),
            "language_specific": {},
            "recommendations": [],
        }

        if language:
            # Language-specific analysis
            language_content = ContentItem.objects.filter(language=language)
            report["language_specific"][language] = {
                "total_content": language_content.count(),
                "active_support": LanguageManager.is_language_active(language),
                "content_quality_rating": LanguageManager.get_language_info(
                    language, {}
                ).get("content_quality", "unknown"),
            }

        return report
