/**
 * Type definitions for the TalonTalk C.A.R.E. Framework
 */
// ============================================================================
// Error Classes (exported as classes, not types)
// ============================================================================
export class CAREError extends Error {
    constructor(message, code, phase, cause) {
        super(message);
        this.code = code;
        this.phase = phase;
        this.cause = cause;
        this.name = 'CAREError';
    }
}
export class APIError extends CAREError {
    constructor(message, statusCode, endpoint, cause) {
        super(message, 'API_ERROR', undefined, cause);
        this.statusCode = statusCode;
        this.endpoint = endpoint;
        this.name = 'APIError';
    }
}
export class ValidationError extends CAREError {
    constructor(message, field, cause) {
        super(message, 'VALIDATION_ERROR', undefined, cause);
        this.field = field;
        this.name = 'ValidationError';
    }
}
//# sourceMappingURL=care.types.js.map