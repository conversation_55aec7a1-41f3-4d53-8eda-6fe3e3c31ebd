"""
C.A.R.E. Framework Views
Pedagogical framework implementation using dynamic content
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.conf import settings
from django.utils import timezone
import json
import requests
import logging
from lessons.models import Lesson, UserLessonProgress

# Simplified imports - quality engine runs in background
from lessons.language_manager import LanguageManager

# Initialize logger
logger = logging.getLogger(__name__)


def care_lesson_view(request, lesson_id=None):
    """
    Main C.A.R.E. lesson view that renders the interactive lesson template
    """
    # Get lesson_id from URL parameter or query parameter
    if not lesson_id:
        lesson_id = request.GET.get("lesson_id", 1)

    # Convert to int
    try:
        lesson_id = int(lesson_id)
    except (ValueError, TypeError):
        lesson_id = 1

    # Get the real lesson from database
    try:
        lesson = get_object_or_404(Lesson, id=lesson_id, is_active=True)
        print(f"✅ Found lesson: {lesson.title} (ID: {lesson.id})")
    except Exception as e:
        print(f"❌ Error finding lesson {lesson_id}: {e}")
        # Fallback to first available lesson
        lesson = Lesson.objects.filter(is_active=True).first()
        if lesson:
            print(f"✅ Using fallback lesson: {lesson.title} (ID: {lesson.id})")
        else:
            # Create a mock lesson object only if no lessons exist
            class MockLesson:
                def __init__(self):
                    self.id = 1
                    self.title = "Basic Greetings"
                    self.description = (
                        "Learn essential Spanish greetings and polite expressions"
                    )
                    self.difficulty_level = 1
                    self.language = "spanish"

            lesson = MockLesson()
            print(f"⚠️ Using mock lesson: {lesson.title}")

    # Get user progress (only for authenticated users)
    progress = None
    if (
        request.user.is_authenticated
        and hasattr(lesson, "id")
        and isinstance(lesson.id, int)
    ):
        try:
            progress, _ = UserLessonProgress.objects.get_or_create(
                user=request.user,
                lesson=lesson,
                defaults={"completed": False, "xp_earned": 0},
            )
        except Exception as e:
            print(f"⚠️ Could not create progress for user: {e}")
            progress = None

    context = {
        "lesson": lesson,
        "progress": progress,
        "lesson_id": lesson_id,
        "user": request.user,
    }

    return render(request, "care/lesson.html", context)


@require_http_methods(["GET"])
def care_phase_data(request, phase):
    """
    API endpoint to get dynamic AI-generated data for each C.A.R.E. phase
    Uses the content quality gateway for high-quality educational content
    """
    # Simple validation
    valid_phases = ["contextualize", "acquire", "reinforce", "extend"]
    if phase not in valid_phases:
        return JsonResponse({"success": False, "error": "Invalid phase"}, status=400)

    lesson_id = request.GET.get("lesson", "current_events")
    topic = request.GET.get("topic", "daily_conversation")
    difficulty = int(request.GET.get("difficulty", "1"))

    # Get user's preferred language
    user_language = (
        LanguageManager.get_user_language(request.user)
        if request.user.is_authenticated
        else "spanish"
    )

    try:
        # Import content generation services
        from lessons.content_quality_gateway import (
            ContentQualityGateway,
            ContentGenerationRequest,
        )
        from ai_services.llm_flashcards import LLMFlashcardService
        from ai_services.llm_config import get_recommended_config

        # Initialize the quality gateway with LLM service
        llm_service = LLMFlashcardService(get_recommended_config())
        gateway = ContentQualityGateway(llm_service)

        # Create content generation request for this specific phase
        request_data = ContentGenerationRequest(
            user_id=request.user.id if request.user.is_authenticated else 0,
            content_type="flashcard",  # Default content type
            target_language=user_language,
            difficulty_level=str(difficulty),
            topic=topic,
            context=f"C.A.R.E. {phase} phase for {lesson_id}",
            quantity=_get_batch_size_for_phase(phase),
            session_type="focused_practice",
        )

        # Generate content using the quality gateway
        result = gateway.generate_quality_content(request_data)

        if result.success and result.content:
            # Transform the generated content into the format expected by the frontend
            formatted_content = _format_content_for_phase(phase, result.content, topic)

            return JsonResponse(
                {
                    "success": True,
                    "content": formatted_content,
                    "quality_score": result.quality_score,
                    "generation_time": result.generation_time,
                    "issues_fixed": len(result.issues_fixed),
                }
            )
        else:
            # Fallback to basic content if generation fails
            fallback_content = _get_fallback_content_for_phase(
                phase, topic, user_language
            )
            return JsonResponse(
                {
                    "success": True,
                    "content": fallback_content,
                    "fallback_used": True,
                    "error": "Content generation failed, using fallback",
                }
            )

    except Exception as e:
        logger.error(f"Error generating C.A.R.E. content for {phase}: {e}")
        # Emergency fallback
        fallback_content = _get_fallback_content_for_phase(phase, topic, user_language)
        return JsonResponse(
            {
                "success": True,
                "content": fallback_content,
                "fallback_used": True,
                "error": str(e),
            }
        )


@require_http_methods(["POST"])
@csrf_exempt
def care_submit_answer(request, phase):
    """
    Handle answer submissions for each C.A.R.E. phase
    Simplified for better user experience
    """
    try:
        # Simple validation
        valid_phases = ["contextualize", "acquire", "reinforce", "extend"]
        if phase not in valid_phases:
            return JsonResponse(
                {"success": False, "error": "Invalid phase"}, status=400
            )

        data = json.loads(request.body)
        answer = data.get("answer")
        question_id = data.get("question_id")

        # Basic validation
        if not answer or not question_id:
            return JsonResponse({"error": "Missing answer or question_id"}, status=400)

        # For now, use simple correct/incorrect logic
        # In a real implementation, this would validate against the correct answer
        is_correct = True  # Placeholder - replace with real validation

        # Generate feedback based on phase
        feedback = generate_phase_feedback(phase, is_correct, answer)

        return JsonResponse(
            {
                "success": True,
                "correct": is_correct,
                "feedback": feedback,
                "next_phase": get_next_phase(phase) if is_correct else None,
            }
        )

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON"}, status=400)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


def generate_phase_feedback(phase, is_correct, answer):
    """Generate contextual feedback based on the C.A.R.E. phase"""

    feedback_templates = {
        "contextualize": {
            "correct": "Excellent! You've understood the context. Now let's learn some key concepts.",
            "incorrect": "Not quite. Let's review the scenario again and focus on the key details.",
        },
        "acquire": {
            "correct": "Great job learning this new concept! Let's practice it now.",
            "incorrect": "Don't worry, this is new material. Let's try a different approach.",
        },
        "reinforce": {
            "correct": "Perfect! You're really getting the hang of this.",
            "incorrect": "Good attempt! Practice makes perfect - let's try again.",
        },
        "extend": {
            "correct": "Outstanding! You've applied your knowledge creatively.",
            "incorrect": "Interesting attempt! Let's think about how to apply what we learned.",
        },
    }

    feedback_type = "correct" if is_correct else "incorrect"
    return feedback_templates.get(phase, {}).get(feedback_type, "Good effort!")


def get_next_phase(current_phase):
    """Get the next phase in the C.A.R.E. sequence"""
    phase_sequence = ["contextualize", "acquire", "reinforce", "extend"]

    try:
        current_index = phase_sequence.index(current_phase)
        if current_index < len(phase_sequence) - 1:
            return phase_sequence[current_index + 1]
    except ValueError:
        pass

    return None  # No next phase or invalid current phase


@require_http_methods(["GET"])
def care_ai_tutor_chat(request):
    """
    AI Tutor chat interface for contextual help
    """
    user_message = request.GET.get("message", "")
    lesson_id = request.GET.get("lesson", "restaurant_ordering")
    phase = request.GET.get("phase", "contextualize")

    if not user_message:
        return JsonResponse({"error": "No message provided"}, status=400)

    # Generate AI tutor response (placeholder - integrate with actual LLM)
    tutor_response = generate_tutor_response(user_message, lesson_id, phase)

    return JsonResponse(
        {
            "success": True,
            "response": tutor_response,
            "phase": phase,
            "lesson": lesson_id,
        }
    )


def generate_tutor_response(user_message, lesson_id, phase):
    """
    Generate AI tutor response based on context
    This is a placeholder - integrate with actual LLM service
    """

    phase_contexts = {
        "contextualize": "I'm here to help you understand the scenario and context.",
        "acquire": "Let me help you learn these new concepts step by step.",
        "reinforce": "Great question! Let's practice this together.",
        "extend": "I love seeing you apply your knowledge creatively!",
    }

    context = phase_contexts.get(phase, "I'm here to help!")

    # Simple response generation (replace with actual LLM integration)
    if "how" in user_message.lower():
        return f"{context} Here's how you can approach this..."
    elif "what" in user_message.lower():
        return f"{context} What you're looking for is..."
    elif "why" in user_message.lower():
        return f"{context} The reason is..."
    else:
        return f"{context} That's a great question! Let me help you with that."


@require_http_methods(["GET"])
def care_lesson_list(request):
    """
    Get list of available C.A.R.E. lessons
    """
    # For now, return static lesson data
    # In production, this would query the database

    lessons = [
        {
            "id": "restaurant_ordering",
            "title": "Restaurant Ordering in Spanish",
            "description": "Learn to order food and drinks confidently",
            "difficulty": "beginner",
            "estimated_time": "15 minutes",
            "completed": False,
        },
        {
            "id": "hotel_checkin",
            "title": "Hotel Check-in Conversations",
            "description": "Master hotel vocabulary and phrases",
            "difficulty": "beginner",
            "estimated_time": "20 minutes",
            "completed": False,
        },
        {
            "id": "shopping_market",
            "title": "Shopping at Local Markets",
            "description": "Navigate markets and bargain effectively",
            "difficulty": "intermediate",
            "estimated_time": "25 minutes",
            "completed": False,
        },
    ]

    return JsonResponse({"success": True, "lessons": lessons})


# Helper functions for C.A.R.E. content generation


def _get_content_types_for_phase(phase):
    """Get appropriate content types for each C.A.R.E. phase"""
    phase_content_types = {
        "contextualize": ["scenario", "cultural_context", "dialogue"],
        "acquire": ["flashcard", "vocabulary", "grammar"],
        "reinforce": ["mcq", "translation", "fill_blank"],
        "extend": ["role_play", "creative_writing", "real_world_task"],
    }
    return phase_content_types.get(phase, ["flashcard"])


def _get_batch_size_for_phase(phase):
    """Get appropriate batch size for each C.A.R.E. phase"""
    phase_batch_sizes = {
        "contextualize": 3,  # Scenario, context, key phrases
        "acquire": 5,  # Multiple vocabulary/grammar items
        "reinforce": 4,  # Several practice exercises
        "extend": 2,  # Fewer but more complex tasks
    }
    return phase_batch_sizes.get(phase, 3)


def _format_content_for_phase(phase, content_items, topic):
    """Format generated content for frontend consumption"""
    if not content_items:
        return {}

    if phase == "contextualize":
        return _format_contextualize_content(content_items, topic)
    elif phase == "acquire":
        return _format_acquire_content(content_items, topic)
    elif phase == "reinforce":
        return _format_reinforce_content(content_items, topic)
    elif phase == "extend":
        return _format_extend_content(content_items, topic)
    else:
        return {"error": f"Unknown phase: {phase}"}


def _format_contextualize_content(content_items, topic):
    """Format content for Contextualize phase"""
    formatted = {
        "scenario": {
            "title": f"Learning Context: {topic.title()}",
            "description": "Real-world scenario to practice your Spanish skills",
            "situation": "Interactive learning environment",
        },
        "cultural_context": {"title": "Cultural Insights", "facts": []},
        "key_phrases": [],
    }

    for item in content_items:
        if hasattr(item, "question_text") and hasattr(item, "answer_text"):
            formatted["key_phrases"].append(
                {
                    "spanish": item.question_text,
                    "english": item.answer_text,
                    "pronunciation": getattr(item, "hint_text", ""),
                }
            )

        if hasattr(item, "explanation_text") and item.explanation_text:
            formatted["cultural_context"]["facts"].append(item.explanation_text)

    return formatted


def _format_acquire_content(content_items, topic):
    """Format content for Acquire phase"""
    formatted = {
        "vocabulary": [],
        "grammar": {"topic": f"Grammar for {topic.title()}", "structures": []},
    }

    for item in content_items:
        if hasattr(item, "question_text") and hasattr(item, "answer_text"):
            vocab_item = {
                "word": item.question_text,
                "translation": item.answer_text,
                "pronunciation": getattr(item, "hint_text", ""),
                "example": getattr(item, "explanation_text", ""),
                "example_translation": "",
            }
            formatted["vocabulary"].append(vocab_item)

    return formatted


def _format_reinforce_content(content_items, topic):
    """Format content for Reinforce phase"""
    formatted = {"exercises": []}

    for item in content_items:
        if hasattr(item, "question_text") and hasattr(item, "answer_text"):
            exercise = {
                "type": "multiple_choice",
                "question": item.question_text,
                "options": getattr(
                    item, "choices_json", [item.answer_text, "Option 2", "Option 3"]
                ),
                "correct": 0,
                "explanation": getattr(item, "explanation_text", ""),
            }
            formatted["exercises"].append(exercise)

    return formatted


def _format_extend_content(content_items, topic):
    """Format content for Extend phase"""
    formatted = {
        "real_world_applications": [],
        "expansion_topics": [],
        "homework": {
            "title": f"Practice Assignment: {topic.title()}",
            "description": "Apply your learning in real situations",
            "steps": [],
        },
    }

    for item in content_items:
        if hasattr(item, "question_text"):
            application = {
                "title": f"Real-world Practice: {topic.title()}",
                "description": item.question_text,
                "tasks": [
                    getattr(item, "explanation_text", "Practice the new vocabulary")
                ],
            }
            formatted["real_world_applications"].append(application)

    return formatted


def _get_fallback_content_for_phase(phase, topic, language):
    """Provide fallback content when generation fails"""
    fallback_content = {
        "contextualize": {
            "scenario": {
                "title": f"Learning {topic.title()}",
                "description": f"Practice {language} in real situations",
                "situation": "Interactive learning",
            },
            "key_phrases": [
                {"spanish": "Hola", "english": "Hello", "pronunciation": "OH-lah"}
            ],
        },
        "acquire": {
            "vocabulary": [
                {
                    "word": "hola",
                    "translation": "hello",
                    "pronunciation": "OH-lah",
                    "example": "Hola, ¿cómo estás?",
                    "example_translation": "Hello, how are you?",
                }
            ]
        },
        "reinforce": {
            "exercises": [
                {
                    "type": "multiple_choice",
                    "question": "How do you say 'hello' in Spanish?",
                    "options": ["Hola", "Adiós", "Gracias", "Por favor"],
                    "correct": 0,
                    "explanation": "Hola is the most common greeting in Spanish",
                }
            ]
        },
        "extend": {
            "real_world_applications": [
                {
                    "title": "Practice Greetings",
                    "description": "Use Spanish greetings in daily life",
                    "tasks": ["Greet someone in Spanish today"],
                }
            ]
        },
    }

    return fallback_content.get(phase, {"error": "No fallback available"})
