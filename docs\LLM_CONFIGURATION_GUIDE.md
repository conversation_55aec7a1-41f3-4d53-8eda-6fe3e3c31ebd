# LLM Configuration Guide for TalonTalk Language Learning Platform

## Overview

This document provides a comprehensive guide to the LLM (Large Language Model) configuration system used in the TalonTalk language learning platform. The system is designed to support multiple AI providers with automatic fallback logic, prioritizing local models for cost efficiency and reliability.

## Supported Providers

### 1. Ollama (Local Models) - **RECOMMENDED**
- **Cost**: Free (local inference)
- **Reliability**: High (no API limits)
- **Performance**: Good (depends on hardware)
- **Setup**: Requires Ollama installation

### 2. OpenRouter
- **Cost**: Free tier available
- **Reliability**: High
- **Performance**: Excellent
- **Setup**: Requires API key

### 3. DeepSeek
- **Cost**: Very low ($0.00014/1K tokens)
- **Reliability**: High
- **Performance**: Excellent reasoning
- **Setup**: Requires API key

### 4. OpenAI
- **Cost**: Moderate ($0.00015-0.0015/1K tokens)
- **Reliability**: Very high
- **Performance**: Excellent
- **Setup**: Requires API key

### 5. Anthropic
- **Cost**: Low-moderate ($0.00025/1K tokens)
- **Reliability**: Very high
- **Performance**: Excellent
- **Setup**: Requires API key

## Model Selection Logic

The system uses the following priority order for automatic model selection:

1. **Local Ollama Models** (if available):
   - llama3.1:8b (best overall performance)
   - qwen2.5:7b (fastest inference)
   - mistral:7b (optimized for flashcards)
   - deepseek-r1:8b (best reasoning)

2. **Cloud Fallbacks**:
   - OpenRouter free tier
   - DeepSeek (if API key available)
   - OpenAI (if API key available)
   - Demo mode (no API key)

## Model Configurations

### Ollama Local Models

#### LLaMA 3.1 8B (Primary Choice)
```yaml
name: llama3.1:8b
cost: Free
max_tokens: 8192
multilingual_quality: 8/10
reasoning_quality: 8/10
description: Excellent instruction following and general performance
```

#### Qwen 2.5 7B (Fastest)
```yaml
name: qwen2.5:7b
cost: Free
max_tokens: 8192
multilingual_quality: 9/10
reasoning_quality: 8/10
description: Fastest inference with excellent multilingual support
```

#### Mistral 7B (Flashcard Optimized)
```yaml
name: mistral:7b
cost: Free
max_tokens: 8192
multilingual_quality: 8/10
reasoning_quality: 7/10
description: Perfect for flashcards and language learning tasks
```

#### DeepSeek R1 8B (Reasoning)
```yaml
name: deepseek-r1:8b
cost: Free
max_tokens: 8192
multilingual_quality: 9/10
reasoning_quality: 9/10
description: Excellent reasoning capabilities
```

### Cloud Provider Models

#### OpenRouter Free Tier
```yaml
name: mistralai/mistral-7b-instruct:free
cost: Free
max_tokens: 8192
multilingual_quality: 8/10
reasoning_quality: 7/10
description: Free Mistral model with excellent language learning capabilities
```

#### DeepSeek Chat
```yaml
name: deepseek-chat
cost: $0.00014/1K tokens
max_tokens: 4096
multilingual_quality: 8/10
reasoning_quality: 9/10
description: Excellent reasoning at very low cost
```

#### OpenAI GPT-4o Mini
```yaml
name: gpt-4o-mini
cost: $0.00015/1K tokens
max_tokens: 128000
multilingual_quality: 9/10
reasoning_quality: 9/10
description: Excellent quality at low cost
```

## Setup Instructions

### 1. Ollama Setup (Recommended)

#### Install Ollama
```powershell
# Download from https://ollama.ai
# Or use package manager
winget install Ollama.Ollama
```

#### Pull Recommended Models
```powershell
# Primary choice - best overall
ollama pull llama3.1:8b

# Alternative options
ollama pull qwen2.5:7b    # Fastest
ollama pull mistral:7b    # Flashcard optimized
ollama pull deepseek-r1:8b # Best reasoning
```

#### Verify Installation
```powershell
ollama list
```

### 2. Cloud Provider Setup

#### Environment Variables
Create a `.env` file in your project root:

```env
# Choose one or more providers
OPENROUTER_API_KEY=your_openrouter_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
```

## Usage Examples

### Basic Usage
```python
from ai_services.llm_config import get_recommended_config

# Get the best available configuration
config = get_recommended_config()
print(f"Using: {config.provider.value} - {config.model_name}")
```

### Manual Configuration
```python
from ai_services.llm_config import LLMConfig, LLMProvider

# Use specific provider
config = LLMConfig(LLMProvider.OLLAMA, "llama")

# Use with custom settings
config = LLMConfig(
    provider=LLMProvider.OPENROUTER,
    model_tier="free",
    api_key="your_key_here"
)
```

### API Client Setup
```python
from openai import OpenAI

config = get_recommended_config()

client = OpenAI(
    api_key=config.api_key,
    base_url=config.base_url
)

response = client.chat.completions.create(
    model=config.model_name,
    messages=[{"role": "user", "content": "Generate a Spanish flashcard"}],
    max_tokens=config.model_config.max_tokens
)
```

## Performance Characteristics

### Local Models (Ollama)
- **Pros**: Free, no API limits, private, consistent performance
- **Cons**: Requires local setup, hardware dependent
- **Best for**: Development, testing, production with good hardware

### Cloud Models
- **Pros**: No local setup, consistent performance, latest models
- **Cons**: API costs, rate limits, internet dependency
- **Best for**: Production with high volume, accessing latest models

## Cost Analysis

### Daily Usage Estimate (1000 flashcards)
- **Ollama**: $0 (Free)
- **DeepSeek**: ~$0.14
- **OpenRouter Free**: $0 (with limits)
- **OpenAI GPT-4o Mini**: ~$0.15
- **Anthropic Claude**: ~$0.25

### Monthly Cost (30,000 flashcards)
- **Ollama**: $0
- **DeepSeek**: ~$4.20
- **OpenAI**: ~$4.50
- **Anthropic**: ~$7.50

## Quality Ratings for Language Learning

### Multilingual Support (1-10)
1. **Qwen 2.5**: 9/10 - Excellent multilingual capabilities
2. **DeepSeek R1**: 9/10 - Strong multilingual support
3. **Claude**: 9/10 - Excellent language understanding
4. **GPT-4o Mini**: 9/10 - Very good multilingual
5. **LLaMA 3.1**: 8/10 - Good multilingual support
6. **Mistral**: 8/10 - Solid multilingual performance

### Reasoning Quality (1-10)
1. **DeepSeek R1**: 9/10 - Exceptional reasoning
2. **Claude**: 9/10 - Excellent reasoning
3. **GPT-4o Mini**: 9/10 - Very strong reasoning
4. **LLaMA 3.1**: 8/10 - Good reasoning
5. **Qwen 2.5**: 8/10 - Solid reasoning
6. **Mistral**: 7/10 - Adequate reasoning

## Troubleshooting

### Ollama Issues

#### Connection Failed
```powershell
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Restart Ollama service
ollama serve
```

#### Model Not Found
```powershell
# List available models
ollama list

# Pull missing model
ollama pull llama3.1:8b
```

### API Key Issues

#### Invalid Key Error
1. Check environment variable is set correctly
2. Verify key has proper permissions
3. Check for whitespace in key

#### Rate Limit Errors
1. Implement exponential backoff
2. Switch to local models
3. Upgrade API plan

## Best Practices

### Model Selection
1. **Development**: Use Ollama for free, unlimited testing
2. **Production**: Use Ollama for cost efficiency, cloud for backup
3. **High Volume**: Consider DeepSeek for cost-effectiveness
4. **Quality Critical**: Use Claude or GPT-4o Mini

### Error Handling
```python
try:
    config = get_recommended_config()
    # Use config for API calls
except Exception as e:
    # Fallback to basic configuration
    print(f"Config error: {e}")
    config = LLMConfig(LLMProvider.OPENROUTER, "free")
```

### Monitoring
- Track token usage for cost management
- Monitor response times for performance
- Log model selection decisions for debugging

## Future Enhancements

### Planned Features
1. **Automatic model switching** based on performance
2. **Load balancing** across multiple providers
3. **Cost optimization** algorithms
4. **A/B testing** for model comparison
5. **Caching layer** for repeated requests

### Model Upgrades
- Monitor for new Ollama models
- Evaluate new cloud provider options
- Test emerging language learning specific models

## Configuration File Structure

The main configuration is in `ai_services/llm_config.py`:

```
ai_services/
├── __init__.py
├── llm_config.py      # Main configuration file
├── llm_flashcards.py  # Flashcard generation logic
├── api.py             # API endpoints
└── requirements.txt   # Dependencies
```

## Dependencies

```txt
openai>=1.0.0          # OpenAI-compatible client
requests>=2.25.0       # For Ollama API checks
python-dotenv>=0.19.0  # Environment variable loading
```

## Security Notes

### API Keys
- Never commit API keys to version control
- Use environment variables or secure vaults
- Rotate keys regularly
- Monitor usage for anomalies

### Local Models
- Ollama runs locally, no data leaves your machine
- Models are stored in local filesystem
- No internet required for inference

## Contact and Support

For issues with this configuration system:
1. Check the troubleshooting section above
2. Review logs in `ai_services/` directory
3. Test with `test_ai_setup.py` script
4. Consult the TalonTalk development team

---

*Last updated: July 5, 2025*
*Version: 2.0*
