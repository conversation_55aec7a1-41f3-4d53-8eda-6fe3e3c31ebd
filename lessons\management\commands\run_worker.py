"""
Django management command to run RQ workers for background tasks
"""

from django.core.management.base import BaseCommand
import django_rq


class Command(BaseCommand):
    help = "Start RQ worker for processing background tasks"

    def add_arguments(self, parser):
        parser.add_argument(
            "--queue",
            type=str,
            choices=["default", "content_high", "content_normal", "content_low"],
            default="content_normal",
            help="Queue to process",
        )
        parser.add_argument(
            "--burst",
            action="store_true",
            help="Run in burst mode (process existing jobs and exit)",
        )

    def handle(self, *args, **options):
        queue_name = options["queue"]
        burst_mode = options["burst"]

        self.stdout.write(
            self.style.SUCCESS(f"🔄 Starting RQ worker for queue: {queue_name}")
        )

        if burst_mode:
            self.stdout.write(
                self.style.WARNING(
                    "⚡ Running in burst mode (will exit after processing existing jobs)"
                )
            )

        try:
            # Get the queue
            queue = django_rq.get_queue(queue_name)

            # Get the worker
            worker = django_rq.get_worker(queue_name)

            self.stdout.write(
                self.style.SUCCESS(f"✅ Worker started for queue: {queue_name}")
            )
            self.stdout.write(f"📊 Queue length: {len(queue)}")

            # Start the worker
            worker.work(burst=burst_mode)

        except KeyboardInterrupt:
            self.stdout.write(self.style.SUCCESS("\n🛑 Worker stopped by user"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Worker error: {e}"))
