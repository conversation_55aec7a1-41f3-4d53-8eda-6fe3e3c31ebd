#!/usr/bin/env python
"""
Test script for the new Structured Prompting Framework
Tests the FETCH → FORMAT → EXECUTE pattern
"""

import os
import sys
import django
import json

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()

from django.contrib.auth import get_user_model
from lessons.structured_prompting import (
    StructuredLearningFramework,
    StructuredPromptBuilder,
)
from lessons.models import Lesson, ContentItem, UserContentPerformance

User = get_user_model()  # Use the correct user model


def test_structured_framework():
    """Test the structured prompting framework"""

    print("🧪 TESTING STRUCTURED PROMPTING FRAMEWORK")
    print("=" * 50)

    # Get or create a test user
    try:
        user = User.objects.get(username="testuser")
        print(f"📱 Using existing test user: {user.username}")
    except User.DoesNotExist:
        print("⚠️  No test user found. Using first available user...")
        user = User.objects.first()
        if not user:
            print("❌ No users found. Create a user first.")
            return

    # Test 1: Check if we can build contexts
    print(f"\n1️⃣ Testing Context Building...")

    builder = StructuredPromptBuilder()

    # Check available lessons
    lessons = Lesson.objects.all()
    print(f"📚 Available lessons: {lessons.count()}")

    if lessons.exists():
        lesson = lessons.first()
        print(f"🎯 Testing with lesson: {lesson.title}")

        try:
            # Test focused practice context
            context = builder.build_context(
                "focused_practice", user, lesson_id=lesson.id
            )
            print(f"✅ Focused practice context built successfully")
            print(f"   - Content items: {len(context.content_items)}")
            print(f"   - Metadata: {list(context.metadata.keys())}")

            # Print sample content
            if context.content_items:
                sample_item = context.content_items[0]
                print(
                    f"   - Sample item: {sample_item.get('text_es', 'N/A')} = {sample_item.get('text_en', 'N/A')}"
                )

        except Exception as e:
            print(f"❌ Error building focused practice context: {e}")

    # Test 2: Check adaptive review context
    print(f"\n2️⃣ Testing Adaptive Review Context...")

    try:
        context = builder.build_context("adaptive_review", user)
        print(f"✅ Adaptive review context built successfully")
        print(f"   - Weak items found: {len(context.content_items)}")

        if context.content_items:
            for i, item in enumerate(context.content_items[:2]):  # Show first 2
                proficiency = item.get("proficiency", "N/A")
                print(
                    f"   - Weak spot {i+1}: {item.get('text_es', 'N/A')} (proficiency: {proficiency})"
                )
        else:
            print("   - No performance data found (expected for new users)")

    except Exception as e:
        print(f"❌ Error building adaptive review context: {e}")

    # Test 3: Test conversation context
    print(f"\n3️⃣ Testing Conversation Context...")

    try:
        context = builder.build_context("conversation_practice", user)
        print(f"✅ Conversation context built successfully")
        print(f"   - Mastered items: {len(context.content_items)}")
        print(f"   - Scenario: {context.metadata.get('scenario', 'N/A')}")

    except Exception as e:
        print(f"❌ Error building conversation context: {e}")

    # Test 4: Mock LLM test (without actually calling AI)
    print(f"\n4️⃣ Testing Framework Integration...")

    class MockLLMService:
        def generate_content(self, prompt):
            return json.dumps(
                {
                    "session_type": "focused_practice",
                    "questions": [
                        {
                            "question_text": "How do you say 'Hello' in Spanish?",
                            "options": ["Hola", "Adiós", "Gracias", "Por favor"],
                            "correct_answer": "Hola",
                            "explanation": "Hola is the most common greeting in Spanish",
                        }
                    ],
                }
            )

    try:
        mock_llm = MockLLMService()
        framework = StructuredLearningFramework(mock_llm)

        if lessons.exists():
            result = framework.generate_learning_session(
                "focused_practice", user, lesson_id=lessons.first().id
            )

            print(f"✅ Framework integration test successful")
            print(f"   - Result type: {result.get('session_type', 'N/A')}")
            print(f"   - Questions generated: {len(result.get('questions', []))}")
        else:
            print("⚠️  No lessons available for framework test")

    except Exception as e:
        print(f"❌ Framework integration test failed: {e}")

    # Test 5: Check database requirements
    print(f"\n5️⃣ Database Requirements Check...")

    content_items = ContentItem.objects.all()
    performance_records = UserContentPerformance.objects.filter(user=user)

    print(f"📊 Content items in database: {content_items.count()}")
    print(f"📊 User performance records: {performance_records.count()}")

    if content_items.count() == 0:
        print("⚠️  No content items found. The framework needs content to work with.")
        print("   Consider running: python manage.py populate_sample_content")

    if performance_records.count() == 0:
        print("⚠️  No performance data found. Adaptive features will be limited.")
        print("   Performance data is created as users practice.")

    print(f"\n🏁 STRUCTURED FRAMEWORK TEST COMPLETE")
    print("=" * 50)

    print("\n💡 NEXT STEPS:")
    print("1. Test the API endpoints:")
    print("   POST /api/structured-lesson/")
    print("   POST /api/adaptive-flashcards/")
    print("2. Integrate with frontend components")
    print("3. Monitor LLM quality and performance")
    print("4. Add more session types as needed")


if __name__ == "__main__":
    test_structured_framework()
