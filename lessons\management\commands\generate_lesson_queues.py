"""
Django Management Command for Content Preloading
===============================================

This command generates personalized lesson queues for users.
Can be run manually, via cron, or triggered by Django-RQ.

Usage:
    python manage.py generate_lesson_queues --all
    python manage.py generate_lesson_queues --user-id 123
    python manage.py generate_lesson_queues --queue-type daily
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import logging

User = get_user_model()

from lessons.models import (
    ContentItem,
    UserContentPerformance,
    UserLearningProfile,
    UserLessonQueue,
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Generate personalized lesson queues for users"

    def add_arguments(self, parser):
        parser.add_argument(
            "--all",
            action="store_true",
            help="Generate queues for all active users",
        )
        parser.add_argument(
            "--user-id",
            type=int,
            help="Generate queue for specific user ID",
        )
        parser.add_argument(
            "--queue-type",
            type=str,
            default="daily",
            choices=["daily", "review", "weak_areas", "new_content"],
            help="Type of queue to generate",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force regeneration even if queue already exists",
        )
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Verbose output",
        )

    def handle(self, *args, **options):
        self.verbose = options["verbose"]

        if options["all"]:
            self.generate_all_queues(options["queue_type"], options["force"])
        elif options["user_id"]:
            self.generate_user_queue(
                options["user_id"], options["queue_type"], options["force"]
            )
        else:
            raise CommandError("Must specify either --all or --user-id")

    def generate_all_queues(self, queue_type, force=False):
        """Generate queues for all active users"""
        cutoff_date = timezone.now() - timedelta(days=7)
        active_users = User.objects.filter(last_login__gte=cutoff_date, is_active=True)

        self.stdout.write(f"Found {active_users.count()} active users")

        success_count = 0
        error_count = 0

        for user in active_users:
            try:
                result = self.generate_user_queue(
                    user.id, queue_type, force, silent=True
                )
                if result:
                    success_count += 1
                    if self.verbose:
                        self.stdout.write(f"✓ Generated queue for user {user.id}")
                else:
                    if self.verbose:
                        self.stdout.write(f"- Skipped user {user.id} (queue exists)")
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f"✗ Error for user {user.id}: {str(e)}")
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"Completed: {success_count} success, {error_count} errors"
            )
        )

    def generate_user_queue(self, user_id, queue_type, force=False, silent=False):
        """Generate queue for a specific user"""
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise CommandError(f"User {user_id} not found")

        # Check if queue already exists and is not expired
        existing_queue = UserLessonQueue.objects.filter(
            user=user, queue_type=queue_type, expires_at__gt=timezone.now()
        ).first()

        if existing_queue and not force:
            if not silent:
                self.stdout.write(f"Queue already exists for user {user_id}")
            return False

        # Get or create user learning profile
        profile, created = UserLearningProfile.objects.get_or_create(user=user)

        # Generate content based on queue type
        if queue_type == "daily":
            content_ids = self._generate_daily_content(user, profile)
        elif queue_type == "review":
            content_ids = self._generate_review_content(user, profile)
        elif queue_type == "weak_areas":
            content_ids = self._generate_weak_areas_content(user, profile)
        elif queue_type == "new_content":
            content_ids = self._generate_new_content(user, profile)
        else:
            content_ids = self._generate_daily_content(user, profile)

        # Remove old queues of the same type
        UserLessonQueue.objects.filter(user=user, queue_type=queue_type).delete()

        # Create new queue
        expires_at = timezone.now() + timedelta(hours=24)
        queue = UserLessonQueue.objects.create(
            user=user,
            ordered_content_ids=content_ids,
            queue_type=queue_type,
            total_items=len(content_ids),
            expires_at=expires_at,
        )

        if not silent:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Generated {queue_type} queue for user {user_id}: {len(content_ids)} items"
                )
            )

        return True

    def _generate_daily_content(self, user, profile):
        """Generate balanced daily content"""
        target_count = 20  # ~30 minutes

        review_content = self._get_review_content(user, count=5)
        weak_areas_content = self._get_weak_areas_content(user, count=5)
        new_content = self._get_new_content(user, profile, count=10)

        all_content = review_content + weak_areas_content + new_content
        return [item.id for item in all_content[:target_count]]

    def _generate_review_content(self, user, profile):
        """Generate spaced repetition content"""
        review_items = self._get_review_content(user, count=15)
        return [item.id for item in review_items]

    def _generate_weak_areas_content(self, user, profile):
        """Generate content for weak areas"""
        weak_items = self._get_weak_areas_content(user, count=15)
        return [item.id for item in weak_items]

    def _generate_new_content(self, user, profile):
        """Generate new content"""
        new_items = self._get_new_content(user, profile, count=15)
        return [item.id for item in new_items]

    def _get_review_content(self, user, count=10):
        """Get content due for review"""
        performance_records = (
            UserContentPerformance.objects.filter(
                user=user, next_review_date__lte=timezone.now()
            )
            .select_related("content_item")
            .order_by("next_review_date")[:count]
        )

        return [
            perf.content_item
            for perf in performance_records
            if perf.content_item.is_active
        ]

    def _get_weak_areas_content(self, user, count=10):
        """Get content for weak areas"""
        weak_performances = (
            UserContentPerformance.objects.filter(
                user=user, proficiency_score__lt=0.5, times_seen__gte=2
            )
            .select_related("content_item")
            .order_by("proficiency_score")[:count]
        )

        return [
            perf.content_item
            for perf in weak_performances
            if perf.content_item.is_active
        ]

    def _get_new_content(self, user, profile, count=10):
        """Get new content for user"""
        seen_content_ids = UserContentPerformance.objects.filter(user=user).values_list(
            "content_item_id", flat=True
        )

        difficulty = profile.preferred_difficulty
        new_content = (
            ContentItem.objects.filter(
                is_active=True,
                difficulty__lte=difficulty + 1,
                language="spanish",  # TODO: Make dynamic
            )
            .exclude(id__in=seen_content_ids)
            .order_by("difficulty", "?")[:count]
        )

        return list(new_content)
