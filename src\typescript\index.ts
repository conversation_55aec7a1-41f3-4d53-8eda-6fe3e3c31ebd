/**
 * Main entry point for TalonTalk TypeScript modules
 */

// Export main components
export { CARELessonManager } from './care/CARELessonManager.js';

// Export types
export type * from './types/care.types.js';
export type * from './types/common.types.js';

// Export utilities
export * from './utils/index.js';

// Version information
export const VERSION = '1.0.0';
export const BUILD_DATE = new Date().toISOString();
