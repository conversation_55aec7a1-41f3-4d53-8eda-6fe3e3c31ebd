/**
 * Simplified C.A.R.E. Framework Interactive Lesson System
 * Connects to the new lesson content API and provides working functionality
 */

class CARELessonManagerSimple {
    constructor() {
        this.currentPhase = 'contextualize';
        this.phases = ['contextualize', 'acquire', 'reinforce', 'extend'];
        this.lessonId = this.getLessonId();
        this.lessonData = null;
        
        this.init();
    }

    init() {
        console.log('🚀 Initializing C.A.R.E. Lesson Manager...');
        this.setupEventListeners();
        this.loadLessonData();
    }

    getLessonId() {
        const lessonElement = document.querySelector('[data-lesson-id]');
        return lessonElement?.dataset.lessonId || '1';
    }

    setupEventListeners() {
        // Phase navigation
        document.querySelectorAll('.care-nav-item').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const phase = e.target.dataset.phase || e.target.closest('.care-nav-item').dataset.phase;
                if (phase) {
                    this.loadPhase(phase);
                }
            });
        });

        // Phase indicators
        document.querySelectorAll('.care-phase-indicator').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const phase = e.target.dataset.phase || e.target.closest('.care-phase-indicator').dataset.phase;
                if (phase) {
                    this.loadPhase(phase);
                }
            });
        });
    }

    async loadLessonData() {
        try {
            console.log(`📡 Loading lesson data for lesson ${this.lessonId}...`);
            
            const response = await fetch(`/api/lessons/${this.lessonId}/care-content/`);
            const data = await response.json();
            
            if (data.care_phases) {
                this.lessonData = data;
                console.log('✅ Lesson data loaded successfully');
                this.loadPhase(this.currentPhase);
            } else {
                console.error('❌ Failed to load lesson data');
                this.showError('Failed to load lesson content');
            }
        } catch (error) {
            console.error('❌ Error loading lesson data:', error);
            this.showError('Network error loading lesson');
        }
    }

    loadPhase(phaseName) {
        console.log(`🔄 Loading phase: ${phaseName}`);
        
        // Update UI state
        this.updatePhaseNavigation(phaseName);
        this.currentPhase = phaseName;
        
        // Show phase content
        this.showPhaseContent(phaseName);
        
        // Load phase-specific content
        if (this.lessonData && this.lessonData.care_phases[phaseName]) {
            this.renderPhaseContent(phaseName, this.lessonData.care_phases[phaseName]);
        } else {
            this.showLoadingContent(phaseName);
        }
    }

    updatePhaseNavigation(phaseName) {
        // Update navigation active states
        document.querySelectorAll('.care-nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.phase === phaseName) {
                item.classList.add('active');
            }
        });

        // Update phase indicators
        document.querySelectorAll('.care-phase-indicator').forEach(indicator => {
            indicator.classList.remove('active');
            if (indicator.dataset.phase === phaseName) {
                indicator.classList.add('active');
            }
        });
    }

    showPhaseContent(phaseName) {
        // Hide all phase content
        document.querySelectorAll('.care-phase-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Show current phase content
        const phaseContent = document.getElementById(`${phaseName}Phase`);
        if (phaseContent) {
            phaseContent.classList.remove('hidden');
        }
    }

    renderPhaseContent(phaseName, phaseData) {
        console.log(`🎨 Rendering ${phaseName} content:`, phaseData);
        
        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (!phaseContainer) {
            console.error(`❌ Phase container not found: ${phaseName}Phase`);
            return;
        }

        const contentHTML = this.generatePhaseHTML(phaseName, phaseData);
        phaseContainer.innerHTML = contentHTML;
        
        // Setup event listeners for interactive content
        this.setupContentInteractions(phaseContainer);
    }

    generatePhaseHTML(phaseName, phaseData) {
        const title = phaseData.title || this.getPhaseTitle(phaseName);
        const description = phaseData.description || this.getPhaseDescription(phaseName);
        const content = phaseData.content || [];
        const color = this.getPhaseColor(phaseName);

        let contentHTML = '';
        
        if (content.length > 0) {
            content.forEach((item, index) => {
                contentHTML += this.generateContentItemHTML(item, index, color);
            });
        } else {
            contentHTML = this.generateEmptyStateHTML(phaseName, color);
        }

        return `
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <div class="bg-gradient-to-r ${color.gradient} text-white p-6">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="${color.icon} text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold">${title}</h2>
                            <p class="${color.textLight}">${description}</p>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    ${contentHTML}
                </div>
            </div>
        `;
    }

    generateContentItemHTML(item, index, color) {
        return `
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-start gap-3">
                    <div class="w-8 h-8 ${color.bg} text-white rounded-full flex items-center justify-center text-sm font-bold">
                        ${index + 1}
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-2">${item.type.charAt(0).toUpperCase() + item.type.slice(1)} Practice</h4>
                        <div class="bg-white p-4 rounded-lg border">
                            <p class="text-gray-800 mb-3">${item.question}</p>
                            ${this.generateAnswerSection(item, color)}
                            ${item.hint ? `
                                <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <p class="text-sm text-yellow-800"><strong>Hint:</strong> ${item.hint}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateAnswerSection(item, color) {
        if (item.choices && item.choices.length > 0) {
            return `
                <div class="space-y-2">
                    ${item.choices.map((choice, i) => `
                        <button class="w-full text-left p-3 rounded-lg border hover:bg-blue-50 transition-colors choice-btn" 
                                data-answer="${choice}" data-correct="${choice === item.answer}">
                            ${String.fromCharCode(65 + i)}. ${choice}
                        </button>
                    `).join('')}
                </div>
            `;
        } else {
            return `
                <div class="mt-3">
                    <input type="text" class="w-full p-3 border rounded-lg answer-input" 
                           placeholder="Type your answer here..." data-correct="${item.answer}">
                    <button class="mt-2 px-4 py-2 ${color.bg} text-white rounded-lg hover:${color.bgHover} check-answer-btn">
                        Check Answer
                    </button>
                </div>
            `;
        }
    }

    generateEmptyStateHTML(phaseName, color) {
        return `
            <div class="text-center py-8">
                <div class="w-16 h-16 ${color.bgLight} rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="${color.icon} ${color.text} text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Welcome to ${this.getPhaseTitle(phaseName)}!</h3>
                <p class="text-gray-600 mb-6">${this.getPhaseDescription(phaseName)}</p>
                <button class="px-6 py-3 ${color.bg} text-white rounded-lg hover:${color.bgHover} transition-colors next-phase-btn">
                    Start Learning <i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        `;
    }

    setupContentInteractions(container) {
        // Multiple choice buttons
        container.querySelectorAll('.choice-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleChoiceClick(e.target);
            });
        });

        // Check answer buttons
        container.querySelectorAll('.check-answer-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleAnswerCheck(e.target);
            });
        });

        // Next phase buttons
        container.querySelectorAll('.next-phase-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.nextPhase();
            });
        });
    }

    handleChoiceClick(button) {
        const isCorrect = button.dataset.correct === 'true';
        const container = button.closest('.bg-white');
        
        // Remove previous feedback
        container.querySelectorAll('.choice-btn').forEach(btn => {
            btn.classList.remove('bg-green-100', 'bg-red-100', 'border-green-500', 'border-red-500');
        });

        if (isCorrect) {
            button.classList.add('bg-green-100', 'border-green-500');
            this.showFeedback(container, 'Correct! Well done! 🎉', 'success');
        } else {
            button.classList.add('bg-red-100', 'border-red-500');
            this.showFeedback(container, 'Not quite right. Try again! 🤔', 'error');
        }
    }

    handleAnswerCheck(button) {
        const input = button.parentElement.querySelector('.answer-input');
        const userAnswer = input.value.trim().toLowerCase();
        const correctAnswer = input.dataset.correct.toLowerCase();
        const container = button.closest('.bg-white');

        if (userAnswer === correctAnswer) {
            input.classList.add('border-green-500');
            this.showFeedback(container, 'Excellent! That\'s correct! 🎉', 'success');
        } else {
            input.classList.add('border-red-500');
            this.showFeedback(container, `Close! The correct answer is: ${input.dataset.correct}`, 'error');
        }
    }

    showFeedback(container, message, type) {
        // Remove existing feedback
        const existingFeedback = container.querySelector('.feedback-message');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        const feedbackClass = type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800';
        const feedbackHTML = `
            <div class="feedback-message mt-3 p-3 ${feedbackClass} border rounded-lg">
                <p class="text-sm font-medium">${message}</p>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', feedbackHTML);
    }

    nextPhase() {
        const currentIndex = this.phases.indexOf(this.currentPhase);
        if (currentIndex < this.phases.length - 1) {
            const nextPhase = this.phases[currentIndex + 1];
            this.loadPhase(nextPhase);
        } else {
            this.showLessonComplete();
        }
    }

    showLessonComplete() {
        alert('🎉 Congratulations! You\'ve completed this lesson!');
    }

    // Helper methods for phase styling
    getPhaseColor(phaseName) {
        const colors = {
            contextualize: {
                gradient: 'from-emerald-500 to-emerald-600',
                bg: 'bg-emerald-500',
                bgHover: 'bg-emerald-600',
                bgLight: 'bg-emerald-100',
                text: 'text-emerald-600',
                textLight: 'text-emerald-100',
                icon: 'fas fa-globe-americas'
            },
            acquire: {
                gradient: 'from-blue-500 to-blue-600',
                bg: 'bg-blue-500',
                bgHover: 'bg-blue-600',
                bgLight: 'bg-blue-100',
                text: 'text-blue-600',
                textLight: 'text-blue-100',
                icon: 'fas fa-book-open'
            },
            reinforce: {
                gradient: 'from-purple-500 to-purple-600',
                bg: 'bg-purple-500',
                bgHover: 'bg-purple-600',
                bgLight: 'bg-purple-100',
                text: 'text-purple-600',
                textLight: 'text-purple-100',
                icon: 'fas fa-dumbbell'
            },
            extend: {
                gradient: 'from-orange-500 to-orange-600',
                bg: 'bg-orange-500',
                bgHover: 'bg-orange-600',
                bgLight: 'bg-orange-100',
                text: 'text-orange-600',
                textLight: 'text-orange-100',
                icon: 'fas fa-rocket'
            }
        };
        return colors[phaseName] || colors.contextualize;
    }

    getPhaseTitle(phaseName) {
        const titles = {
            contextualize: 'Contextualize',
            acquire: 'Acquire',
            reinforce: 'Reinforce',
            extend: 'Extend'
        };
        return titles[phaseName] || 'Learning Phase';
    }

    getPhaseDescription(phaseName) {
        const descriptions = {
            contextualize: 'Set the scene for your learning journey',
            acquire: 'Learn new vocabulary and concepts',
            reinforce: 'Practice and strengthen your knowledge',
            extend: 'Apply your knowledge in new contexts'
        };
        return descriptions[phaseName] || 'Continue your learning journey';
    }

    showLoadingContent(phaseName) {
        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (phaseContainer) {
            phaseContainer.innerHTML = `
                <div class="text-center py-12">
                    <div class="loading-spinner border-4 border-gray-300 border-t-blue-500 rounded-full w-8 h-8 mx-auto mb-4 animate-spin"></div>
                    <p class="text-gray-600">Loading ${phaseName} content...</p>
                </div>
            `;
        }
    }

    showError(message) {
        console.error('❌ Error:', message);
        // Could show a user-friendly error message here
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Simple C.A.R.E. Lesson Manager...');
    window.careManager = new CARELessonManagerSimple();
});

// Make it globally available
window.CARELessonManagerSimple = CARELessonManagerSimple;
