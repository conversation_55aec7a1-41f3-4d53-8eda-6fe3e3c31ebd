"""
Content Pipeline Service for TalonTalk Language Learning
Manages automated content generation, spaced repetition, and adaptive learning
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q, Count, Avg

from lessons.models import (
    ContentItem,
    Lesson,
    UserContentPerformance,
    UserLearningProfile,
    UserLessonQueue,
)
from ai_services.llm_flashcards import DifficultyLevel
from .content_generator import ContentGeneratorService

# Import background task services
try:
    from .spaced_repetition import SpacedRepetitionService
    from .background_tasks import BackgroundTaskService

    BACKGROUND_TASKS_AVAILABLE = True
except ImportError:
    BACKGROUND_TASKS_AVAILABLE = False


logger = logging.getLogger(__name__)


class ContentPipelineService:
    """
    Manages the complete content pipeline with spaced repetition and adaptive learning
    """

    def __init__(self):
        self.content_generator = ContentGeneratorService()

        # Initialize background services if available
        if BACKGROUND_TASKS_AVAILABLE:
            self.spaced_repetition = SpacedRepetitionService()
            self.background_tasks = BackgroundTaskService()
        else:
            self.spaced_repetition = None
            self.background_tasks = None

    def generate_user_lesson_queue(
        self, user, target_items: int = 10, focus_areas: Optional[List[str]] = None
    ) -> Dict:
        """
        Generate personalized lesson queue using spaced repetition and adaptive learning

        Args:
            user: User object
            target_items: Number of items to include in queue
            focus_areas: Optional list of focus areas/topics

        Returns:
            Dict with queue generation results
        """
        try:
            # Get user learning profile
            profile, created = UserLearningProfile.objects.get_or_create(
                user=user,
                defaults={
                    "preferred_language": "spanish",
                    "current_level": 1,
                    "learning_goals": ["vocabulary", "grammar"],
                    "study_streak": 0,
                    "total_study_time": 0,
                },
            )

            # Calculate items needed by priority
            queue_items = []

            # 1. Review items (spaced repetition)
            review_items = self._get_review_items(user, target_items // 2)
            queue_items.extend(review_items)

            # 2. Struggling items (adaptive learning)
            struggling_items = self._get_struggling_items(user, target_items // 4)
            queue_items.extend(struggling_items)

            # 3. New content (progressive learning)
            remaining_slots = target_items - len(queue_items)
            new_items = self._generate_new_content(
                user, profile, remaining_slots, focus_areas
            )
            queue_items.extend(new_items)

            # Clear existing queue and create new one
            UserLessonQueue.objects.filter(user=user).delete()

            # Add items to queue with priority ordering
            for i, item in enumerate(queue_items[:target_items]):
                UserLessonQueue.objects.create(
                    user=user,
                    content_item=item,
                    priority=i + 1,
                    scheduled_for=timezone.now() + timedelta(minutes=i),
                    status="pending",
                )

            logger.info(
                f"✅ Generated lesson queue for {user.username}: {len(queue_items)} items"
            )

            return {
                "success": True,
                "queue_size": len(queue_items),
                "review_items": len(review_items),
                "struggling_items": len(struggling_items),
                "new_items": len(new_items),
                "user_level": profile.current_level,
            }

        except Exception as e:
            logger.error(f"❌ Failed to generate lesson queue for {user.username}: {e}")
            return {"success": False, "error": str(e), "queue_size": 0}

    def _get_review_items(self, user, max_items: int) -> List[ContentItem]:
        """Get items due for review using spaced repetition algorithm"""

        # Calculate review intervals based on performance
        now = timezone.now()

        # Get items due for review
        review_performances = (
            UserContentPerformance.objects.filter(user=user, next_review__lte=now)
            .select_related("content_item")
            .order_by("next_review")[:max_items]
        )

        return [perf.content_item for perf in review_performances]

    def _get_struggling_items(self, user, max_items: int) -> List[ContentItem]:
        """Get items user is struggling with for extra practice"""

        # Find items with low success rate or recent failures
        struggling_performances = (
            UserContentPerformance.objects.filter(
                user=user, success_rate__lt=0.7  # Less than 70% success rate
            )
            .select_related("content_item")
            .order_by("success_rate", "-last_attempt")[:max_items]
        )

        return [perf.content_item for perf in struggling_performances]

    def _generate_new_content(
        self,
        user,
        profile: UserLearningProfile,
        max_items: int,
        focus_areas: Optional[List[str]] = None,
    ) -> List[ContentItem]:
        """Generate new content appropriate for user's level and goals"""

        if max_items <= 0:
            return []

        try:
            # Determine content types based on user goals
            content_types = self._get_content_types_for_goals(profile.learning_goals)

            # Generate new content
            result = self.content_generator.generate_care_content_batch(
                language=profile.preferred_language,
                difficulty_level=profile.current_level,
                content_types=content_types,
                batch_size=max_items,
                topic=focus_areas[0] if focus_areas else None,
            )

            if result["success"]:
                return result["content_items"]
            else:
                logger.warning(f"Failed to generate new content: {result.get('error')}")
                return []

        except Exception as e:
            logger.error(f"Error generating new content: {e}")
            return []

    def _get_content_types_for_goals(self, learning_goals: List[str]) -> List[str]:
        """Map learning goals to appropriate content types"""

        goal_mapping = {
            "vocabulary": ["flashcard", "translation"],
            "grammar": ["fill_blank", "mcq"],
            "speaking": ["pronunciation", "conversation"],
            "listening": ["audio_comprehension", "dictation"],
            "reading": ["reading_comprehension", "mcq"],
            "writing": ["translation", "fill_blank"],
        }

        content_types = []
        for goal in learning_goals:
            content_types.extend(goal_mapping.get(goal, ["flashcard"]))

        # Remove duplicates and ensure we have at least basic types
        unique_types = list(set(content_types))
        if not unique_types:
            unique_types = ["flashcard", "mcq"]

        return unique_types[:3]  # Limit to 3 types for variety

    def update_user_performance(
        self,
        user,
        content_item: ContentItem,
        is_correct: bool,
        response_time: Optional[float] = None,
        hints_used: int = 0,
    ) -> None:
        """Update user performance and calculate next review interval"""

        try:
            # Get or create performance record
            performance, created = UserContentPerformance.objects.get_or_create(
                user=user,
                content_item=content_item,
                defaults={
                    "attempts": 0,
                    "correct_attempts": 0,
                    "success_rate": 0.0,
                    "average_response_time": 0.0,
                    "confidence_level": 0.5,
                },
            )

            # Update performance metrics
            performance.attempts += 1
            if is_correct:
                performance.correct_attempts += 1

            performance.success_rate = (
                performance.correct_attempts / performance.attempts
            )
            performance.last_attempt = timezone.now()

            # Update response time
            if response_time:
                if performance.average_response_time == 0:
                    performance.average_response_time = response_time
                else:
                    performance.average_response_time = (
                        performance.average_response_time * 0.8 + response_time * 0.2
                    )

            # Calculate confidence based on performance
            confidence_factors = [
                performance.success_rate,
                1.0 if response_time and response_time < 10 else 0.5,  # Quick response
                max(0, 1.0 - (hints_used * 0.2)),  # Fewer hints = higher confidence
            ]
            performance.confidence_level = sum(confidence_factors) / len(
                confidence_factors
            )

            # Calculate next review interval using spaced repetition
            performance.next_review = self._calculate_next_review_interval(
                performance, is_correct
            )

            performance.save()

            # Update user learning profile
            self._update_user_level(user, performance)

            logger.debug(
                f"Updated performance for {user.username}: {content_item.type} - Success Rate: {performance.success_rate:.2f}"
            )

        except Exception as e:
            logger.error(f"Failed to update user performance: {e}")

    def _calculate_next_review_interval(
        self, performance: UserContentPerformance, is_correct: bool
    ) -> datetime:
        """Calculate next review time using spaced repetition algorithm (SM-2 inspired)"""

        base_intervals = {
            1: timedelta(minutes=5),  # 5 minutes
            2: timedelta(hours=1),  # 1 hour
            3: timedelta(hours=6),  # 6 hours
            4: timedelta(days=1),  # 1 day
            5: timedelta(days=3),  # 3 days
            6: timedelta(days=7),  # 1 week
            7: timedelta(days=14),  # 2 weeks
            8: timedelta(days=30),  # 1 month
        }

        # Start with interval 1 if first attempt
        if performance.attempts == 1:
            interval_key = 1
        else:
            # Get current interval level (simplified)
            current_level = min(performance.attempts, 8)

            if is_correct:
                # Increase interval if correct
                interval_key = min(current_level + 1, 8)

                # Bonus for high confidence
                if performance.confidence_level > 0.8:
                    interval_key = min(interval_key + 1, 8)
            else:
                # Reset to shorter interval if incorrect
                interval_key = max(1, current_level - 2)

        # Apply success rate modifier
        interval = base_intervals[interval_key]
        success_modifier = 0.5 + (performance.success_rate * 0.5)  # 0.5 to 1.0

        adjusted_interval = timedelta(
            seconds=interval.total_seconds() * success_modifier
        )

        return timezone.now() + adjusted_interval

    def _update_user_level(self, user, performance: UserContentPerformance) -> None:
        """Update user level based on overall performance"""

        try:
            profile = UserLearningProfile.objects.get(user=user)

            # Calculate average performance across all content
            avg_performance = UserContentPerformance.objects.filter(
                user=user
            ).aggregate(
                avg_success=Avg("success_rate"), avg_confidence=Avg("confidence_level")
            )

            avg_success = avg_performance["avg_success"] or 0
            avg_confidence = avg_performance["avg_confidence"] or 0

            # Level up conditions
            if avg_success > 0.8 and avg_confidence > 0.7:
                total_attempts = UserContentPerformance.objects.filter(
                    user=user
                ).count()

                # Require minimum attempts before leveling up
                min_attempts_per_level = 20
                target_level = min(5, (total_attempts // min_attempts_per_level) + 1)

                if target_level > profile.current_level:
                    profile.current_level = target_level
                    profile.save()
                    logger.info(
                        f"🎉 User {user.username} leveled up to {target_level}!"
                    )

        except UserLearningProfile.DoesNotExist:
            logger.warning(f"No learning profile found for user {user.username}")
        except Exception as e:
            logger.error(f"Error updating user level: {e}")

    def get_user_progress_summary(self, user) -> Dict:
        """Get comprehensive progress summary for user"""

        try:
            profile = UserLearningProfile.objects.get(user=user)

            # Performance statistics
            performance_stats = UserContentPerformance.objects.filter(
                user=user
            ).aggregate(
                total_attempts=Count("id"),
                avg_success=Avg("success_rate"),
                avg_confidence=Avg("confidence_level"),
                avg_response_time=Avg("average_response_time"),
            )

            # Queue status
            queue_status = UserLessonQueue.objects.filter(user=user).aggregate(
                pending=Count("id", filter=Q(status="pending")),
                completed=Count("id", filter=Q(status="completed")),
                total=Count("id"),
            )

            # Recent activity
            recent_activity = UserContentPerformance.objects.filter(
                user=user, last_attempt__gte=timezone.now() - timedelta(days=7)
            ).count()

            return {
                "user_level": profile.current_level,
                "preferred_language": profile.preferred_language,
                "learning_goals": profile.learning_goals,
                "study_streak": profile.study_streak,
                "total_study_time": profile.total_study_time,
                "performance": {
                    "total_attempts": performance_stats["total_attempts"] or 0,
                    "average_success_rate": round(
                        performance_stats["avg_success"] or 0, 2
                    ),
                    "average_confidence": round(
                        performance_stats["avg_confidence"] or 0, 2
                    ),
                    "average_response_time": round(
                        performance_stats["avg_response_time"] or 0, 2
                    ),
                },
                "queue": queue_status,
                "recent_activity": recent_activity,
                "next_level_progress": self._calculate_level_progress(user, profile),
            }

        except UserLearningProfile.DoesNotExist:
            return {"error": "User learning profile not found"}
        except Exception as e:
            logger.error(f"Error getting progress summary: {e}")
            return {"error": str(e)}

    def _calculate_level_progress(self, user, profile: UserLearningProfile) -> Dict:
        """Calculate progress towards next level"""

        current_level = profile.current_level
        next_level = min(current_level + 1, 5)

        if current_level >= 5:
            return {"at_max_level": True}

        # Requirements for next level
        required_attempts = next_level * 20
        required_success_rate = 0.8
        required_confidence = 0.7

        # Current progress
        current_attempts = UserContentPerformance.objects.filter(user=user).count()
        avg_performance = UserContentPerformance.objects.filter(user=user).aggregate(
            avg_success=Avg("success_rate"), avg_confidence=Avg("confidence_level")
        )

        current_success = avg_performance["avg_success"] or 0
        current_confidence = avg_performance["avg_confidence"] or 0

        return {
            "current_level": current_level,
            "next_level": next_level,
            "progress": {
                "attempts": {
                    "current": current_attempts,
                    "required": required_attempts,
                    "percentage": min(
                        100, (current_attempts / required_attempts) * 100
                    ),
                },
                "success_rate": {
                    "current": round(current_success, 2),
                    "required": required_success_rate,
                    "met": current_success >= required_success_rate,
                },
                "confidence": {
                    "current": round(current_confidence, 2),
                    "required": required_confidence,
                    "met": current_confidence >= required_confidence,
                },
            },
        }
