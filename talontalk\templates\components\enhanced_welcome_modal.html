<!-- Enhanced Welcome Modal with Django Integration -->
<div id="welcome-modal" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 hidden transition-opacity duration-300">
    <div class="bg-white rounded-2xl shadow-2xl max-w-md mx-4 p-8 text-center transform transition-all duration-500 scale-95 opacity-0" id="modal-content">
        <!-- User Greeting with Real Data -->
        <div class="mb-6">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center animate-pulse">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5s3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18s-3.332.477-4.5 1.253"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">
                Welcome back, 
                {% if user.first_name %}
                    {{ user.first_name }}!
                {% else %}
                    {{ user.username }}!
                {% endif %}
            </h2>
            <p class="text-gray-600" id="personalized-greeting">
                {% now "D" as current_day %}
                Great to see you this {{ current_day }}! We're preparing your personalized lessons...
            </p>
        </div>

        <!-- Streak Display with Animation -->
        <div class="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-4 mb-6 transform hover:scale-105 transition-transform duration-200">
            <div class="flex items-center justify-center space-x-2">
                <span class="text-2xl animate-bounce">🔥</span>
                <span class="text-lg font-semibold text-orange-600" id="streak-count">Loading...</span>
                <span class="text-sm text-orange-500">day streak</span>
            </div>
            <p class="text-xs text-orange-400 mt-1" id="streak-motivation">Keep it up!</p>
        </div>

        <!-- Enhanced Loading Progress with Better UX -->
        <div class="space-y-4 mb-6">
            <div class="flex items-center space-x-3 p-2 rounded-lg" id="step-1">
                <div class="loading-spinner w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                <span class="text-sm text-gray-600 flex-1 text-left">Analyzing your learning preferences...</span>
                <svg class="w-5 h-5 text-green-500 hidden" id="step-1-done" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <div class="flex items-center space-x-3 p-2 rounded-lg opacity-50" id="step-2">
                <div class="loading-spinner w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600 flex-1 text-left">Generating AI-powered flashcards...</span>
                <svg class="w-5 h-5 text-green-500 hidden" id="step-2-done" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <div class="flex items-center space-x-3 p-2 rounded-lg opacity-50" id="step-3">
                <div class="loading-spinner w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600 flex-1 text-left">Preparing your daily goals...</span>
                <svg class="w-5 h-5 text-green-500 hidden" id="step-3-done" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>

            <div class="flex items-center space-x-3 p-2 rounded-lg opacity-50" id="step-4">
                <div class="loading-spinner w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full"></div>
                <span class="text-sm text-gray-600 flex-1 text-left">Optimizing for your schedule...</span>
                <svg class="w-5 h-5 text-green-500 hidden" id="step-4-done" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="w-full bg-gray-200 rounded-full h-2 mb-6">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500 ease-out" id="progress-bar" style="width: 0%"></div>
        </div>

        <!-- Daily Goals Preview (Shows after loading) -->
        <div id="goals-preview" class="hidden bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Your Goals Today</h3>
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-2xl font-bold text-blue-600" id="goal-new-words">0</div>
                    <div class="text-gray-600">New Words</div>
                </div>
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-2xl font-bold text-green-600" id="goal-reviews">0</div>
                    <div class="text-gray-600">Reviews</div>
                </div>
            </div>
            <div class="mt-3 text-center">
                <span class="text-sm text-gray-600">Estimated time: </span>
                <span class="font-semibold text-purple-600" id="goal-time">0 minutes</span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
            <button id="start-learning-btn" class="hidden w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
                Start Learning! 🚀
            </button>
            
            <button id="skip-btn" class="w-full text-gray-500 py-2 px-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-sm">
                Skip and continue
            </button>
        </div>

        <!-- Loading State Message -->
        <div id="loading-message" class="text-center">
            <p class="text-sm text-gray-500">This usually takes 5-10 seconds...</p>
        </div>

        <!-- Error State -->
        <div id="error-state" class="hidden bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
            <div class="flex items-center space-x-2 text-red-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm font-medium">Connection issue</span>
            </div>
            <p class="text-sm text-red-600 mt-1">Don't worry! You can still practice with our offline content.</p>
            <button id="continue-offline-btn" class="mt-2 text-sm bg-red-100 hover:bg-red-200 text-red-700 py-1 px-3 rounded transition-colors">
                Continue offline
            </button>
        </div>
    </div>
</div>

<style>
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

#welcome-modal.show #modal-content {
    opacity: 1;
    transform: scale(1);
}

.step-complete {
    opacity: 1 !important;
    background: linear-gradient(to right, #d1fae5, #ecfdf5);
}

.step-complete .loading-spinner {
    display: none;
}
</style>

<script>
class EnhancedWelcomeModal {
    constructor() {
        this.modal = document.getElementById('welcome-modal');
        this.modalContent = document.getElementById('modal-content');
        this.progressBar = document.getElementById('progress-bar');
        this.steps = ['step-1', 'step-2', 'step-3', 'step-4'];
        this.currentStep = 0;
        this.preloadedContent = null;
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Start Learning button
        document.getElementById('start-learning-btn').addEventListener('click', () => {
            this.startLearning();
        });

        // Skip button
        document.getElementById('skip-btn').addEventListener('click', () => {
            this.hide();
        });

        // Continue offline button
        document.getElementById('continue-offline-btn').addEventListener('click', () => {
            this.hide();
            this.showOfflineMessage();
        });

        // Close on outside click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });
    }

    async show() {
        // Get CSRF token for Django
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        this.modal.classList.remove('hidden');
        setTimeout(() => {
            this.modal.classList.add('show');
        }, 10);

        // Start the loading process
        await this.loadContent(csrfToken);
    }

    hide() {
        this.modal.classList.remove('show');
        setTimeout(() => {
            this.modal.classList.add('hidden');
        }, 300);
    }

    async loadContent(csrfToken) {
        try {
            // Step 1: Load user preferences
            await this.completeStep(0, "User preferences loaded!");
            await this.sleep(800);

            // Step 2: Generate AI content
            await this.completeStep(1, "AI content ready!");
            const content = await this.fetchDailyContent(csrfToken);
            await this.sleep(1000);

            // Step 3: Prepare goals
            await this.completeStep(2, "Daily goals set!");
            await this.sleep(600);

            // Step 4: Final optimization
            await this.completeStep(3, "Everything ready!");
            await this.sleep(400);

            // Show completion
            this.showCompletionState(content);

        } catch (error) {
            console.error('Error loading content:', error);
            this.showErrorState();
        }
    }

    async fetchDailyContent(csrfToken) {
        // First try to get cached content
        const cached = this.getCachedContent();
        if (cached) {
            console.log('Using cached content');
            return cached;
        }

        // Fetch fresh content from API
        const response = await fetch('/api/preload/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                language: 'spanish',  // TODO: Get from user preferences
                difficulty: 'beginner'  // TODO: Get from user profile
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (data.success) {
            // Cache the content
            this.cacheContent(data.daily_content);
            this.preloadedContent = data.daily_content;
            return data.daily_content;
        } else {
            throw new Error(data.error || 'Failed to load content');
        }
    }

    async completeStep(stepIndex, message) {
        const stepElement = document.getElementById(this.steps[stepIndex]);
        const doneIcon = document.getElementById(`${this.steps[stepIndex]}-done`);
        const spinner = stepElement.querySelector('.loading-spinner');
        
        // Update progress bar
        const progress = ((stepIndex + 1) / this.steps.length) * 100;
        this.progressBar.style.width = `${progress}%`;
        
        // Complete current step
        stepElement.classList.add('step-complete');
        stepElement.style.opacity = '1';
        spinner.style.display = 'none';
        doneIcon.classList.remove('hidden');
        
        // Update step text
        const stepText = stepElement.querySelector('span');
        stepText.textContent = message;
        
        // Activate next step
        if (stepIndex + 1 < this.steps.length) {
            const nextStep = document.getElementById(this.steps[stepIndex + 1]);
            nextStep.style.opacity = '1';
            nextStep.querySelector('.loading-spinner').classList.add('animate-spin');
        }
    }

    showCompletionState(content) {
        // Hide loading message
        document.getElementById('loading-message').style.display = 'none';
        
        // Show goals preview
        const goalsPreview = document.getElementById('goals-preview');
        goalsPreview.classList.remove('hidden');
        
        // Update streak info
        if (content && content.user_stats) {
            document.getElementById('streak-count').textContent = content.user_stats.current_streak;
            this.updateStreakMotivation(content.user_stats.current_streak);
        }
        
        // Update goals
        if (content && content.goals) {
            document.getElementById('goal-new-words').textContent = content.goals.new_words;
            document.getElementById('goal-reviews').textContent = content.goals.review_items;
            document.getElementById('goal-time').textContent = `${content.goals.estimated_minutes} minutes`;
        }
        
        // Show start button
        document.getElementById('start-learning-btn').classList.remove('hidden');
        
        // Hide skip button or change to "Maybe later"
        document.getElementById('skip-btn').textContent = 'Maybe later';
    }

    updateStreakMotivation(streak) {
        const motivations = {
            1: "Great start! 🌱",
            3: "Building momentum! 💪", 
            7: "One week strong! 🎯",
            14: "Two weeks - amazing! ⭐",
            30: "One month champion! 🏆",
            default: "You're on fire! 🔥"
        };
        
        const message = motivations[streak] || motivations.default;
        document.getElementById('streak-motivation').textContent = message;
    }

    showErrorState() {
        document.getElementById('loading-message').style.display = 'none';
        document.getElementById('error-state').classList.remove('hidden');
        
        // Still show some basic goals
        document.getElementById('goals-preview').classList.remove('hidden');
        document.getElementById('goal-new-words').textContent = '5';
        document.getElementById('goal-reviews').textContent = '10';
        document.getElementById('goal-time').textContent = '10 minutes';
    }

    startLearning() {
        // Emit event with preloaded content
        if (this.preloadedContent) {
            window.dispatchEvent(new CustomEvent('startLearningWithContent', {
                detail: this.preloadedContent
            }));
        } else {
            window.dispatchEvent(new CustomEvent('startLearning'));
        }
        
        this.hide();
        
        // Navigate to practice page or trigger practice mode
        // You can customize this based on your app's routing
        window.location.href = '/practice/'; // Or wherever your practice page is
    }

    showOfflineMessage() {
        // Show a temporary notification about offline mode
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-blue-500 text-white p-4 rounded-lg shadow-lg z-50';
        notification.innerHTML = '📚 Continuing with offline content - you can still learn!';
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    getCachedContent() {
        try {
            const cached = localStorage.getItem('dailyLessonsEnhanced');
            if (!cached) return null;
            
            const data = JSON.parse(cached);
            const today = new Date().toDateString();
            
            if (data.date === today && data.content) {
                return data.content;
            }
        } catch (e) {
            console.warn('Error reading cached content:', e);
        }
        return null;
    }

    cacheContent(content) {
        try {
            const cacheData = {
                date: new Date().toDateString(),
                content: content,
                timestamp: Date.now()
            };
            localStorage.setItem('dailyLessonsEnhanced', JSON.stringify(cacheData));
        } catch (e) {
            console.warn('Error caching content:', e);
        }
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Global instance
window.enhancedWelcomeModal = new EnhancedWelcomeModal();

// Auto-show logic based on Django context or session
document.addEventListener('DOMContentLoaded', () => {
    // Check if user just logged in - you can set this in your Django login view
    {% if user.is_authenticated %}
        // Check session storage for login indicator
        const justLoggedIn = sessionStorage.getItem('justLoggedIn');
        const lastWelcomeShown = localStorage.getItem('lastWelcomeShown');
        const today = new Date().toDateString();
        
        // Show if just logged in OR if not shown today
        if (justLoggedIn || lastWelcomeShown !== today) {
            sessionStorage.removeItem('justLoggedIn');
            localStorage.setItem('lastWelcomeShown', today);
            
            // Small delay to let the page load
            setTimeout(() => {
                window.enhancedWelcomeModal.show();
            }, 500);
        }
    {% endif %}
});

// Listen for custom events from other parts of the app
window.addEventListener('showWelcomeModal', () => {
    window.enhancedWelcomeModal.show();
});

// Export for use in other scripts
window.addEventListener('startLearningWithContent', (event) => {
    console.log('Starting learning with preloaded content:', event.detail);
    // Store the preloaded content globally
    window.preloadedLessons = event.detail;
    
    // Emit another event that your practice components can listen to
    window.dispatchEvent(new CustomEvent('lessonsPreloaded', {
        detail: event.detail
    }));
});
</script>
