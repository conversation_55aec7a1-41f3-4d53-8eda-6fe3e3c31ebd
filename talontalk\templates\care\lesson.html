{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ lesson.title }} - TalonTalk C.A.R.E.</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Enhanced animations for C.A.R.E. transitions */
        .care-transition {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .care-phase-enter {
            opacity: 0;
            transform: translateY(20px);
        }
        
        .care-phase-active {
            opacity: 1;
            transform: translateY(0);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #10b981 0%, #3b82f6 33%, #8b5cf6 66%, #f59e0b 100%);
            transition: width 0.5s ease-in-out;
        }
        
        .contextualize-theme { background: linear-gradient(135deg, #10b981, #34d399); }
        .acquire-theme { background: linear-gradient(135deg, #3b82f6, #60a5fa); }
        .reinforce-theme { background: linear-gradient(135deg, #8b5cf6, #a78bfa); }
        .extend-theme { background: linear-gradient(135deg, #f59e0b, #fbbf24); }
        
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .chat-message {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen"
      data-lesson-id="{{ lesson.id }}"
      data-user-id="{% if user.is_authenticated %}{{ user.id }}{% endif %}">
    <!-- C.A.R.E. Progress Header -->
    <div class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between mb-3">
                <h1 class="text-2xl font-bold text-gray-900">{{ lesson.title }}</h1>
                <button id="aiTutorBtn" class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-robot"></i>
                    <span>AI Tutor</span>
                </button>
            </div>
            
            <!-- C.A.R.E. Progress Bar -->
            <div class="relative">
                <div class="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
                    <div id="careProgressBar" class="h-full progress-bar transition-all duration-500" style="width: {{ progress_percentage }}%"></div>
                </div>
                
                <!-- Phase Indicators -->
                <div class="flex justify-between mt-2">
                    <div class="care-phase-indicator" data-phase="contextualize">
                        <div class="w-8 h-8 rounded-full bg-emerald-500 flex items-center justify-center text-white text-sm font-bold">C</div>
                        <span class="text-xs mt-1 block text-gray-600">Contextualize</span>
                    </div>
                    <div class="care-phase-indicator" data-phase="acquire">
                        <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-bold">A</div>
                        <span class="text-xs mt-1 block text-gray-600">Acquire</span>
                    </div>
                    <div class="care-phase-indicator" data-phase="reinforce">
                        <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center text-white text-sm font-bold">R</div>
                        <span class="text-xs mt-1 block text-gray-600">Reinforce</span>
                    </div>
                    <div class="care-phase-indicator" data-phase="extend">
                        <div class="w-8 h-8 rounded-full bg-amber-500 flex items-center justify-center text-white text-sm font-bold">E</div>
                        <span class="text-xs mt-1 block text-gray-600">Extend</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="max-w-7xl mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Sidebar - Lesson Navigation -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-32">
                    <h3 class="font-semibold text-gray-900 mb-4">Lesson Progress</h3>

                    <!-- Session Progress Indicator -->
                    <div class="session-progress mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                        <div class="text-sm text-gray-600 mb-2">Session Progress</div>
                        <div class="text-xs text-gray-500">
                            Progress: 0/0 correct (0%) | Streak: 0 | XP: 0
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="care-nav-item" data-phase="contextualize">
                            <div class="flex items-center gap-3 p-3 rounded-lg hover:bg-emerald-50 cursor-pointer transition-colors">
                                <i class="fas fa-globe-americas text-emerald-600"></i>
                                <div>
                                    <div class="font-medium text-gray-900">Contextualize</div>
                                    <div class="text-sm text-gray-500">Set the scene</div>
                                </div>
                            </div>
                        </div>
                        <div class="care-nav-item" data-phase="acquire">
                            <div class="flex items-center gap-3 p-3 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors">
                                <i class="fas fa-lightbulb text-blue-600"></i>
                                <div>
                                    <div class="font-medium text-gray-900">Acquire</div>
                                    <div class="text-sm text-gray-500">Learn concepts</div>
                                </div>
                            </div>
                        </div>
                        <div class="care-nav-item" data-phase="reinforce">
                            <div class="flex items-center gap-3 p-3 rounded-lg hover:bg-purple-50 cursor-pointer transition-colors">
                                <i class="fas fa-dumbbell text-purple-600"></i>
                                <div>
                                    <div class="font-medium text-gray-900">Reinforce</div>
                                    <div class="text-sm text-gray-500">Practice & solidify</div>
                                </div>
                            </div>
                        </div>
                        <div class="care-nav-item" data-phase="extend">
                            <div class="flex items-center gap-3 p-3 rounded-lg hover:bg-amber-50 cursor-pointer transition-colors">
                                <i class="fas fa-rocket text-amber-600"></i>
                                <div>
                                    <div class="font-medium text-gray-900">Extend</div>
                                    <div class="text-sm text-gray-500">Apply knowledge</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content - C.A.R.E. Phases -->
            <div class="lg:col-span-3">
                <div id="careContentArea" class="care-transition">
                    <!-- Contextualize Phase -->
                    <div id="contextualizePhase" class="care-phase-content">
                        <div class="text-center py-12">
                            <div class="loading-spinner border-4 border-gray-300 border-t-emerald-500 rounded-full w-8 h-8 mx-auto mb-4"></div>
                            <p class="text-gray-600">Loading Contextualize content...</p>
                        </div>
                    </div>
                    
                    <!-- Acquire Phase -->
                    <div id="acquirePhase" class="care-phase-content hidden">
                        <div class="text-center py-12">
                            <div class="loading-spinner border-4 border-gray-300 border-t-blue-500 rounded-full w-8 h-8 mx-auto mb-4"></div>
                            <p class="text-gray-600">Loading Acquire content...</p>
                        </div>
                    </div>
                    
                    <!-- Reinforce Phase -->
                    <div id="reinforcePhase" class="care-phase-content hidden">
                        <div class="text-center py-12">
                            <div class="loading-spinner border-4 border-gray-300 border-t-purple-500 rounded-full w-8 h-8 mx-auto mb-4"></div>
                            <p class="text-gray-600">Loading Reinforce content...</p>
                        </div>
                    </div>
                    
                    <!-- Extend Phase -->
                    <div id="extendPhase" class="care-phase-content hidden">
                        <div class="text-center py-12">
                            <div class="loading-spinner border-4 border-gray-300 border-t-amber-500 rounded-full w-8 h-8 mx-auto mb-4"></div>
                            <p class="text-gray-600">Loading Extend content...</p>
                        </div>
                    </div>
                </div>

                <!-- Phase Navigation -->
                <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
                    <button id="prevPhaseBtn" class="flex items-center gap-2 px-6 py-3 text-gray-600 hover:text-gray-900 transition-colors" disabled>
                        <i class="fas fa-chevron-left"></i>
                        <span>Previous</span>
                    </button>
                    
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-500">Phase</span>
                        <span id="currentPhaseNum" class="text-lg font-bold text-blue-600">1</span>
                        <span class="text-sm text-gray-500">of 4</span>
                    </div>
                    
                    <button id="nextPhaseBtn" class="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <span>Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Tutor Modal -->
    <div id="aiTutorModal" class="fixed inset-0 z-50 hidden">
        <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>
        <div class="relative flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900">AI Learning Tutor</h3>
                                <p class="text-sm text-gray-500">Ask me anything about this lesson!</p>
                            </div>
                        </div>
                        <button id="closeTutorBtn" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div id="tutorChatArea" class="p-6 h-96 overflow-y-auto">
                    <div class="tutor-message bg-blue-50 p-4 rounded-lg mb-4">
                        <p class="text-gray-800">Hello! I'm your AI tutor. I can help explain concepts, provide extra examples, or answer questions about this lesson. What would you like to know?</p>
                    </div>
                </div>
                <div class="p-6 border-t border-gray-200">
                    <div class="flex gap-3">
                        <input id="tutorInput" type="text" placeholder="Ask a question..." class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button id="sendTutorBtn" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'js/care-lesson-simple.js' %}"></script>
</body>
</html>
