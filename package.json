{"name": "talontalk-frontend", "version": "1.0.0", "description": "TalonTalk Language Learning Platform - Frontend TypeScript", "main": "index.js", "scripts": {"build": "tsc", "watch": "tsc --watch", "dev": "concurrently \"tsc --watch\" \"npm run serve\"", "serve": "python manage.py runserver", "lint": "eslint src/typescript/**/*.ts", "lint:fix": "eslint src/typescript/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "rimraf talontalk/static/js/dist", "prebuild": "npm run clean"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "daisyui": "^5.0.43", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.55.0", "rimraf": "^5.0.5", "typescript": "^5.3.0"}, "keywords": ["typescript", "language-learning", "django", "education"], "author": "TalonTalk Team", "license": "MIT"}