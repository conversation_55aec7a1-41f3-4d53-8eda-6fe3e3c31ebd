{% extends 'base.html' %}
{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
  <!-- Header Section -->
  <div class="bg-white/80 backdrop-blur-sm shadow-sm border-b border-blue-100">
    <div class="max-w-7xl mx-auto px-6 py-6">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center shadow-lg">
            <span class="text-white text-2xl">📚</span>
          </div>
          <div>
            <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{{ lesson.title }}</h1>
            <p class="text-gray-600 text-lg">{{ lesson.description }}</p>
          </div>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'dashboard' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-xl text-sm font-medium transition-all hover:scale-105">
            ← Dashboard
          </a>
          {% if not progress.completed %}
          <button id="practice-lesson-btn" class="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-blue-900 px-8 py-3 rounded-xl text-sm font-bold transition-all hover:scale-105 shadow-lg">
            Start Practice →
          </button>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content - Full Width Utilization -->
  <div class="max-w-7xl mx-auto px-6 py-8">
    <!-- Top Stats Row -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg border border-blue-100">
        <div class="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center mx-auto mb-3">
          <span class="text-2xl">📖</span>
        </div>
        <div class="text-2xl font-bold text-blue-600">{{ vocabularies.count }}</div>
        <div class="text-sm text-gray-600">Vocabulary Words</div>
      </div>
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg border border-purple-100">
        <div class="w-12 h-12 rounded-xl bg-purple-100 flex items-center justify-center mx-auto mb-3">
          <span class="text-2xl">⏱️</span>
        </div>
        <div class="text-2xl font-bold text-purple-600">{{ vocabularies.count|add:5 }}m</div>
        <div class="text-sm text-gray-600">Estimated Time</div>
      </div>
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg border border-green-100">
        <div class="w-12 h-12 rounded-xl bg-green-100 flex items-center justify-center mx-auto mb-3">
          <span class="text-2xl">🎯</span>
        </div>
        <div class="text-2xl font-bold text-green-600 capitalize">{{ lesson.difficulty|default:"Beginner" }}</div>
        <div class="text-sm text-gray-600">Difficulty Level</div>
      </div>
      <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg border border-yellow-100">
        <div class="w-12 h-12 rounded-xl bg-yellow-100 flex items-center justify-center mx-auto mb-3">
          <span class="text-2xl">{% if progress.completed %}✅{% else %}🏃{% endif %}</span>
        </div>
        <div class="text-2xl font-bold text-yellow-600">{% if progress.completed %}Done!{% else %}Ready{% endif %}</div>
        <div class="text-sm text-gray-600">Status</div>
      </div>
    </div>

    <!-- Vocabulary Grid - Better Space Usage -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-800 flex items-center">
          <span class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center mr-3">
            <span class="text-white text-sm">📝</span>
          </span>
          Study Material
        </h2>
        <div class="text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-full">
          {{ vocabularies.count }} words to master
        </div>
      </div>
      
      <!-- Responsive Grid Layout -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {% for vocab in vocabularies %}
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-100 hover:border-blue-300 transition-all duration-300 hover:scale-105 hover:shadow-xl group">
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1">
              <h3 class="font-bold text-xl text-blue-600 mb-2 group-hover:text-purple-600 transition-colors">{{ vocab.word }}</h3>
              <p class="text-gray-700 text-lg font-medium mb-3">{{ vocab.translation }}</p>
              {% if vocab.pronunciation %}
              <div class="bg-blue-50 rounded-lg p-3 mb-3 border border-blue-100">
                <p class="text-sm font-medium text-blue-800 mb-1">Pronunciation:</p>
                <p class="text-blue-700 font-mono">{{ vocab.pronunciation }}</p>
              </div>
              {% endif %}
            </div>
            <button class="text-blue-500 hover:text-purple-500 p-3 hover:bg-blue-50 rounded-xl transition-all duration-200 hover:scale-110" 
                    onclick="pronounceWord('{{ vocab.word }}')">
              <span class="text-2xl">🔊</span>
            </button>
          </div>
          
          {% if vocab.example_sentence %}
          <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-4 mt-4">
            <p class="text-sm font-semibold text-blue-800 mb-2 flex items-center">
              <span class="w-4 h-4 rounded bg-blue-500 mr-2"></span>
              Example:
            </p>
            <p class="text-blue-700 italic">"{{ vocab.example_sentence }}"</p>
          </div>
          {% endif %}
        </div>
        {% endfor %}
      </div>
    </div>

    <!-- Learning Tips & Action Section -->
    <div class="grid md:grid-cols-2 gap-8">
      <!-- Tips Section -->
      <div class="bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl p-8 text-white shadow-xl">
        <h3 class="text-2xl font-bold mb-6 flex items-center">
          <span class="w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center mr-3">💡</span>
          Pro Learning Tips
        </h3>
        <div class="space-y-4">
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 rounded-full bg-yellow-400 flex items-center justify-center flex-shrink-0 mt-1">
              <span class="text-purple-800 text-sm font-bold">1</span>
            </div>
            <p class="text-white/90">Review each word carefully and understand its context</p>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 rounded-full bg-yellow-400 flex items-center justify-center flex-shrink-0 mt-1">
              <span class="text-purple-800 text-sm font-bold">2</span>
            </div>
            <p class="text-white/90">Use the audio feature to practice correct pronunciation</p>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 rounded-full bg-yellow-400 flex items-center justify-center flex-shrink-0 mt-1">
              <span class="text-purple-800 text-sm font-bold">3</span>
            </div>
            <p class="text-white/90">Pay attention to example sentences for real-world usage</p>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 rounded-full bg-yellow-400 flex items-center justify-center flex-shrink-0 mt-1">
              <span class="text-purple-800 text-sm font-bold">4</span>
            </div>
            <p class="text-white/90">Take your time - understanding beats speed every time</p>
          </div>
        </div>
      </div>

      <!-- Practice Section -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-gray-100">
        <h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <span class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center mr-3">
            <span class="text-white text-sm">🎯</span>
          </span>
          Ready to Practice?
        </h3>
        
        {% if progress.completed %}
        <div class="text-center mb-6">
          <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
            <span class="text-4xl">🎉</span>
          </div>
          <h4 class="text-xl font-bold text-green-600 mb-2">Lesson Completed!</h4>
          <p class="text-gray-600 mb-4">You earned <strong>{{ progress.xp_earned }} XP</strong></p>
          <button onclick="startPractice()" class="w-full bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-blue-900 px-6 py-4 rounded-xl font-bold text-lg transition-all hover:scale-105 shadow-lg">
            🔄 Practice Again
          </button>
        </div>
        {% else %}
        <div class="text-center mb-6">
          <div class="w-20 h-20 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-4">
            <span class="text-4xl">🚀</span>
          </div>
          <h4 class="text-xl font-bold text-gray-800 mb-2">Start Your Practice Session</h4>
          <p class="text-gray-600 mb-6">Test your knowledge and earn XP points!</p>
          <button onclick="startPractice()" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-4 rounded-xl font-bold text-lg transition-all hover:scale-105 shadow-lg">
            🎯 Start Practice Session
          </button>
        </div>
        {% endif %}
        
        <div class="bg-gray-50 rounded-xl p-4 mt-6">
          <div class="grid grid-cols-3 gap-4 text-center text-sm">
            <div>
              <div class="font-bold text-blue-600">{{ vocabularies.count }}</div>
              <div class="text-gray-600">Questions</div>
            </div>
            <div>
              <div class="font-bold text-purple-600">~{{ vocabularies.count|add:5 }}m</div>
              <div class="text-gray-600">Duration</div>
            </div>
            <div>
              <div class="font-bold text-green-600">{{ vocabularies.count|mul:10 }}</div>
              <div class="text-gray-600">Max XP</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function pronounceWord(word) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(word);
        utterance.lang = 'es-ES'; // Assuming Spanish, adjust as needed
        utterance.rate = 0.8;
        speechSynthesis.speak(utterance);
    } else {
        console.log('Speech synthesis not supported');
    }
}

function startPractice() {
    // Add smooth transition animation
    document.body.style.opacity = '0.7';
    document.body.style.transition = 'opacity 0.3s ease';
    
    setTimeout(() => {
        window.location.href = '{% url "care:lesson" %}?lesson_id={{ lesson.id }}';
    }, 300);
}

// Add entrance animations
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.grid > div');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<style>
/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* Glassmorphism effect */
.backdrop-blur-sm {
    backdrop-filter: blur(8px);
}

/* Custom gradient text */
.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

/* Hover effects */
.group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

/* Responsive grid improvements */
@media (min-width: 1536px) {
    .xl\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

/* Better mobile experience */
@media (max-width: 768px) {
    .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .md\:grid-cols-4 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}
</style>
{% endblock %}
