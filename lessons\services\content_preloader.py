"""
Content Preloading Service for TalonTalk
Provides instant access to pre-generated content for seamless user experience
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.core.cache import cache
from django.contrib.auth import get_user_model
from django.db.models import Q

from lessons.models import ContentItem, UserLearningProfile, UserContentPerformance

logger = logging.getLogger(__name__)
User = get_user_model()


class ContentPreloader:
    """
    Fast content delivery service that provides instant access to pre-generated content
    Eliminates the need for real-time AI generation during user sessions
    """
    
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour cache
        self.default_session_size = 10
        
    def get_instant_flashcards(self, user_id: int, session_type: str = "practice", 
                              difficulty: str = None, topic: str = None, 
                              count: int = 10) -> List[Dict[str, Any]]:
        """
        Get instant flashcards from pre-generated content pool
        No AI generation delay - instant response
        """
        
        cache_key = f"flashcards_{user_id}_{session_type}_{difficulty}_{topic}_{count}"
        
        # Try cache first
        cached_content = cache.get(cache_key)
        if cached_content:
            logger.info(f"🚀 Serving {len(cached_content)} flashcards from cache")
            return cached_content
        
        # Get user profile for personalization
        user_profile = self._get_user_profile(user_id)
        
        # Build content query based on user preferences and session type
        content_query = self._build_content_query(user_profile, session_type, difficulty, topic)
        
        # Get content from database
        content_items = list(content_query[:count * 2])  # Get extra for variety
        
        # Format for frontend consumption
        formatted_content = self._format_content_for_frontend(content_items, count)
        
        # Cache the result
        cache.set(cache_key, formatted_content, self.cache_timeout)
        
        logger.info(f"✅ Serving {len(formatted_content)} flashcards from database")
        return formatted_content
    
    def preload_user_session(self, user_id: int, session_types: List[str] = None) -> Dict[str, Any]:
        """
        Preload content for multiple session types for a user
        Called when user logs in or starts a learning session
        """
        
        if session_types is None:
            session_types = ["practice", "review", "weak_spots"]
        
        user_profile = self._get_user_profile(user_id)
        preloaded_content = {}
        
        for session_type in session_types:
            try:
                content = self.get_instant_flashcards(
                    user_id=user_id,
                    session_type=session_type,
                    difficulty=user_profile.get("skill_level", "beginner").lower(),
                    count=self.default_session_size
                )
                preloaded_content[session_type] = content
                
            except Exception as e:
                logger.error(f"Failed to preload {session_type} content for user {user_id}: {e}")
                preloaded_content[session_type] = []
        
        # Cache the preloaded session
        session_cache_key = f"preloaded_session_{user_id}"
        cache.set(session_cache_key, preloaded_content, self.cache_timeout)
        
        return preloaded_content
    
    def get_adaptive_content(self, user_id: int, count: int = 10) -> List[Dict[str, Any]]:
        """
        Get adaptive content based on user's learning history and weak spots
        Uses spaced repetition and performance data
        """
        
        # Get user's performance data
        weak_content = self._get_weak_spot_content(user_id, count // 2)
        review_content = self._get_spaced_repetition_content(user_id, count // 2)
        
        # Combine and shuffle
        adaptive_content = weak_content + review_content
        
        # Fill remaining slots with new content if needed
        if len(adaptive_content) < count:
            user_profile = self._get_user_profile(user_id)
            new_content = self._get_new_content(user_profile, count - len(adaptive_content))
            adaptive_content.extend(new_content)
        
        return adaptive_content[:count]
    
    def _get_user_profile(self, user_id: int) -> Dict[str, Any]:
        """Get user profile information for content personalization"""
        
        try:
            user = User.objects.select_related('profile').get(id=user_id)
            return {
                "skill_level": getattr(user.profile, 'skill_level', 'beginner'),
                "target_language": getattr(user.profile, 'target_language', 'spanish'),
                "native_language": getattr(user.profile, 'native_language', 'english'),
                "main_goal": getattr(user.profile, 'main_goal', 'conversational_fluency')
            }
        except User.DoesNotExist:
            return {
                "skill_level": "beginner",
                "target_language": "spanish",
                "native_language": "english",
                "main_goal": "conversational_fluency"
            }
    
    def _build_content_query(self, user_profile: Dict[str, Any], session_type: str, 
                           difficulty: str = None, topic: str = None):
        """Build database query for content based on parameters"""
        
        # Base query for active content
        query = ContentItem.objects.filter(
            is_active=True,
            language=user_profile["target_language"].lower()
        )
        
        # Apply difficulty filter
        if difficulty:
            difficulty_mapping = {"beginner": 1, "intermediate": 3, "advanced": 5}
            query = query.filter(difficulty=difficulty_mapping.get(difficulty, 1))
        else:
            # Use user's skill level
            skill_level = user_profile["skill_level"].lower()
            difficulty_mapping = {"beginner": 1, "intermediate": 3, "advanced": 5}
            user_difficulty = difficulty_mapping.get(skill_level, 1)
            
            # Include content at user's level and slightly below/above
            query = query.filter(difficulty__in=[max(1, user_difficulty-1), user_difficulty, min(5, user_difficulty+1)])
        
        # Apply topic filter
        if topic:
            query = query.filter(tags__contains=[topic])
        
        # Session type specific filtering
        if session_type == "review":
            # Prioritize content the user has seen before
            query = query.filter(
                id__in=UserContentPerformance.objects.filter(
                    user_id=user_profile.get("user_id", 0)
                ).values_list('content_item_id', flat=True)
            )
        elif session_type == "new":
            # Prioritize content the user hasn't seen
            query = query.exclude(
                id__in=UserContentPerformance.objects.filter(
                    user_id=user_profile.get("user_id", 0)
                ).values_list('content_item_id', flat=True)
            )
        
        # Order by creation date for variety
        return query.order_by('-created_at')
    
    def _format_content_for_frontend(self, content_items: List[ContentItem], count: int) -> List[Dict[str, Any]]:
        """Format database content items for frontend consumption"""
        
        formatted_items = []
        
        for item in content_items[:count]:
            formatted_item = {
                "id": item.id,
                "question": item.question_text,
                "correct_answer": item.answer_text,
                "options": item.options if item.options else [item.answer_text],
                "explanation": item.explanation or f"The correct answer is '{item.answer_text}'",
                "difficulty_level": self._map_difficulty_to_string(item.difficulty),
                "tags": item.tags or [],
                "type": item.content_type,
                "source": "preloaded"
            }
            
            # Ensure we have 4 options for multiple choice
            if len(formatted_item["options"]) < 4:
                formatted_item["options"] = self._generate_additional_options(
                    formatted_item["options"], item.language, item.tags
                )
            
            formatted_items.append(formatted_item)
        
        return formatted_items
    
    def _map_difficulty_to_string(self, difficulty_level: int) -> str:
        """Map numeric difficulty to string"""
        mapping = {1: "beginner", 2: "elementary", 3: "intermediate", 4: "upper_intermediate", 5: "advanced"}
        return mapping.get(difficulty_level, "beginner")
    
    def _generate_additional_options(self, existing_options: List[str], language: str, tags: List[str]) -> List[str]:
        """Generate additional multiple choice options if needed"""
        
        # Get similar content items for generating distractors
        similar_items = ContentItem.objects.filter(
            language=language,
            tags__overlap=tags,
            is_active=True
        ).exclude(answer_text__in=existing_options)[:10]
        
        additional_options = [item.answer_text for item in similar_items]
        
        # Combine and ensure we have exactly 4 options
        all_options = existing_options + additional_options
        
        # Add generic options if still not enough
        if len(all_options) < 4:
            generic_options = {
                "spanish": ["no sé", "tal vez", "posiblemente", "quizás"],
                "english": ["I don't know", "maybe", "possibly", "perhaps"]
            }
            all_options.extend(generic_options.get(language, ["option1", "option2", "option3", "option4"]))
        
        return all_options[:4]
    
    def _get_weak_spot_content(self, user_id: int, count: int) -> List[Dict[str, Any]]:
        """Get content for user's weak spots based on performance data"""
        
        weak_performances = UserContentPerformance.objects.filter(
            user_id=user_id,
            proficiency_score__lt=0.7
        ).select_related('content_item').order_by('proficiency_score')[:count]
        
        return [self._format_single_item(perf.content_item) for perf in weak_performances]
    
    def _get_spaced_repetition_content(self, user_id: int, count: int) -> List[Dict[str, Any]]:
        """Get content for spaced repetition based on last review time"""
        
        # Get content that's due for review (not reviewed in last 24 hours)
        due_for_review = UserContentPerformance.objects.filter(
            user_id=user_id,
            last_attempt__lt=datetime.now() - timedelta(hours=24)
        ).select_related('content_item').order_by('last_attempt')[:count]
        
        return [self._format_single_item(perf.content_item) for perf in due_for_review]
    
    def _get_new_content(self, user_profile: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """Get new content that user hasn't seen before"""
        
        query = self._build_content_query(user_profile, "new")
        new_items = list(query[:count])
        
        return [self._format_single_item(item) for item in new_items]
    
    def _format_single_item(self, item: ContentItem) -> Dict[str, Any]:
        """Format a single content item"""
        
        return {
            "id": item.id,
            "question": item.question_text,
            "correct_answer": item.answer_text,
            "options": item.options if item.options else [item.answer_text],
            "explanation": item.explanation or f"The correct answer is '{item.answer_text}'",
            "difficulty_level": self._map_difficulty_to_string(item.difficulty),
            "tags": item.tags or [],
            "type": item.content_type,
            "source": "database"
        }


# Utility functions for integration with existing views

def preload_content_for_user(user_id: int) -> Dict[str, Any]:
    """Utility function to preload content when user logs in"""
    preloader = ContentPreloader()
    return preloader.preload_user_session(user_id)


def get_instant_practice_session(user_id: int, count: int = 10) -> List[Dict[str, Any]]:
    """Utility function to get instant practice session"""
    preloader = ContentPreloader()
    return preloader.get_instant_flashcards(user_id, "practice", count=count)
