"""
Background Content Generation Service for TalonTalk
Implements the "Living Dataset" model with proactive content pipeline
"""

import logging
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model

from lessons.models import ContentItem, UserLearningProfile, UserContentPerformance
from lessons.content_quality_gateway import ContentQualityGateway, ContentGenerationRequest
from ai_services.llm_flashcards import LLMFlashcardService
from ai_services.llm_config import get_recommended_config

logger = logging.getLogger(__name__)
User = get_user_model()


class BackgroundContentGenerator:
    """
    Proactive Content Pipeline - Generates content in background using local Ollama
    Ensures users always have instant access to high-quality, personalized content
    """
    
    def __init__(self):
        self.llm_service = LLMFlashcardService(get_recommended_config())
        self.quality_gateway = ContentQualityGateway(self.llm_service)
        
        # Content generation targets
        self.daily_content_targets = {
            "beginner": {"flashcards": 20, "vocabulary": 15, "grammar": 10},
            "intermediate": {"flashcards": 25, "vocabulary": 20, "grammar": 15},
            "advanced": {"flashcards": 30, "vocabulary": 25, "grammar": 20}
        }
        
        # Popular topics for content generation
        self.content_topics = {
            "spanish": [
                "restaurant_ordering", "travel_basics", "family_relationships",
                "daily_routines", "shopping", "weather", "directions",
                "greetings", "numbers", "colors", "food_drinks", "clothing",
                "body_parts", "emotions", "hobbies", "work_professions"
            ]
        }
    
    def run_daily_content_generation(self):
        """Main method to run daily content generation"""
        logger.info("🚀 Starting daily content generation pipeline...")
        
        start_time = time.time()
        generation_stats = {
            "total_generated": 0,
            "total_accepted": 0,
            "total_rejected": 0,
            "languages_processed": [],
            "topics_covered": []
        }
        
        try:
            # Generate content for each supported language
            for language in ["spanish"]:  # Expand as needed
                language_stats = self._generate_language_content(language)
                generation_stats["total_generated"] += language_stats["generated"]
                generation_stats["total_accepted"] += language_stats["accepted"]
                generation_stats["total_rejected"] += language_stats["rejected"]
                generation_stats["languages_processed"].append(language)
                generation_stats["topics_covered"].extend(language_stats["topics"])
            
            # Clean up old content to maintain database size
            self._cleanup_old_content()
            
            # Generate personalized content for active users
            self._generate_personalized_content()
            
            generation_time = time.time() - start_time
            logger.info(f"✅ Daily content generation completed in {generation_time:.2f}s")
            logger.info(f"📊 Stats: {generation_stats}")
            
            return generation_stats
            
        except Exception as e:
            logger.error(f"❌ Daily content generation failed: {e}")
            raise
    
    def _generate_language_content(self, language: str) -> Dict[str, Any]:
        """Generate content for a specific language"""
        logger.info(f"Generating content for {language}...")
        
        stats = {"generated": 0, "accepted": 0, "rejected": 0, "topics": []}
        topics = self.content_topics.get(language, ["general"])
        
        for difficulty in ["beginner", "intermediate", "advanced"]:
            targets = self.daily_content_targets[difficulty]
            
            for topic in topics[:8]:  # Limit topics per day to avoid overwhelming
                # Generate flashcards
                flashcard_count = targets["flashcards"] // len(topics[:8])
                flashcard_stats = self._generate_topic_content(
                    language, difficulty, topic, "flashcard", flashcard_count
                )
                
                stats["generated"] += flashcard_stats["generated"]
                stats["accepted"] += flashcard_stats["accepted"]
                stats["rejected"] += flashcard_stats["rejected"]
                
                if topic not in stats["topics"]:
                    stats["topics"].append(topic)
                
                # Add small delay to avoid overwhelming the LLM
                time.sleep(0.5)
        
        return stats
    
    def _generate_topic_content(self, language: str, difficulty: str, topic: str, content_type: str, count: int) -> Dict[str, Any]:
        """Generate content for a specific topic and difficulty"""
        
        stats = {"generated": 0, "accepted": 0, "rejected": 0}
        
        try:
            # Create content generation request
            request = ContentGenerationRequest(
                user_id=0,  # System-generated content
                content_type=content_type,
                target_language=language,
                difficulty_level=difficulty,
                topic=topic,
                quantity=count,
                session_type="focused_practice"
            )
            
            # Generate content using quality gateway
            result = self.quality_gateway.generate_quality_content(request)
            
            if result.success:
                # Save accepted content to database
                saved_count = self._save_content_to_database(result.content, language, difficulty, topic)
                
                stats["generated"] = len(result.content)
                stats["accepted"] = saved_count
                stats["rejected"] = len(result.content) - saved_count
                
                logger.info(f"✅ Generated {saved_count}/{len(result.content)} {content_type}s for {topic} ({difficulty})")
            else:
                logger.warning(f"⚠️ Failed to generate {content_type} for {topic} ({difficulty})")
        
        except Exception as e:
            logger.error(f"❌ Error generating {content_type} for {topic}: {e}")
        
        return stats
    
    def _save_content_to_database(self, content_items: List[Dict[str, Any]], language: str, difficulty: str, topic: str) -> int:
        """Save generated content to database"""
        
        saved_count = 0
        difficulty_mapping = {"beginner": 1, "intermediate": 3, "advanced": 5}
        
        with transaction.atomic():
            for item in content_items:
                try:
                    # Check if similar content already exists
                    existing = ContentItem.objects.filter(
                        question_text=item.get("question", ""),
                        language=language
                    ).first()
                    
                    if existing:
                        continue  # Skip duplicates
                    
                    # Create new content item
                    content_item = ContentItem.objects.create(
                        content_type="flashcard",
                        language=language,
                        difficulty=difficulty_mapping.get(difficulty, 1),
                        question_text=item.get("question", ""),
                        answer_text=item.get("correct_answer", ""),
                        options=item.get("options", []),
                        explanation=item.get("explanation", ""),
                        tags=item.get("tags", [topic]),
                        metadata={
                            "topic": topic,
                            "generated_at": datetime.now().isoformat(),
                            "generation_method": "background_pipeline",
                            "quality_score": getattr(item, "quality_score", 0.8)
                        },
                        is_active=True
                    )
                    
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to save content item: {e}")
                    continue
        
        return saved_count
    
    def _cleanup_old_content(self):
        """Clean up old generated content to maintain database performance"""
        
        # Remove content older than 30 days that hasn't been used
        cutoff_date = datetime.now() - timedelta(days=30)
        
        old_unused_content = ContentItem.objects.filter(
            created_at__lt=cutoff_date,
            metadata__generation_method="background_pipeline"
        ).exclude(
            id__in=UserContentPerformance.objects.values_list('content_item_id', flat=True)
        )
        
        deleted_count = old_unused_content.count()
        old_unused_content.delete()
        
        if deleted_count > 0:
            logger.info(f"🧹 Cleaned up {deleted_count} old unused content items")
    
    def _generate_personalized_content(self):
        """Generate personalized content for active users"""
        
        # Get active users (logged in within last 7 days)
        active_users = User.objects.filter(
            last_login__gte=datetime.now() - timedelta(days=7)
        ).select_related('profile')[:50]  # Limit to avoid overwhelming
        
        for user in active_users:
            try:
                self._generate_user_specific_content(user)
                time.sleep(0.2)  # Small delay between users
            except Exception as e:
                logger.error(f"Failed to generate content for user {user.id}: {e}")
    
    def _generate_user_specific_content(self, user):
        """Generate content tailored to a specific user's needs"""
        
        try:
            profile = user.profile
            
            # Get user's weak areas from performance data
            weak_areas = UserContentPerformance.objects.filter(
                user=user,
                proficiency_score__lt=0.7
            ).values_list('content_item__tags', flat=True)[:5]
            
            # Generate targeted content for weak areas
            for weak_area_tags in weak_areas:
                if weak_area_tags:
                    topic = weak_area_tags[0] if isinstance(weak_area_tags, list) else str(weak_area_tags)
                    
                    request = ContentGenerationRequest(
                        user_id=user.id,
                        content_type="flashcard",
                        target_language=profile.target_language.lower(),
                        difficulty_level=profile.skill_level.lower(),
                        topic=topic,
                        quantity=3,  # Small batch for personalized content
                        session_type="adaptive_review"
                    )
                    
                    result = self.quality_gateway.generate_quality_content(request)
                    
                    if result.success:
                        self._save_content_to_database(
                            result.content, 
                            profile.target_language.lower(), 
                            profile.skill_level.lower(), 
                            topic
                        )
        
        except Exception as e:
            logger.error(f"Error generating personalized content for user {user.id}: {e}")


class Command(BaseCommand):
    """Django management command to run background content generation"""
    
    help = 'Generate background content for TalonTalk using local Ollama'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--continuous',
            action='store_true',
            help='Run continuously (for production deployment)',
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=3600,  # 1 hour
            help='Interval between generation cycles in seconds',
        )
    
    def handle(self, *args, **options):
        generator = BackgroundContentGenerator()
        
        if options['continuous']:
            self.stdout.write("🔄 Starting continuous content generation...")
            
            while True:
                try:
                    stats = generator.run_daily_content_generation()
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Generated {stats['total_accepted']} content items")
                    )
                    
                    # Wait for next cycle
                    time.sleep(options['interval'])
                    
                except KeyboardInterrupt:
                    self.stdout.write("⏹️ Stopping content generation...")
                    break
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"❌ Generation cycle failed: {e}")
                    )
                    time.sleep(60)  # Wait 1 minute before retrying
        else:
            # Single run
            stats = generator.run_daily_content_generation()
            self.stdout.write(
                self.style.SUCCESS(f"✅ Generated {stats['total_accepted']} content items")
            )
