#!/usr/bin/env python3

import requests
import json
import sys
import time

BASE_URL = "http://127.0.0.1:8000"


def test_care_system():
    """Comprehensive test of the C.A.R.E. system"""
    print("🧪 Testing C.A.R.E. System Comprehensive...")

    # Test each phase endpoint
    phases = ["contextualize", "acquire", "reinforce", "extend"]

    for phase in phases:
        print(f"\n📝 Testing {phase.title()} phase...")
        try:
            url = f"{BASE_URL}/care/api/phase/{phase}/"
            response = requests.get(url, timeout=10)

            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ {phase.title()} API working")
                    print(f"Response keys: {list(data.keys())}")

                    # Check expected structure
                    if "success" in data and data["success"]:
                        print(f"✅ Success field present and true")
                        if "content" in data:
                            content = data["content"]
                            print(
                                f"Content keys: {list(content.keys()) if isinstance(content, dict) else 'Not a dict'}"
                            )
                        else:
                            print("⚠️ No content field in response")
                    else:
                        print(f"❌ Success field missing or false")

                except json.JSONDecodeError as e:
                    print(f"❌ Invalid JSON response: {e}")
                    print(f"Response text: {response.text[:200]}...")
            else:
                print(f"❌ HTTP Error {response.status_code}")
                print(f"Response: {response.text[:200]}...")

        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")

    # Test lesson page load
    print(f"\n🌐 Testing lesson page load...")
    try:
        url = f"{BASE_URL}/care/lesson/1/"
        response = requests.get(url, timeout=10)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ Lesson page loads")

            # Check for key elements in HTML
            html = response.text
            checks = [
                ("care-lesson.js", "JavaScript file included"),
                ("contextualizePhase", "Contextualize phase container"),
                ("acquirePhase", "Acquire phase container"),
                ("reinforcePhase", "Reinforce phase container"),
                ("extendPhase", "Extend phase container"),
                ("CARELessonManager", "JavaScript class present"),
            ]

            for check, description in checks:
                if check in html:
                    print(f"✅ {description}")
                else:
                    print(f"❌ {description} missing")

        else:
            print(f"❌ HTTP Error {response.status_code}")
            print(f"Response: {response.text[:200]}...")

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")


def test_javascript_syntax():
    """Test JavaScript file for syntax errors"""
    print(f"\n🔍 Testing JavaScript syntax...")

    try:
        js_file = "c:\\Users\\<USER>\\Desktop\\SaaS\\LinguaJoy\\talontalk\\static\\js\\care-lesson.js"
        with open(js_file, "r", encoding="utf-8") as f:
            js_content = f.read()

        print(f"✅ JavaScript file loaded, length: {len(js_content)} characters")

        # Basic syntax checks
        syntax_checks = [
            ("class CARELessonManager", "Main class defined"),
            ("constructor()", "Constructor present"),
            ("loadPhase(", "loadPhase method"),
            ("setupPhaseSpecificListeners", "Event listener setup"),
            ("DOMContentLoaded", "DOM ready handler"),
        ]

        for check, description in syntax_checks:
            if check in js_content:
                print(f"✅ {description}")
            else:
                print(f"❌ {description} missing")

    except Exception as e:
        print(f"❌ Error reading JavaScript file: {e}")


if __name__ == "__main__":
    print("🚀 C.A.R.E. System Final Test")
    print("=" * 50)

    test_javascript_syntax()
    test_care_system()

    print("\n" + "=" * 50)
    print("✨ Test complete!")
