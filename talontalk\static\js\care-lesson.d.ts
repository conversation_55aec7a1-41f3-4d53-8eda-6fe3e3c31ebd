/**
 * C.A.R.E. Framework Interactive Lesson System
 * Handles phase navigation, progress tracking, and AI tutor interaction
 */
declare class CARELessonManager {
    currentPhase: string;
    phaseProgress: number;
    totalPhases: number;
    phases: string[];
    init(): void;
    setupEventListeners(): void;
    loadPhase(phaseName: any): void;
    loadPhaseContent(phaseName: any): void;
    renderPhaseContent(phaseName: any, content: any): void;
    generatePhaseHTML(phaseName: any, content: any): string;
    generateContextualizeHTML(content: any): string;
    generateAcquireHTML(content: any): string;
    generateReinforceHTML(content: any): string;
    generateExtendHTML(content: any): string;
    showFallbackContent(phaseName: any): void;
    setupPhaseSpecificListeners(phaseName: any): void;
    handlePracticeAnswer(button: any): void;
    handleConversationResponse(button: any): void;
    nextPhase(): void;
    completeLesson(): void;
    updatePhaseIndicators(currentPhase: any): void;
    updateProgressBar(): void;
    openAITutor(): void;
    closeAITutor(): void;
    sendTutorMessage(): void;
    generateTutorResponse(userMessage: any): string;
    getCSRFToken(): any;
}
//# sourceMappingURL=care-lesson.d.ts.map