{% extends "base.html" %}
{% load static %}

{% block title %}Quality Dashboard - TalonTalk{% endblock %}

{% block extra_css %}
<style>
.quality-card {
    @apply bg-white rounded-lg shadow-md p-6 border-l-4;
}
.quality-excellent { @apply border-green-500; }
.quality-good { @apply border-blue-500; }
.quality-acceptable { @apply border-yellow-500; }
.quality-poor { @apply border-orange-500; }
.quality-rejected { @apply border-red-500; }

.metric-card {
    @apply bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-200;
}

.language-card {
    @apply bg-white rounded-lg shadow-sm p-4 border hover:shadow-md transition-shadow;
}

.issue-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}
.issue-critical { @apply bg-red-100 text-red-800; }
.issue-high { @apply bg-orange-100 text-orange-800; }
.issue-medium { @apply bg-yellow-100 text-yellow-800; }
.issue-low { @apply bg-blue-100 text-blue-800; }
.issue-info { @apply bg-gray-100 text-gray-800; }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Quality Control Dashboard</h1>
            <p class="mt-2 text-gray-600">Monitor content quality and system performance across all languages</p>
        </div>

        <!-- Quality Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Overall Quality Score</p>
                        <p class="text-2xl font-bold text-gray-900" id="overall-score">--</p>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Content Items</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-content">--</p>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Languages</p>
                        <p class="text-2xl font-bold text-gray-900" id="active-languages">--</p>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Issues Found</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-issues">--</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quality Distribution Chart -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Quality Distribution</h2>
            <div class="grid grid-cols-5 gap-4" id="quality-distribution">
                <!-- Quality levels will be populated by JavaScript -->
            </div>
        </div>

        <!-- Language Statistics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Language Breakdown -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Language Breakdown</h2>
                <div id="language-breakdown" class="space-y-3">
                    <!-- Language stats will be populated by JavaScript -->
                </div>
            </div>

            <!-- Common Issues -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Common Issues</h2>
                <div id="common-issues" class="space-y-3">
                    <!-- Issues will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Improvement Suggestions -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Improvement Suggestions</h2>
            <div id="improvement-suggestions" class="space-y-2">
                <!-- Suggestions will be populated by JavaScript -->
            </div>
        </div>

        <!-- Language Selection for Detailed Report -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Language-Specific Analysis</h2>
            <div class="flex items-center space-x-4 mb-4">
                <select id="language-selector" class="form-select rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="">Select a language...</option>
                </select>
                <button id="generate-report" class="btn btn-primary">Generate Report</button>
            </div>
            <div id="language-report" class="hidden">
                <!-- Language-specific report will be populated here -->
            </div>
        </div>
    </div>
</div>

<script>
class QualityDashboard {
    constructor() {
        this.init();
    }

    async init() {
        await this.loadOverallMetrics();
        await this.loadLanguageStatistics();
        this.setupEventListeners();
    }

    async loadOverallMetrics() {
        try {
            const response = await fetch('/api/language/quality/report/');
            const data = await response.json();
            
            if (data.success) {
                this.updateOverallMetrics(data.report.time_period_metrics);
                this.updateQualityDistribution(data.report.time_period_metrics.quality_distribution);
                this.updateCommonIssues(data.report.time_period_metrics.common_issues);
                this.updateImprovementSuggestions(data.report.time_period_metrics.improvement_suggestions);
            }
        } catch (error) {
            console.error('Error loading metrics:', error);
        }
    }

    async loadLanguageStatistics() {
        try {
            const response = await fetch('/api/language/statistics/');
            const data = await response.json();
            
            if (data.success) {
                this.updateLanguageBreakdown(data.statistics);
                this.populateLanguageSelector(data.statistics);
                document.getElementById('active-languages').textContent = data.active_languages;
            }
        } catch (error) {
            console.error('Error loading language statistics:', error);
        }
    }

    updateOverallMetrics(metrics) {
        document.getElementById('overall-score').textContent = Math.round(metrics.average_quality_score) + '%';
        document.getElementById('total-content').textContent = metrics.total_content;
        document.getElementById('total-issues').textContent = Object.values(metrics.common_issues).reduce((a, b) => a + b, 0);
    }

    updateQualityDistribution(distribution) {
        const container = document.getElementById('quality-distribution');
        const levels = ['excellent', 'good', 'acceptable', 'poor', 'rejected'];
        const colors = ['green', 'blue', 'yellow', 'orange', 'red'];
        
        container.innerHTML = '';
        
        levels.forEach((level, index) => {
            const count = distribution[level] || 0;
            const div = document.createElement('div');
            div.className = `quality-card quality-${level}`;
            div.innerHTML = `
                <div class="text-center">
                    <div class="text-2xl font-bold text-${colors[index]}-600">${count}</div>
                    <div class="text-sm text-gray-600 capitalize">${level}</div>
                </div>
            `;
            container.appendChild(div);
        });
    }

    updateLanguageBreakdown(languages) {
        const container = document.getElementById('language-breakdown');
        container.innerHTML = '';
        
        languages.slice(0, 8).forEach(lang => {
            const div = document.createElement('div');
            div.className = 'language-card';
            div.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">${lang.flag}</span>
                        <div>
                            <div class="font-medium">${lang.name}</div>
                            <div class="text-sm text-gray-500">${lang.native_name}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-medium">${lang.content_count} items</div>
                        <div class="text-sm text-gray-500">${lang.user_count} users</div>
                    </div>
                </div>
            `;
            container.appendChild(div);
        });
    }

    updateCommonIssues(issues) {
        const container = document.getElementById('common-issues');
        container.innerHTML = '';
        
        Object.entries(issues).forEach(([issue, count]) => {
            const div = document.createElement('div');
            div.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
            div.innerHTML = `
                <span class="font-medium">${issue.replace(/_/g, ' ')}</span>
                <span class="issue-badge issue-medium">${count} occurrences</span>
            `;
            container.appendChild(div);
        });
    }

    updateImprovementSuggestions(suggestions) {
        const container = document.getElementById('improvement-suggestions');
        container.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const div = document.createElement('div');
            div.className = 'flex items-start p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400';
            div.innerHTML = `
                <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                <span class="text-blue-800">${suggestion}</span>
            `;
            container.appendChild(div);
        });
    }

    populateLanguageSelector(languages) {
        const selector = document.getElementById('language-selector');
        languages.forEach(lang => {
            if (lang.active) {
                const option = document.createElement('option');
                option.value = lang.code;
                option.textContent = `${lang.flag} ${lang.name}`;
                selector.appendChild(option);
            }
        });
    }

    setupEventListeners() {
        document.getElementById('generate-report').addEventListener('click', () => {
            this.generateLanguageReport();
        });
    }

    async generateLanguageReport() {
        const language = document.getElementById('language-selector').value;
        if (!language) return;

        try {
            const response = await fetch(`/api/language/quality/report/?language=${language}`);
            const data = await response.json();
            
            if (data.success) {
                this.displayLanguageReport(data.report, language);
            }
        } catch (error) {
            console.error('Error generating language report:', error);
        }
    }

    displayLanguageReport(report, language) {
        const container = document.getElementById('language-report');
        container.className = 'mt-4 p-4 bg-gray-50 rounded-lg';
        
        const langInfo = report.language_specific[language];
        container.innerHTML = `
            <h3 class="text-lg font-semibold mb-3">Report for ${language}</h3>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p><strong>Total Content:</strong> ${langInfo.total_content}</p>
                    <p><strong>Active Support:</strong> ${langInfo.active_support ? 'Yes' : 'No'}</p>
                    <p><strong>Content Quality:</strong> ${langInfo.content_quality_rating}</p>
                </div>
                <div>
                    <p><strong>Generated:</strong> ${report.timestamp}</p>
                </div>
            </div>
        `;
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new QualityDashboard();
});
</script>
{% endblock %}
