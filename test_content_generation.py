#!/usr/bin/env python
"""
Test script for content generation system
"""

import os
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()

print("🧪 Testing Content Generation Service...")

try:
    from lessons.services import content_generator, content_pipeline

    print("✅ Services imported successfully!")

    print(
        f"🎯 LLM Config: {content_generator.config.provider.value} - {content_generator.config.model_name}"
    )

    # Test basic content generation
    result = content_generator.generate_care_content_batch(
        language="spanish",
        difficulty_level=1,
        content_types=["flashcard", "mcq"],
        batch_size=2,
    )

    print(f"📊 Content Generation Result:")
    print(f'   Success: {result.get("success", False)}')
    print(f'   Batch Size: {result.get("batch_size", 0)}')

    if result.get("content_items"):
        print(f'   Generated {len(result["content_items"])} content items:')
        for i, item in enumerate(result["content_items"][:3], 1):
            print(f"   {i}. {item.type}: {item.question_text[:60]}...")
    else:
        print("   No content items generated")

    if result.get("errors"):
        print(f'   Errors: {result["errors"]}')

    print("\n🎯 Content Pipeline initialized successfully!")

except ImportError as e:
    print(f"❌ Import Error: {e}")
except Exception as e:
    print(f"❌ Runtime Error: {e}")
    import traceback

    traceback.print_exc()
