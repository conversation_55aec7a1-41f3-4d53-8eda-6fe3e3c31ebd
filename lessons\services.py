"""
Content Generation Service for TalonTalk Language Learning
Implements C.A.R.E. framework with automated content pipeline and spaced repetition
"""

import logging
from typing import Dict, List, Optional, Any
from django.utils import timezone
from datetime import timedelta
import random

from .models import (
    ContentItem,
    UserContentPerformance,
    UserLearningProfile,
    UserLessonQueue,
    Lesson,
)
from ai_services.llm_flashcards import (
    LLMFlashcardService,
    FlashcardRequest,
    DifficultyLevel,
    ExerciseType,
)

logger = logging.getLogger(__name__)


class ContentGenerationService:
    """
    Intelligent content generation service using C.A.R.E. framework
    """

    def __init__(self):
        self.llm_service = LLMFlashcardService()

    def generate_care_content_batch(
        self,
        language: str = "spanish",
        difficulty_level: int = 1,
        content_types: List[str] = None,
        batch_size: int = 5,
    ) -> Dict[str, Any]:
        """
        Generate a batch of C.A.R.E. framework content
        """
        if content_types is None:
            content_types = ["flashcard", "mcq", "translation"]

        try:
            content_items = []
            care_phases = ["contextualize", "acquire", "reinforce", "extend"]

            for i in range(batch_size):
                # Cycle through C.A.R.E. phases
                care_phase = care_phases[i % len(care_phases)]
                content_type = random.choice(content_types)

                # Generate content using AI
                content_item = self._generate_single_content_item(
                    language=language,
                    difficulty_level=difficulty_level,
                    content_type=content_type,
                    care_phase=care_phase,
                )

                if content_item:
                    content_items.append(content_item)

            return {
                "success": True,
                "content_items": content_items,
                "batch_size": len(content_items),
                "care_phases_covered": list(
                    set(
                        [
                            item.tags[0] if item.tags else "acquire"
                            for item in content_items
                        ]
                    )
                ),
            }

        except Exception as e:
            logger.error(f"Error generating content batch: {e}")
            return {"success": False, "error": str(e), "content_items": []}

    def _generate_single_content_item(
        self, language: str, difficulty_level: int, content_type: str, care_phase: str
    ) -> Optional[ContentItem]:
        """Generate a single content item using AI"""

        try:
            # Map content type to exercise type
            exercise_type_map = {
                "flashcard": ExerciseType.TRANSLATION,
                "mcq": ExerciseType.MULTIPLE_CHOICE,
                "translation": ExerciseType.TRANSLATION,
                "grammar": ExerciseType.FILL_BLANK,
            }

            exercise_type = exercise_type_map.get(
                content_type, ExerciseType.MULTIPLE_CHOICE
            )

            # Create AI request with C.A.R.E. context
            request = FlashcardRequest(
                language="english",  # User's native language
                target_language=language,
                difficulty=(
                    DifficultyLevel.BEGINNER
                    if difficulty_level <= 2
                    else (
                        DifficultyLevel.INTERMEDIATE
                        if difficulty_level <= 4
                        else DifficultyLevel.ADVANCED
                    )
                ),
                exercise_type=exercise_type,
                context=self._get_care_context(care_phase, difficulty_level),
            )

            # Generate content using LLM
            response = self.llm_service.generate_flashcard(request)

            # Create progressive hints based on C.A.R.E. phase
            progressive_hints = self._generate_progressive_hints(
                response.hint, care_phase
            )

            # Create and save content item
            content_item = ContentItem.objects.create(
                type=content_type,
                question_text=response.question,
                answer_text=response.correct_answer,
                choices_json=response.options or [],
                hint_text=response.hint,
                progressive_hints=progressive_hints,
                explanation_text=response.explanation,
                difficulty=difficulty_level,
                language=language,
                tags=[care_phase, f"difficulty_{difficulty_level}"],
            )

            logger.info(
                f"Generated {care_phase} {content_type} content: {content_item.id}"
            )
            return content_item

        except Exception as e:
            logger.error(f"Error generating single content item: {e}")
            return None

    def _get_care_context(self, care_phase: str, difficulty_level: int) -> str:
        """Get context prompt based on C.A.R.E. phase"""

        contexts = {
            "contextualize": f"Create a {['basic', 'simple', 'intermediate', 'advanced', 'expert'][difficulty_level-1]} exercise that sets up a real-world context or scenario where the learner would use this language.",
            "acquire": f"Generate a {['beginner-friendly', 'elementary', 'intermediate', 'challenging', 'advanced'][difficulty_level-1]} exercise focused on introducing and practicing new vocabulary or grammar concepts clearly.",
            "reinforce": f"Create a {['basic', 'simple', 'moderate', 'challenging', 'advanced'][difficulty_level-1]} reinforcement exercise that helps solidify previously learned concepts through repetition and practice.",
            "extend": f"Design a {['creative', 'applied', 'practical', 'complex', 'sophisticated'][difficulty_level-1]} exercise that challenges learners to use their knowledge in new, creative, or more complex situations.",
        }

        return contexts.get(
            care_phase, "Create an engaging language learning exercise."
        )

    def _generate_progressive_hints(self, base_hint: str, care_phase: str) -> List[str]:
        """Generate progressive hints based on C.A.R.E. phase"""

        if not base_hint:
            base_hint = "Think about the context and what you've learned."

        hints = [base_hint]

        # Add phase-specific progressive hints
        if care_phase == "contextualize":
            hints.extend(
                [
                    "Consider the real-world situation described.",
                    "Think about when you would use this in daily life.",
                ]
            )
        elif care_phase == "acquire":
            hints.extend(
                [
                    "Focus on the new vocabulary or grammar rule.",
                    "Remember the pattern you just learned.",
                ]
            )
        elif care_phase == "reinforce":
            hints.extend(
                [
                    "This is similar to what we practiced before.",
                    "Use what you already know to figure this out.",
                ]
            )
        elif care_phase == "extend":
            hints.extend(
                [
                    "Try applying the concept in a new way.",
                    "Think creatively about how to use what you know.",
                ]
            )

        return hints


class SpacedRepetitionService:
    """
    Implements spaced repetition algorithm for optimal learning
    """

    @staticmethod
    def calculate_next_review_date(
        performance: UserContentPerformance,
    ) -> timezone.datetime:
        """Calculate when content should be reviewed again"""

        base_interval = 1  # Start with 1 day

        # Adjust based on performance
        if performance.proficiency_score >= 0.8:
            # High proficiency - longer intervals
            interval = base_interval * (2**performance.consecutive_correct)
        elif performance.proficiency_score >= 0.5:
            # Medium proficiency - standard intervals
            interval = base_interval * (1.5**performance.consecutive_correct)
        else:
            # Low proficiency - shorter intervals
            interval = max(1, base_interval * (1.2**performance.consecutive_correct))

        # Cap maximum interval at 30 days
        interval = min(interval, 30)

        return timezone.now() + timedelta(days=interval)

    @staticmethod
    def get_due_content(user, limit: int = 10) -> List[ContentItem]:
        """Get content items due for review"""

        performances = (
            UserContentPerformance.objects.filter(
                user=user, next_review_date__lte=timezone.now()
            )
            .select_related("content_item")
            .order_by("next_review_date")[:limit]
        )

        return [p.content_item for p in performances]


class AdaptiveLearningEngine:
    """
    Adaptive learning engine that personalizes content based on user performance
    """

    def __init__(self):
        self.content_service = ContentGenerationService()
        self.spaced_repetition = SpacedRepetitionService()

    def populate_user_queue(
        self, user_id: int, queue_type: str = "daily"
    ) -> Dict[str, Any]:
        """
        Populate user's learning queue with personalized content
        """
        try:
            from django.contrib.auth import get_user_model

            User = get_user_model()

            user = User.objects.get(id=user_id)

            # Get user's learning profile
            profile, _ = UserLearningProfile.objects.get_or_create(user=user)

            if queue_type == "daily":
                content_ids = self._generate_daily_queue(user, profile)
            elif queue_type == "review":
                content_ids = self._generate_review_queue(user, profile)
            elif queue_type == "weak_areas":
                content_ids = self._generate_weak_areas_queue(user, profile)
            else:
                content_ids = self._generate_daily_queue(user, profile)

            # Create or update queue
            queue, created = UserLessonQueue.objects.get_or_create(
                user=user,
                queue_type=queue_type,
                status="active",
                defaults={
                    "expires_at": timezone.now() + timedelta(hours=24),
                    "ordered_content_ids": content_ids,
                    "total_items": len(content_ids),
                },
            )

            if not created:
                # Update existing queue
                queue.ordered_content_ids = content_ids
                queue.total_items = len(content_ids)
                queue.current_position = 0
                queue.expires_at = timezone.now() + timedelta(hours=24)
                queue.save()

            return {
                "success": True,
                "queue_id": queue.id,
                "content_count": len(content_ids),
                "expires_at": queue.expires_at.isoformat(),
            }

        except Exception as e:
            logger.error(f"Error populating user queue: {e}")
            return {"success": False, "error": str(e)}

    def _generate_daily_queue(self, user, profile) -> List[int]:
        """Generate daily learning queue with balanced C.A.R.E. content"""

        content_ids = []

        # Get spaced repetition content (40% of queue)
        due_content = self.spaced_repetition.get_due_content(user, limit=4)
        content_ids.extend([c.id for c in due_content])

        # Get new content based on difficulty (60% of queue)
        new_content = ContentItem.objects.filter(
            language=getattr(user.profile, "target_language", "spanish"),
            difficulty=profile.preferred_difficulty,
            is_active=True,
        ).exclude(
            id__in=UserContentPerformance.objects.filter(user=user).values_list(
                "content_item_id", flat=True
            )
        )[
            :6
        ]

        content_ids.extend([c.id for c in new_content])

        # Shuffle for variety
        random.shuffle(content_ids)

        return content_ids[:10]  # Limit to 10 items per day

    def _generate_review_queue(self, user, profile) -> List[int]:
        """Generate review queue focused on spaced repetition"""

        due_content = self.spaced_repetition.get_due_content(user, limit=10)
        return [c.id for c in due_content]

    def _generate_weak_areas_queue(self, user, profile) -> List[int]:
        """Generate queue focused on weak areas"""

        weak_performances = (
            UserContentPerformance.objects.filter(
                user=user, proficiency_score__lt=0.6, times_seen__gte=2
            )
            .select_related("content_item")
            .order_by("proficiency_score")[:10]
        )

        return [p.content_item.id for p in weak_performances]


# Initialize global services
content_pipeline = AdaptiveLearningEngine()
content_generator = ContentGenerationService()
spaced_repetition = SpacedRepetitionService()
