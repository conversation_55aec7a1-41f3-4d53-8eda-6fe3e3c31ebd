# Django Template Migration Guide: JavaScript to TypeScript

## Overview
This guide documents the migration from JavaScript to TypeScript for the TalonTalk C.A.R.E. lesson system, including necessary template updates and best practices for future development.

## Migration Summary

### Files Updated
1. **Main C.A.R.E. Lesson Template**
   - File: `talontalk/templates/care/lesson.html`
   - Change: Updated script tag to use TypeScript build
   - Before: `<script src="{% static 'js/care-lesson.js' %}"></script>`
   - After: `<script type="module" src="{% static 'js/dist/care-lesson.js' %}"></script>`

2. **Debug Templates**
   - File: `debug_care_js.html`
   - Change: Updated to use TypeScript build for debugging
   - Before: `<script src="/static/js/care-lesson.js"></script>`
   - After: `<script type="module" src="/static/js/dist/care-lesson.js"></script>`

### Key Changes Explained

#### ES6 Module Loading
The TypeScript build generates ES6 modules, so templates now use:
```html
<script type="module" src="{% static 'js/dist/care-lesson.js' %}"></script>
```

#### Benefits of Migration
1. **Type Safety**: Full TypeScript type checking prevents runtime errors
2. **Better IDE Support**: Enhanced autocomplete and error detection
3. **Modern JavaScript**: Uses latest ES6+ features and imports
4. **Improved Maintainability**: Clearer interfaces and documentation
5. **Future-Proof**: Easier to extend and modify

## Template Integration Guidelines

### For New Templates
When creating new lesson templates that use the C.A.R.E. system:

1. **Use the TypeScript Build**:
   ```html
   <script type="module" src="{% static 'js/dist/care-lesson.js' %}"></script>
   ```

2. **Initialize with TypeScript-Style Configuration**:
   ```html
   <script type="module">
   document.addEventListener('DOMContentLoaded', () => {
       const careManager = new CARELessonManager({
           lessonId: {{ lesson.id }},
           debugMode: {% if debug %}true{% else %}false{% endif %},
           initialPhase: '{{ initial_phase|default:"contextualize" }}',
           onPhaseChange: (phase, progress) => {
               console.log(`Phase changed to: ${phase}, Progress: ${progress}%`);
           },
           onLessonComplete: (completionData) => {
               console.log('Lesson completed:', completionData);
               // Handle lesson completion
           }
       });
   });
   </script>
   ```

### Required HTML Structure
The TypeScript system expects the same HTML structure as the JavaScript version:

1. **Progress Elements**:
   ```html
   <div id="careProgressBar" class="progress-bar"></div>
   <div class="care-phase-indicator" data-phase="contextualize">...</div>
   ```

2. **Phase Content Areas**:
   ```html
   <div id="contextualizePhase" class="care-phase-content"></div>
   <div id="acquirePhase" class="care-phase-content hidden"></div>
   <div id="reinforcePhase" class="care-phase-content hidden"></div>
   <div id="extendPhase" class="care-phase-content hidden"></div>
   ```

3. **Navigation Elements**:
   ```html
   <button id="prevPhaseBtn">Previous</button>
   <button id="nextPhaseBtn">Next</button>
   <span id="currentPhaseNum">1</span>
   ```

## API Integration

### Backend Requirements
Ensure your Django views provide the necessary data structure:

```python
# In your view
context = {
    'lesson': lesson,
    'initial_phase': 'contextualize',  # Optional
    'progress_percentage': progress_calc(),
    'debug': settings.DEBUG,
    # ... other context
}
```

### API Endpoints
The TypeScript system expects the same API endpoints as the JavaScript version:
- `GET /api/lessons/{lesson_id}/care/phases/{phase}/` - Load phase content
- `POST /api/lessons/{lesson_id}/care/progress/` - Update progress
- `POST /api/lessons/{lesson_id}/care/tutor/` - AI tutor interaction

## Development Workflow

### Building TypeScript Changes
When modifying TypeScript code:

1. **Make changes in src/typescript/**
2. **Build the project**:
   ```bash
   npm run build:typescript
   ```
3. **Verify output in talontalk/static/js/dist/**
4. **Test in browser/Django template**

### Testing Templates
1. Use the development server: `python manage.py runserver`
2. Navigate to C.A.R.E. lessons to test functionality
3. Check browser console for any module loading errors
4. Use `typescript-demo.html` for isolated testing

### Debugging
1. **Enable debug mode** in CARELessonManager constructor
2. **Check browser console** for TypeScript-generated logs
3. **Use browser dev tools** to inspect module loading
4. **Test with different browsers** to ensure ES6 module compatibility

## Browser Compatibility

### Minimum Requirements
- Chrome 63+
- Firefox 60+
- Safari 11.1+
- Edge 79+

### Fallback Strategy
For older browsers, consider:
1. Transpiling to ES5 with Babel
2. Using a module loader like SystemJS
3. Providing a legacy JavaScript fallback

## File Structure Reference

```
talontalk/static/js/
├── dist/                          # TypeScript build output (use this)
│   ├── care-lesson.js            # Main C.A.R.E. manager
│   ├── care/
│   │   └── CARELessonManager.js  # Core manager class
│   ├── types/
│   │   ├── care.types.js         # C.A.R.E. type definitions
│   │   └── common.types.js       # Common utilities
│   └── utils/
│       └── index.js              # Utility functions
└── care-lesson.js                # Legacy JavaScript (deprecated)
```

## Migration Checklist

- [x] Update main C.A.R.E. lesson template
- [x] Update debug templates
- [x] Test template loading with TypeScript build
- [ ] Update any custom lesson templates (if applicable)
- [ ] Test cross-browser compatibility
- [ ] Update documentation for other developers
- [ ] Train team on TypeScript development workflow

## Future Improvements

1. **Add TypeScript types to Django context** for better integration
2. **Create TypeScript interfaces for API responses** 
3. **Implement more sophisticated error handling**
4. **Add automated testing for template integration**
5. **Consider server-side rendering for better SEO**

## Troubleshooting

### Common Issues

1. **Module not found errors**:
   - Ensure the TypeScript build was successful
   - Check file paths in script tags
   - Verify static files are being served correctly

2. **CARELessonManager is not defined**:
   - Make sure you're using `type="module"` in script tag
   - Check browser console for import errors

3. **Legacy functionality not working**:
   - Verify all HTML structure requirements are met
   - Check that event listeners are properly attached
   - Review API endpoint compatibility

### Support
For issues with the TypeScript migration, refer to:
- `TYPESCRIPT_IMPLEMENTATION_COMPLETE.md` for technical details
- `src/typescript/` source code with inline documentation
- `typescript-demo.html` for working examples
