"""
Django Management Command for Background Content Generation
Runs the proactive content pipeline using local Ollama
"""

import time
import logging
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from lessons.services.background_content_generator import BackgroundContentGenerator

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Django management command to run background content generation
    
    Usage:
    python manage.py generate_background_content                    # Single run
    python manage.py generate_background_content --continuous       # Continuous mode
    python manage.py generate_background_content --interval 1800    # Custom interval
    """
    
    help = 'Generate background content for TalonTalk using local Ollama'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--continuous',
            action='store_true',
            help='Run continuously (for production deployment)',
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=3600,  # 1 hour
            help='Interval between generation cycles in seconds (default: 3600)',
        )
        parser.add_argument(
            '--language',
            type=str,
            default='spanish',
            help='Target language for content generation (default: spanish)',
        )
        parser.add_argument(
            '--max-items',
            type=int,
            default=100,
            help='Maximum items to generate per cycle (default: 100)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without saving to database (for testing)',
        )
    
    def handle(self, *args, **options):
        """Main command handler"""
        
        # Validate Ollama is available
        if not self._check_ollama_availability():
            raise CommandError("❌ Ollama is not available. Please ensure it's running locally.")
        
        # Initialize generator
        try:
            generator = BackgroundContentGenerator()
        except Exception as e:
            raise CommandError(f"❌ Failed to initialize content generator: {e}")
        
        # Display startup information
        self.stdout.write("🚀 TalonTalk Background Content Generator")
        self.stdout.write(f"📊 Mode: {'Continuous' if options['continuous'] else 'Single Run'}")
        self.stdout.write(f"🌍 Language: {options['language']}")
        self.stdout.write(f"📝 Max Items: {options['max_items']}")
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING("⚠️ DRY RUN MODE - No content will be saved"))
        
        if options['continuous']:
            self._run_continuous_mode(generator, options)
        else:
            self._run_single_cycle(generator, options)
    
    def _run_continuous_mode(self, generator, options):
        """Run generator in continuous mode"""
        
        self.stdout.write("🔄 Starting continuous content generation...")
        self.stdout.write(f"⏰ Interval: {options['interval']} seconds")
        self.stdout.write("Press Ctrl+C to stop")
        
        cycle_count = 0
        
        while True:
            try:
                cycle_count += 1
                self.stdout.write(f"\n🔄 Starting generation cycle #{cycle_count}")
                
                start_time = time.time()
                stats = generator.run_daily_content_generation()
                cycle_time = time.time() - start_time
                
                # Display results
                self._display_generation_stats(stats, cycle_time)
                
                # Wait for next cycle
                self.stdout.write(f"⏳ Waiting {options['interval']} seconds until next cycle...")
                time.sleep(options['interval'])
                
            except KeyboardInterrupt:
                self.stdout.write("\n⏹️ Stopping content generation...")
                self.stdout.write(f"📊 Completed {cycle_count} generation cycles")
                break
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"❌ Generation cycle #{cycle_count} failed: {e}")
                )
                self.stdout.write("⏳ Waiting 60 seconds before retrying...")
                time.sleep(60)
    
    def _run_single_cycle(self, generator, options):
        """Run a single generation cycle"""
        
        self.stdout.write("▶️ Starting single content generation cycle...")
        
        try:
            start_time = time.time()
            stats = generator.run_daily_content_generation()
            cycle_time = time.time() - start_time
            
            # Display results
            self._display_generation_stats(stats, cycle_time)
            
            self.stdout.write(self.style.SUCCESS("✅ Content generation completed successfully"))
            
        except Exception as e:
            raise CommandError(f"❌ Content generation failed: {e}")
    
    def _display_generation_stats(self, stats, cycle_time):
        """Display generation statistics"""
        
        self.stdout.write("\n📊 Generation Statistics:")
        self.stdout.write(f"  ⏱️  Cycle Time: {cycle_time:.2f} seconds")
        self.stdout.write(f"  📝 Total Generated: {stats['total_generated']}")
        self.stdout.write(f"  ✅ Total Accepted: {stats['total_accepted']}")
        self.stdout.write(f"  ❌ Total Rejected: {stats['total_rejected']}")
        
        if stats['total_generated'] > 0:
            acceptance_rate = (stats['total_accepted'] / stats['total_generated']) * 100
            self.stdout.write(f"  📈 Acceptance Rate: {acceptance_rate:.1f}%")
        
        if stats['languages_processed']:
            self.stdout.write(f"  🌍 Languages: {', '.join(stats['languages_processed'])}")
        
        if stats['topics_covered']:
            topics_display = ', '.join(stats['topics_covered'][:5])
            if len(stats['topics_covered']) > 5:
                topics_display += f" (+{len(stats['topics_covered']) - 5} more)"
            self.stdout.write(f"  📚 Topics: {topics_display}")
        
        # Quality indicators
        if acceptance_rate >= 80:
            self.stdout.write(self.style.SUCCESS("🎯 High quality content generation"))
        elif acceptance_rate >= 60:
            self.stdout.write(self.style.WARNING("⚠️ Moderate quality - consider prompt tuning"))
        else:
            self.stdout.write(self.style.ERROR("🚨 Low quality - review generation settings"))
    
    def _check_ollama_availability(self):
        """Check if Ollama is available and running"""
        
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            
            if response.status_code == 200:
                models = response.json().get("models", [])
                if models:
                    model_names = [model.get("name", "") for model in models]
                    self.stdout.write(f"✅ Ollama available with models: {', '.join(model_names[:3])}")
                    return True
                else:
                    self.stdout.write(self.style.WARNING("⚠️ Ollama running but no models found"))
                    return False
            else:
                return False
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Ollama check failed: {e}"))
            return False
    
    def _get_system_info(self):
        """Get system information for diagnostics"""
        
        info = {
            "django_version": getattr(settings, 'DJANGO_VERSION', 'Unknown'),
            "debug_mode": getattr(settings, 'DEBUG', False),
            "database": getattr(settings, 'DATABASES', {}).get('default', {}).get('ENGINE', 'Unknown'),
        }
        
        return info


# Additional utility functions for monitoring and maintenance

def get_content_generation_status():
    """Get current status of content generation system"""
    
    from lessons.models import ContentItem
    from datetime import datetime, timedelta
    
    # Get recent content statistics
    recent_content = ContentItem.objects.filter(
        created_at__gte=datetime.now() - timedelta(days=1),
        metadata__generation_method="background_pipeline"
    )
    
    status = {
        "recent_items_generated": recent_content.count(),
        "total_background_items": ContentItem.objects.filter(
            metadata__generation_method="background_pipeline"
        ).count(),
        "last_generation_time": recent_content.order_by('-created_at').first().created_at if recent_content.exists() else None,
        "active_languages": list(recent_content.values_list('language', flat=True).distinct()),
        "active_difficulties": list(recent_content.values_list('difficulty', flat=True).distinct()),
    }
    
    return status


def cleanup_failed_generations():
    """Clean up any failed or incomplete content generation attempts"""
    
    from lessons.models import ContentItem
    from datetime import datetime, timedelta
    
    # Remove items with incomplete data
    incomplete_items = ContentItem.objects.filter(
        metadata__generation_method="background_pipeline",
        question_text="",
    )
    
    deleted_count = incomplete_items.count()
    incomplete_items.delete()
    
    return {"cleaned_items": deleted_count}
