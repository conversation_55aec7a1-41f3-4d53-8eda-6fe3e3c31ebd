#!/usr/bin/env python
"""
Test Simple R1 Generation
Quick test of the simplified R1 flashcard generator
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talontalk.settings')
django.setup()

from lessons.simple_r1_generator import SimpleR1Generator
from lessons.models import ContentItem

def main():
    print("Simple R1 Generator Test")
    print("=" * 30)
    
    # Initialize generator
    generator = SimpleR1Generator()
    
    if not generator.client:
        print("ERROR: DeepSeek R1 not available")
        return
    
    print("DeepSeek R1 is available!")
    print("Testing simple generation...")
    
    # Test individual flashcard generation
    print("\nTest 1: Vocabulary Flashcard")
    vocab_card = generator.generate_vocabulary_flashcard("spanish", "family", 2)
    
    if vocab_card:
        print("SUCCESS: Vocabulary flashcard created")
        print(f"  Question: {vocab_card.question_text}")
        print(f"  Answer: {vocab_card.answer_text}")
        print(f"  Explanation: {vocab_card.explanation_text[:80]}...")
    else:
        print("FAILED: Could not create vocabulary flashcard")
    
    print("\nTest 2: Grammar Flashcard")
    grammar_card = generator.generate_grammar_flashcard("spanish", "present_tense", 2)
    
    if grammar_card:
        print("SUCCESS: Grammar flashcard created")
        print(f"  Question: {grammar_card.question_text}")
        print(f"  Answer: {grammar_card.answer_text}")
        print(f"  Explanation: {grammar_card.explanation_text[:80]}...")
    else:
        print("FAILED: Could not create grammar flashcard")
    
    print("\nTest 3: Batch Generation")
    result = generator.generate_batch("spanish", 5)
    
    if result["success"]:
        print(f"SUCCESS: Generated {result['generated_count']}/5 flashcards")
        print(f"Success rate: {result['success_rate']:.1f}%")
        
        # Show samples
        for i, card in enumerate(result["flashcards"][:2], 1):
            print(f"  Sample {i}: {card.question_text[:50]}...")
    else:
        print("FAILED: Batch generation failed")
    
    # Final summary
    total_count = ContentItem.objects.count()
    print(f"\nFinal Results:")
    print(f"  Total flashcards in database: {total_count}")
    
    if total_count > 0:
        print("SUCCESS: Simple R1 generation is working!")
    else:
        print("FAILED: No flashcards generated")

if __name__ == "__main__":
    main()
