from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
import json
import random

from .models import (
    Lesson,
    Vocabulary,
    UserLessonProgress,
    ContentItem,
    UserContentPerformance,
    UserLearningProfile,
)
from .serializers import (
    LessonSerializer,
    VocabularySerializer,
    UserLessonProgressSerializer,
)


class LessonViewSet(viewsets.ModelViewSet):
    queryset = Lesson.objects.all()
    serializer_class = LessonSerializer
    permission_classes = [AllowAny]

    @action(detail=True, methods=["get"])
    def content(self, request, pk=None):
        """Get lesson content for C.A.R.E. system"""
        lesson = self.get_object()

        # Get content items related to this lesson's vocabulary
        vocab_words = [
            word.lower() for word in lesson.vocabularies.values_list("word", flat=True)
        ]
        content_items = ContentItem.objects.filter(
            tags__overlap=vocab_words, is_active=True
        ).order_by("difficulty", "?")[
            :10
        ]  # Random selection of 10 items

        content_data = []
        for item in content_items:
            content_data.append(
                {
                    "id": item.id,
                    "type": item.type,
                    "question": item.question_text,
                    "answer": item.answer_text,
                    "choices": item.choices_list,
                    "hint": item.hint_text,
                    "explanation": item.explanation_text,
                    "difficulty": item.difficulty,
                    "tags": item.tags,
                }
            )

        return Response(
            {
                "lesson": LessonSerializer(lesson).data,
                "content": content_data,
                "total_items": len(content_data),
            }
        )

    @action(detail=True, methods=["post"])
    def start_session(self, request, pk=None):
        """Start a learning session for this lesson"""
        lesson = self.get_object()
        user = request.user if request.user.is_authenticated else None

        if not user:
            return Response({"error": "Authentication required"}, status=401)

        # Get or create user progress
        progress, created = UserLessonProgress.objects.get_or_create(
            user=user, lesson=lesson, defaults={"completed": False, "xp_earned": 0}
        )

        # Get personalized content for this user
        content_items = self.get_personalized_content(user, lesson)

        return Response(
            {
                "session_id": f"lesson_{lesson.id}_{user.id}",
                "lesson": LessonSerializer(lesson).data,
                "content": content_items,
                "progress": {
                    "completed": progress.completed,
                    "xp_earned": progress.xp_earned,
                },
            }
        )

    def get_personalized_content(self, user, lesson):
        """Get personalized content based on user's learning profile"""
        # Get vocabulary for this lesson
        vocab_words = [
            word.lower() for word in lesson.vocabularies.values_list("word", flat=True)
        ]

        # Get content items for this lesson's vocabulary
        content_items = ContentItem.objects.filter(
            tags__overlap=vocab_words, is_active=True
        )

        # Get user's performance data to personalize content
        user_performances = UserContentPerformance.objects.filter(
            user=user, content_item__in=content_items
        ).select_related("content_item")

        # Separate content into categories
        weak_content = []
        review_content = []
        new_content = []

        performed_item_ids = set()

        for perf in user_performances:
            performed_item_ids.add(perf.content_item.id)
            if perf.proficiency_score < 0.5:
                weak_content.append(perf.content_item)
            elif perf.needs_review:
                review_content.append(perf.content_item)

        # Get new content (not yet attempted)
        new_content = content_items.exclude(id__in=performed_item_ids)[:5]

        # Build personalized content mix
        content_mix = []

        # Add weak areas (highest priority)
        content_mix.extend(weak_content[:3])

        # Add review content
        content_mix.extend(review_content[:3])

        # Add new content
        content_mix.extend(new_content[:4])

        # If we don't have enough, fill with random content
        if len(content_mix) < 8:
            remaining = content_items.exclude(
                id__in=[item.id for item in content_mix]
            ).order_by("?")[: 8 - len(content_mix)]
            content_mix.extend(remaining)

        # Convert to API format
        content_data = []
        for item in content_mix:
            content_data.append(
                {
                    "id": item.id,
                    "type": item.type,
                    "question": item.question_text,
                    "answer": item.answer_text,
                    "choices": item.choices_list,
                    "hint": item.hint_text,
                    "explanation": item.explanation_text,
                    "difficulty": item.difficulty,
                    "tags": item.tags,
                }
            )

        return content_data


class VocabularyViewSet(viewsets.ModelViewSet):
    queryset = Vocabulary.objects.all()
    serializer_class = VocabularySerializer
    permission_classes = [AllowAny]


class UserLessonProgressViewSet(viewsets.ModelViewSet):
    queryset = UserLessonProgress.objects.all()
    serializer_class = UserLessonProgressSerializer
    permission_classes = [AllowAny]


@api_view(["GET"])
@permission_classes([AllowAny])
def get_lesson_content_for_care(request, lesson_id):
    """
    API endpoint specifically for C.A.R.E. system
    Returns structured content for each C.A.R.E. phase
    """
    try:
        lesson = Lesson.objects.get(id=lesson_id, is_active=True)
    except Lesson.DoesNotExist:
        return Response({"error": "Lesson not found"}, status=404)

    # Get vocabulary for this lesson
    vocabularies = lesson.vocabularies.all()
    vocab_words = [v.word.lower() for v in vocabularies]

    # Get content items for this lesson - try tags first, then fallback to text matching
    content_items = ContentItem.objects.filter(
        tags__overlap=vocab_words, is_active=True
    ).order_by("difficulty")

    # If no items found by tags, try matching by question text containing vocabulary
    if not content_items.exists():
        from django.db.models import Q

        query = Q()
        for word in vocab_words:
            query |= Q(question_text__icontains=word) | Q(answer_text__icontains=word)
        content_items = ContentItem.objects.filter(query, is_active=True).order_by(
            "difficulty"
        )

    # Organize content by C.A.R.E. phases
    care_content = {
        "contextualize": {
            "title": "Contextualize - Set the Scene",
            "description": f"Learn about {lesson.title} in real-world contexts",
            "content": [],
        },
        "acquire": {
            "title": "Acquire - Learn New Concepts",
            "description": f"Master the vocabulary and concepts in {lesson.title}",
            "content": [],
        },
        "reinforce": {
            "title": "Reinforce - Practice and Solidify",
            "description": f"Practice and strengthen your knowledge of {lesson.title}",
            "content": [],
        },
        "extend": {
            "title": "Extend - Apply in New Contexts",
            "description": f"Apply {lesson.title} concepts in creative ways",
            "content": [],
        },
    }

    # Distribute content across C.A.R.E. phases
    content_list = list(content_items)

    # Ensure we have enough content for a meaningful 15+ minute session
    # Target: 6-10 items per phase = 24-40 total items
    # Use CEFR-based progression for pedagogically sound learning

    # Get lesson's CEFR level for appropriate content selection
    lesson_cefr = getattr(lesson, "cefr_level", "A1")

    # CEFR-based content selection with pedagogical progression
    # Contextualize: Start with current or slightly easier CEFR level
    contextualize_content = [
        item
        for item in content_list
        if getattr(item, "cefr_level", "A1") <= lesson_cefr and item.difficulty <= 2
    ][:8]

    # Acquire: Core learning at lesson's CEFR level
    acquire_content = [
        item
        for item in content_list
        if (
            getattr(item, "cefr_level", "A1") == lesson_cefr
            and item.type in ["flashcard", "mcq", "translation"]
        )
    ][:10]

    # Reinforce: Practice at current CEFR level with interactive content
    reinforce_content = [
        item
        for item in content_list
        if (
            getattr(item, "cefr_level", "A1") == lesson_cefr
            and item.type in ["mcq", "translation", "flashcard"]
        )
    ][:10]

    # Extend: Challenge with next CEFR level or higher difficulty
    cefr_order = ["A1", "A2", "B1", "B2", "C1", "C2"]
    next_cefr = cefr_order[min(cefr_order.index(lesson_cefr) + 1, len(cefr_order) - 1)]
    extend_content = [
        item
        for item in content_list
        if (getattr(item, "cefr_level", "A1") >= lesson_cefr and item.difficulty >= 3)
    ][:6]

    # Fallback: If we don't have enough content, pad with available items
    min_items_per_phase = 5
    if len(contextualize_content) < min_items_per_phase:
        contextualize_content.extend(content_list[:min_items_per_phase])
    if len(acquire_content) < min_items_per_phase:
        acquire_content.extend(content_list[:min_items_per_phase])
    if len(reinforce_content) < min_items_per_phase:
        reinforce_content.extend(content_list[:min_items_per_phase])
    if len(extend_content) < 3:
        extend_content.extend(content_list[:3])

    # Format content for each phase
    for phase, items in [
        ("contextualize", contextualize_content),
        ("acquire", acquire_content),
        ("reinforce", reinforce_content),
        ("extend", extend_content),
    ]:
        for item in items:
            care_content[phase]["content"].append(
                {
                    "id": item.id,
                    "type": item.type,
                    "question": item.question_text,
                    "answer": item.answer_text,
                    "choices": item.choices_list,
                    "hint": item.hint_text,
                    "explanation": item.explanation_text,
                    "difficulty": item.difficulty,
                }
            )

    return Response(
        {
            "lesson": {
                "id": lesson.id,
                "title": lesson.title,
                "description": lesson.description,
                "difficulty_level": lesson.difficulty_level,
                "care_phase": lesson.care_phase,
            },
            "care_phases": care_content,
            "vocabulary": [
                {
                    "word": v.word,
                    "translation": v.translation,
                    "example": v.example_sentence,
                }
                for v in vocabularies
            ],
        }
    )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def complete_lesson(request):
    """Handle lesson completion with XP awarding"""
    try:
        data = request.data
        lesson_id = data.get("lesson_id")
        total_questions = data.get("total_questions", 0)
        correct_answers = data.get("correct_answers", 0)
        max_streak = data.get("max_streak", 0)
        xp_earned = data.get("xp_earned", 0)
        completion_time = data.get("completion_time", 0)

        if not lesson_id:
            return Response({"error": "lesson_id is required"}, status=400)

        # Get the lesson
        try:
            lesson = Lesson.objects.get(id=lesson_id)
        except Lesson.DoesNotExist:
            return Response({"error": "Lesson not found"}, status=404)

        # Get or create user progress
        progress, created = UserLessonProgress.objects.get_or_create(
            user=request.user,
            lesson=lesson,
            defaults={
                "completed": True,
                "xp_earned": xp_earned,
                "completion_time": completion_time,
            },
        )

        if not created:
            # Update existing progress
            progress.completed = True
            progress.xp_earned = max(progress.xp_earned, xp_earned)  # Keep highest XP
            progress.completion_time = completion_time
            progress.save()

        # Award XP using gamification service
        from gamification.services import GamificationService

        xp_result = GamificationService.award_xp(
            user=request.user,
            amount=xp_earned,
            source="lesson_completion",
            source_id=lesson.id,
            description=f"Completed lesson: {lesson.title}",
        )

        # Update user learning profile
        profile, _ = UserLearningProfile.objects.get_or_create(user=request.user)
        profile.total_xp = xp_result.get("total_xp", profile.total_xp)
        profile.current_level = xp_result.get("level", profile.current_level)
        profile.save()

        # Check for CEFR progression
        from lessons.services.cefr_progression import CEFRProgressionService

        cefr_evaluation = CEFRProgressionService.evaluate_user_progression(request.user)
        cefr_advancement = None

        if cefr_evaluation.get("ready_for_advancement", False):
            cefr_advancement = CEFRProgressionService.advance_user_cefr_level(
                request.user
            )
            # Refresh profile to get updated CEFR level
            profile.refresh_from_db()

        response_data = {
            "success": True,
            "lesson_completed": True,
            "xp_awarded": xp_earned,
            "total_xp": xp_result.get("total_xp", 0),
            "level": xp_result.get("level", 1),
            "level_up": xp_result.get("level_up", False),
            "accuracy": round((correct_answers / max(total_questions, 1)) * 100, 1),
            "next_lesson_id": lesson.id + 1,
            "current_cefr_level": profile.current_cefr_level,
            "cefr_evaluation": cefr_evaluation,
        }

        # Add CEFR advancement info if applicable
        if cefr_advancement and cefr_advancement.get("success", False):
            response_data.update(
                {
                    "cefr_advancement": cefr_advancement,
                    "cefr_level_up": True,
                    "new_cefr_level": cefr_advancement.get("new_level"),
                    "cefr_xp_bonus": cefr_advancement.get("xp_bonus", 0),
                }
            )

        return Response(response_data)

    except Exception as e:
        return Response({"error": str(e)}, status=500)


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_user_progress(request):
    """Get user's current progress and stats"""
    try:
        user = request.user

        # Get user learning profile
        profile, _ = UserLearningProfile.objects.get_or_create(user=user)

        # Get gamification level
        from gamification.models import Level

        level, _ = Level.objects.get_or_create(user=user)

        # Get recent lesson progress
        recent_lessons = UserLessonProgress.objects.filter(user=user).order_by(
            "-updated_at"
        )[:5]

        # Get CEFR progression info
        from lessons.services.cefr_progression import CEFRProgressionService

        cefr_evaluation = CEFRProgressionService.evaluate_user_progression(user)
        cefr_info = CEFRProgressionService.get_cefr_level_info(
            profile.current_cefr_level
        )

        return Response(
            {
                "user_id": user.id,
                "username": user.username,
                "level": level.level,
                "total_xp": level.xp,
                "current_level": profile.current_level,
                "skill_level": profile.skill_level,
                "target_language": profile.target_language,
                "current_cefr_level": profile.current_cefr_level,
                "target_cefr_level": profile.target_cefr_level,
                "cefr_info": cefr_info,
                "cefr_evaluation": cefr_evaluation,
                "recent_lessons": [
                    {
                        "lesson_id": progress.lesson.id,
                        "lesson_title": progress.lesson.title,
                        "completed": progress.completed,
                        "xp_earned": progress.xp_earned,
                        "updated_at": progress.updated_at,
                    }
                    for progress in recent_lessons
                ],
            }
        )

    except Exception as e:
        return Response({"error": str(e)}, status=500)
