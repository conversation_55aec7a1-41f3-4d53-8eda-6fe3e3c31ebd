<!-- Modal Overlay -->
<div id="auth-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm hidden">
  <div class="bg-white rounded-xl shadow-xl w-full max-w-md mx-auto relative animate-fade-in">
    <button class="absolute top-4 right-4 text-gray-400 hover:text-gray-600" onclick="document.getElementById('auth-modal').classList.add('hidden')">&times;</button>
    <div class="flex flex-col items-center pt-8">
      <img src="/static/logo.svg" alt="TalonTalk Logo" class="h-8 mb-2">
      <span class="font-semibold text-lg text-indigo-700 mb-4">TalonTalk</span>
    </div>
    <div class="flex border-b border-gray-200 mb-4">
      <button id="tab-signup" class="flex-1 py-2 text-center font-medium text-gray-500 border-b-2 border-transparent focus:outline-none" onclick="showTab('signup')">Sign Up</button>
      <button id="tab-login" class="flex-1 py-2 text-center font-medium text-indigo-600 border-b-2 border-indigo-600 focus:outline-none" onclick="showTab('login')">Log In</button>
    </div>
    <div id="tab-content-signup" class="hidden">
      {% include 'account/_social_buttons.html' %}
      {% include 'account/_signup_form.html' %}
    </div>
    <div id="tab-content-login">
      {% include 'account/_social_buttons.html' %}
      {% include 'account/_login_form.html' %}
    </div>
    <div class="h-4"></div>
  </div>
</div>
<script>
function showTab(tab) {
  document.getElementById('tab-content-signup').classList.toggle('hidden', tab !== 'signup');
  document.getElementById('tab-content-login').classList.toggle('hidden', tab !== 'login');
  document.getElementById('tab-signup').classList.toggle('text-indigo-600', tab === 'signup');
  document.getElementById('tab-signup').classList.toggle('text-gray-500', tab !== 'signup');
  document.getElementById('tab-signup').classList.toggle('border-indigo-600', tab === 'signup');
  document.getElementById('tab-signup').classList.toggle('border-transparent', tab !== 'signup');
  document.getElementById('tab-login').classList.toggle('text-indigo-600', tab === 'login');
  document.getElementById('tab-login').classList.toggle('text-gray-500', tab !== 'login');
  document.getElementById('tab-login').classList.toggle('border-indigo-600', tab === 'login');
  document.getElementById('tab-login').classList.toggle('border-transparent', tab !== 'login');
}
// Always default to login tab when modal opens
showTab('login');
</script>
