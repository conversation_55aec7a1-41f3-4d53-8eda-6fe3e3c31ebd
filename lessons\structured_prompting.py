"""
Structured Prompting Framework for TalonTalk
Implements the FETCH → FORMAT → EXECUTE pattern for high-quality AI content generation
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


@dataclass
class ContentContext:
    """Structured context for AI prompting"""

    session_type: str
    user_id: int
    content_items: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class StructuredPromptBuilder:
    """
    Core class for building structured, data-driven prompts
    Implements the FETCH → FORMAT → EXECUTE framework
    """

    def __init__(self):
        self.session_types = {
            "focused_practice": self._build_focused_practice_context,
            "adaptive_review": self._build_adaptive_review_context,
            "care_phase": self._build_care_phase_context,
            "conversation_practice": self._build_conversation_context,
            "weak_spot_reinforcement": self._build_weak_spot_context,
        }

    def build_context(self, session_type: str, user, **kwargs) -> ContentContext:
        """
        FETCH: Get structured data from database
        FORMAT: Create rich context for AI
        """
        if session_type not in self.session_types:
            raise ValueError(f"Unknown session type: {session_type}")

        builder_func = self.session_types[session_type]
        return builder_func(user, **kwargs)

    def _build_focused_practice_context(
        self, user, lesson_id: int = None, **kwargs
    ) -> ContentContext:
        """Build context for focused lesson practice"""
        from lessons.models import Lesson, ContentItem

        # FETCH: Get lesson and its content items
        if lesson_id:
            lesson = Lesson.objects.get(id=lesson_id)
        else:
            # Create a default lesson context when no specific lesson is provided
            lesson = type(
                "DefaultLesson",
                (),
                {
                    "title": kwargs.get("topic", "General Practice").title(),
                    "description": f"Practice session for {kwargs.get('topic', 'general topics')}",
                    "difficulty_level": kwargs.get("difficulty_level", "1"),
                },
            )()

        # Get content items (since there's no direct lesson relationship,
        # we'll use content items with matching difficulty and language)
        content_items = ContentItem.objects.filter(
            difficulty=lesson.difficulty_level,
            language="spanish",  # TODO: Make this dynamic
            is_active=True,
        ).values("id", "type", "question_text", "answer_text", "difficulty", "tags")[
            :10
        ]  # Limit to 10 items for focused practice

        # FORMAT: Structure the data
        formatted_items = []
        for item in content_items:
            formatted_items.append(
                {
                    "item_id": item["id"],
                    "type": item["type"],
                    "text_es": item["answer_text"],  # Spanish answer
                    "text_en": item["question_text"],  # English question
                    "difficulty": item["difficulty"],
                    "tags": item["tags"] or [],
                }
            )

        metadata = {
            "lesson_title": lesson.title,
            "lesson_description": lesson.description,
            "target_language": "spanish",
            "user_level": getattr(user.profile, "skill_level", "beginner"),
            "session_length": kwargs.get("session_length", 5),
        }

        return ContentContext(
            session_type="focused_practice",
            user_id=user.id,
            content_items=formatted_items,
            metadata=metadata,
        )

    def _build_adaptive_review_context(self, user, **kwargs) -> ContentContext:
        """Build context for personalized adaptive review"""
        from lessons.models import UserContentPerformance, ContentItem

        # FETCH: Get user's weakest performing items
        weak_performance = (
            UserContentPerformance.objects.filter(
                user=user, proficiency_score__lt=0.7  # Items user struggles with
            )
            .select_related("content_item")
            .order_by("proficiency_score")[:5]
        )

        # If no weak items, get items that need review (haven't seen recently)
        if not weak_performance.exists():
            from datetime import datetime, timedelta

            last_week = datetime.now() - timedelta(days=7)
            weak_performance = (
                UserContentPerformance.objects.filter(
                    user=user, last_seen__lt=last_week
                )
                .select_related("content_item")
                .order_by("last_seen")[:5]
            )

        # If still no performance data, get random content items
        if not weak_performance.exists():
            content_items = ContentItem.objects.filter(
                language="spanish", is_active=True
            ).order_by("?")[
                :5
            ]  # Random selection

            formatted_items = []
            for item in content_items:
                formatted_items.append(
                    {
                        "item_id": item.id,
                        "text_es": item.answer_text,
                        "text_en": item.question_text,
                        "proficiency": 0.0,  # No data
                        "last_seen": "never",
                        "attempts": 0,
                        "correct_rate": 0.0,
                    }
                )
        else:
            # FORMAT: Structure the performance data
            formatted_items = []
            for perf in weak_performance:
                item = perf.content_item
                formatted_items.append(
                    {
                        "item_id": item.id,
                        "text_es": item.answer_text,
                        "text_en": item.question_text,
                        "proficiency": round(perf.proficiency_score, 2),
                        "last_seen": perf.last_seen.strftime("%Y-%m-%d"),
                        "attempts": perf.times_seen,
                        "correct_rate": round(
                            perf.times_correct / max(perf.times_seen, 1), 2
                        ),
                    }
                )

        metadata = {
            "user_level": getattr(user.profile, "skill_level", "beginner"),
            "target_language": getattr(user.profile, "target_language", "spanish"),
            "focus_area": "weak_spots",
            "session_length": kwargs.get("session_length", 3),
        }

        return ContentContext(
            session_type="adaptive_review",
            user_id=user.id,
            content_items=formatted_items,
            metadata=metadata,
        )

    def _build_care_phase_context(
        self, user, phase: str, lesson_id: int = None, **kwargs
    ) -> ContentContext:
        """Build context for C.A.R.E. framework phases"""
        from lessons.models import Lesson, ContentItem

        # FETCH: Get lesson content for the specific C.A.R.E. phase
        lesson = Lesson.objects.get(id=lesson_id)
        content_items = ContentItem.objects.filter(
            difficulty=lesson.difficulty_level, language="spanish", is_active=True
        )

        # Different content focus for each phase
        if phase == "contextualize":
            # Focus on conversation and phrases
            focus_items = content_items.filter(type__in=["conversation", "flashcard"])
        elif phase == "acquire":
            # Focus on vocabulary
            focus_items = content_items.filter(type="vocabulary")
        elif phase == "reinforce":
            # All lesson content for practice
            focus_items = content_items
        elif phase == "extend":
            # Focus on advanced usage
            focus_items = content_items.filter(type__in=["conversation", "mcq"])
        else:
            focus_items = content_items

        # FORMAT: Structure for C.A.R.E. phase
        formatted_items = []
        for item in focus_items[:10]:  # Limit to 10 items
            formatted_items.append(
                {
                    "item_id": item.id,
                    "type": item.type,
                    "text_es": item.answer_text,
                    "text_en": item.question_text,
                    "context": "",  # Could add cultural context from tags
                    "example_usage": item.explanation_text or "",
                }
            )

        metadata = {
            "care_phase": phase,
            "lesson_title": lesson.title,
            "phase_objective": self._get_care_phase_objective(phase),
            "user_level": getattr(user.profile, "skill_level", "beginner"),
        }

        return ContentContext(
            session_type="care_phase",
            user_id=user.id,
            content_items=formatted_items,
            metadata=metadata,
        )

    def _build_conversation_context(self, user, **kwargs) -> ContentContext:
        """Build context for AI conversation practice"""
        from lessons.models import UserContentPerformance

        # FETCH: Get user's mastered vocabulary for conversation
        mastered_items = (
            UserContentPerformance.objects.filter(
                user=user, proficiency_score__gte=0.8  # Well-known items
            )
            .select_related("content_item")
            .order_by("-proficiency_score")[:10]
        )

        # If no mastered items, get some basic content
        if not mastered_items.exists():
            from lessons.models import ContentItem

            basic_items = ContentItem.objects.filter(
                difficulty=1, language="spanish", is_active=True  # Beginner level
            )[:10]

            formatted_items = []
            for item in basic_items:
                formatted_items.append(
                    {
                        "text_es": item.answer_text,
                        "text_en": item.question_text,
                        "confidence": 0.5,  # Assumed basic confidence
                        "type": item.type,
                    }
                )
        else:
            # FORMAT: Create conversation scenario
            formatted_items = []
            for perf in mastered_items:
                item = perf.content_item
                formatted_items.append(
                    {
                        "text_es": item.answer_text,
                        "text_en": item.question_text,
                        "confidence": round(perf.proficiency_score, 2),
                        "type": item.type,
                    }
                )

        # Get scenario from kwargs or generate appropriate one
        scenario = kwargs.get("scenario", self._get_conversation_scenario(user))

        metadata = {
            "scenario": scenario,
            "user_level": getattr(user.profile, "skill_level", "beginner"),
            "conversation_type": kwargs.get("conversation_type", "cafe_ordering"),
            "ai_personality": "friendly_native_speaker",
        }

        return ContentContext(
            session_type="conversation_practice",
            user_id=user.id,
            content_items=formatted_items,
            metadata=metadata,
        )

    def _build_weak_spot_context(self, user, **kwargs) -> ContentContext:
        """Build context for targeted weak spot reinforcement"""
        from lessons.models import UserContentPerformance

        # FETCH: Get the absolute weakest items for intensive practice
        weakest_items = (
            UserContentPerformance.objects.filter(user=user, proficiency_score__lt=0.5)
            .select_related("content_item")
            .order_by("proficiency_score")[:3]
        )

        formatted_items = []
        for perf in weakest_items:
            item = perf.content_item
            formatted_items.append(
                {
                    "item_id": item.id,
                    "text_es": item.spanish_text,
                    "text_en": item.english_text,
                    "proficiency": perf.proficiency_score,
                    "error_patterns": self._analyze_error_patterns(perf),
                    "suggested_focus": self._suggest_focus_area(item, perf),
                }
            )

        metadata = {
            "focus_type": "intensive_remediation",
            "user_level": getattr(user.profile, "skill_level", "beginner"),
            "session_length": 3,  # Short, focused sessions for weak spots
        }

        return ContentContext(
            session_type="weak_spot_reinforcement",
            user_id=user.id,
            content_items=formatted_items,
            metadata=metadata,
        )

    def _format_content_items_for_prompt(
        self, content_items: List[Dict[str, Any]]
    ) -> str:
        """Format content items for inclusion in prompts"""
        if not content_items:
            return (
                "No specific content items provided - create general practice content."
            )

        formatted_items = []
        for item in content_items:
            formatted_items.append(
                f"- {item.get('text_es', 'N/A')} ({item.get('text_en', 'N/A')})"
            )

        return "\n".join(formatted_items)

    def _get_care_phase_objective(self, phase: str) -> str:
        """Get learning objective for C.A.R.E. phase"""
        objectives = {
            "contextualize": "Understand cultural context and real-world usage",
            "acquire": "Learn new vocabulary and basic concepts",
            "reinforce": "Practice and solidify understanding through exercises",
            "extend": "Apply knowledge in complex, real-world scenarios",
        }
        return objectives.get(phase, "General language learning")

    def _get_conversation_scenario(self, user) -> str:
        """Generate appropriate conversation scenario based on user level"""
        level = getattr(user.profile, "skill_level", "beginner")

        scenarios = {
            "beginner": "You are a tourist ordering food at a Spanish café",
            "intermediate": "You are asking for directions while traveling in Madrid",
            "advanced": "You are discussing weekend plans with a Spanish friend",
        }

        return scenarios.get(level, scenarios["beginner"])

    def _analyze_error_patterns(self, performance) -> List[str]:
        """Analyze what types of errors user makes with this content"""
        # This would analyze historical wrong answers
        # For now, return common patterns based on proficiency
        if performance.proficiency_score < 0.3:
            return ["difficulty_with_recognition", "confusion_with_similar_words"]
        elif performance.proficiency_score < 0.6:
            return ["occasional_confusion", "needs_more_practice"]
        else:
            return ["minor_hesitation"]

    def _suggest_focus_area(self, content_item, performance) -> str:
        """Suggest what aspect to focus on for improvement"""
        if content_item.content_type == "vocabulary":
            if performance.proficiency_score < 0.4:
                return "recognition_and_meaning"
            else:
                return "usage_in_context"
        elif content_item.content_type == "phrase":
            return "pronunciation_and_fluency"
        else:
            return "general_comprehension"


class StructuredPromptExecutor:
    """
    EXECUTE: Send structured context to LLM with specific prompts
    """

    def __init__(self, llm_service):
        self.llm_service = llm_service

    def _format_content_items_for_prompt(
        self, content_items: List[Dict[str, Any]]
    ) -> str:
        """Format content items for inclusion in prompts"""
        if not content_items:
            return "No specific content items provided. Generate appropriate beginner-level content."

        formatted = []
        for item in content_items:
            if "text_es" in item and "text_en" in item:
                formatted.append(f"- {item['text_en']} → {item['text_es']}")
            elif "question" in item and "answer" in item:
                formatted.append(f"- {item['question']} → {item['answer']}")

        return (
            "\n".join(formatted)
            if formatted
            else "Generate appropriate content for the lesson."
        )

    def execute_focused_practice(self, context: ContentContext) -> Dict[str, Any]:
        """Generate focused practice session with advanced prompt engineering"""

        # Advanced persona-driven prompt with specific constraints
        prompt = f"""
You are María González, a certified Spanish language instructor with 15 years of experience teaching A1-C2 level students. You specialize in creating pedagogically sound, engaging flashcards that follow proven language acquisition principles.

STUDENT PROFILE:
- Level: {context.metadata.get('user_level', 'beginner')}
- Target Language: {context.metadata.get('target_language', 'spanish')}
- Lesson Focus: {context.metadata.get('lesson_title', 'General Practice')}

CONTENT TO PRACTICE:
{self._format_content_items_for_prompt(context.content_items)}

STRICT REQUIREMENTS:
1. Create EXACTLY {context.metadata.get('session_length', 5)} flashcards
2. Each flashcard MUST use proper Spanish capitalization (only first word and proper nouns)
3. Questions must be in English, answers in Spanish
4. NO circular logic (don't ask "What Spanish word means 'food'?" if answer is also "food")
5. Use format: "How do you say '[English phrase]' in Spanish?"
6. Include pronunciation guide in IPA format
7. Provide contextual example sentence
8. Multiple choice options must be plausible but clearly distinct

PEDAGOGICAL GOALS:
- Reinforce lesson vocabulary through spaced repetition
- Build confidence through achievable challenges
- Connect new words to real-world contexts
- Maintain engagement through variety

OUTPUT FORMAT (JSON only, no explanations):
{{
    "flashcards": [
        {{
            "question": "How do you say 'good morning' in Spanish?",
            "correct_answer": "buenos días",
            "options": ["buenos días", "buenas tardes", "buenas noches", "hasta luego"],
            "pronunciation": "/ˈbwe.nos ˈdi.as/",
            "explanation": "Used as a greeting from sunrise until noon",
            "example_sentence": "Buenos días, ¿cómo está usted?",
            "difficulty_level": "{context.metadata.get('user_level', 'beginner')}",
            "tags": ["greetings", "daily_expressions"]
        }}
    ]
}}

Generate the flashcards now:
"""

        # Send to LLM and parse response
        try:
            response = self.llm_service._make_llm_request(
                [{"role": "user", "content": prompt}], temperature=0.3, max_tokens=1500
            )
            return self._parse_and_validate_response(response, "focused_practice")
        except Exception as e:
            logger.error(f"Error in focused practice generation: {e}")
            return {
                "error": str(e),
                "session_type": "focused_practice",
                "fallback": True,
            }

    def execute_adaptive_review(self, context: ContentContext) -> Dict[str, Any]:
        """Generate personalized adaptive review"""

        prompt = f"""
You are an adaptive learning AI creating a personalized review session.

USER PERFORMANCE DATA:
{json.dumps(context.content_items, indent=2)}

TASK: Create {context.metadata['session_length']} targeted questions to improve proficiency on these weak items. For each item:
- Start with the lowest proficiency item
- Create questions that address the specific weakness
- Use varied question types (multiple choice, fill-in-blank, translation)
- Provide encouraging explanations

Return a JSON object with personalized questions targeting the user's specific weak spots.
"""

        response = self.llm_service.generate_content(prompt)
        return self._parse_and_validate_response(response, "adaptive_review")

    def execute_care_phase(self, context: ContentContext) -> Dict[str, Any]:
        """Generate C.A.R.E. phase content"""

        phase = context.metadata["care_phase"]
        objective = context.metadata["phase_objective"]

        prompt = f"""
You are implementing the {phase.upper()} phase of the C.A.R.E. learning framework.

PHASE OBJECTIVE: {objective}

CONTENT FOR THIS PHASE:
{json.dumps(context.content_items, indent=2)}

TASK: Create engaging {phase} content that:
- Aligns with the phase objective
- Uses the provided content items appropriately
- Matches the user's {context.metadata['user_level']} level
- Provides interactive elements

Return appropriate content for the {phase} phase.
"""

        response = self.llm_service.generate_content(prompt)
        return self._parse_and_validate_response(response, f"care_{phase}")

    def execute_conversation_practice(self, context: ContentContext) -> Dict[str, Any]:
        """Initialize AI conversation with user's mastered vocabulary"""

        scenario = context.metadata["scenario"]
        mastered_words = [item["text_es"] for item in context.content_items]

        prompt = f"""
You are starting a conversation practice session as a native Spanish speaker.

SCENARIO: {scenario}

USER'S MASTERED VOCABULARY (subtly encourage use of these words):
{mastered_words}

TASK: 
1. Start the conversation naturally based on the scenario
2. Be encouraging and patient
3. Gently guide conversation to use mastered vocabulary
4. Respond in Spanish with English translations when needed
5. Keep responses conversational and realistic

Begin the conversation now.
"""

        response = self.llm_service.generate_content(prompt)
        return {
            "conversation_start": response,
            "scenario": scenario,
            "target_vocabulary": mastered_words,
            "ai_personality": context.metadata["ai_personality"],
        }

    def _parse_and_validate_response(
        self, response: str, session_type: str
    ) -> Dict[str, Any]:
        """Parse and validate LLM response"""

        # Handle DeepSeek R1 reasoning format
        if response.strip().startswith("<think>"):
            # Find the JSON after the </think> tag
            if "</think>" in response:
                json_part = response.split("</think>", 1)[1].strip()
            else:
                # Fallback: look for JSON-like content
                json_part = response
        else:
            json_part = response.strip()

        try:
            # Try to parse as JSON
            if json_part.startswith("{"):
                parsed = json.loads(json_part)
                return parsed
            else:
                # Try to find JSON within the text
                import re

                json_match = re.search(r"\{.*\}", json_part, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    parsed = json.loads(json_str)
                    return parsed
                else:
                    # Wrap non-JSON response
                    return {
                        "session_type": session_type,
                        "content": response,
                        "format": "text",
                    }
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse LLM response as JSON: {e}")
            return {
                "session_type": session_type,
                "content": response,
                "format": "text",
                "error": "json_parse_failed",
            }


# Main framework class that ties everything together
class StructuredLearningFramework:
    """
    Main class implementing the FETCH → FORMAT → EXECUTE framework
    """

    def __init__(self, llm_service):
        self.prompt_builder = StructuredPromptBuilder()
        self.prompt_executor = StructuredPromptExecutor(llm_service)

    def generate_learning_session(
        self, session_type: str, user, **kwargs
    ) -> Dict[str, Any]:
        """
        Complete FETCH → FORMAT → EXECUTE pipeline
        """
        try:
            # FETCH → FORMAT: Build structured context from database
            context = self.prompt_builder.build_context(session_type, user, **kwargs)

            # EXECUTE: Send to LLM with structured prompt
            if session_type == "focused_practice":
                return self.prompt_executor.execute_focused_practice(context)
            elif session_type == "adaptive_review":
                return self.prompt_executor.execute_adaptive_review(context)
            elif session_type == "care_phase":
                return self.prompt_executor.execute_care_phase(context)
            elif session_type == "conversation_practice":
                return self.prompt_executor.execute_conversation_practice(context)
            else:
                raise ValueError(f"Unsupported session type: {session_type}")

        except Exception as e:
            logger.error(f"Error in structured learning framework: {e}")
            return {"error": str(e), "session_type": session_type, "fallback": True}
