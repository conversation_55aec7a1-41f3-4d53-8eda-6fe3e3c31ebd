{% load static %}

<!-- Enhanced Flashcard Component for R1 Generated Content -->
<div class="flashcard-container max-w-2xl mx-auto">
    <!-- Flashcard -->
    <div id="flashcard" class="flashcard-wrapper relative">
        <!-- Front of Card -->
        <div id="flashcard-front" class="flashcard-face flashcard-front">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 min-h-[400px] flex flex-col">
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div class="flex items-center space-x-3">
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                            {{ flashcard.get_type_display }}
                        </span>
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                            {{ flashcard.cefr_level }}
                        </span>
                        {% if flashcard.template_type %}
                        <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                            {{ flashcard.template_type|title }}
                        </span>
                        {% endif %}
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="hint-btn" class="p-2 text-gray-500 hover:text-blue-600 transition-colors" title="Get Hint">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </button>
                        <button id="audio-btn" class="p-2 text-gray-500 hover:text-green-600 transition-colors" title="Listen">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M9 9a3 3 0 000 6h3v-3H9V9z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Question Content -->
                <div class="flex-1 flex flex-col justify-center">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4 leading-relaxed">
                            {{ flashcard.question_text }}
                        </h2>
                        
                        <!-- Cultural Context (if available) -->
                        {% if flashcard.cultural_context %}
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4 text-left">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-blue-800">Cultural Context</p>
                                    <p class="text-sm text-blue-700">{{ flashcard.cultural_context }}</p>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Multiple Choice Options (if applicable) -->
                    {% if flashcard.choices_json %}
                    <div class="space-y-3">
                        {% for choice in flashcard.choices_json %}
                        <button class="choice-btn w-full p-4 text-left border border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                data-choice="{{ choice }}">
                            <span class="font-medium">{{ forloop.counter|add:"64"|chr }}.</span>
                            <span class="ml-3">{{ choice }}</span>
                        </button>
                        {% endfor %}
                    </div>
                    {% else %}
                    <!-- Free Response -->
                    <div class="space-y-4">
                        <textarea id="user-answer" 
                                  class="w-full p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                  rows="3" 
                                  placeholder="Type your answer here..."></textarea>
                        <button id="check-answer-btn" 
                                class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                            Check Answer
                        </button>
                    </div>
                    {% endif %}
                </div>

                <!-- Hint Display -->
                <div id="hint-display" class="hidden mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-yellow-800">Hint</p>
                            <p id="hint-text" class="text-sm text-yellow-700"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back of Card -->
        <div id="flashcard-back" class="flashcard-face flashcard-back hidden">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 min-h-[400px]">
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-green-700">Answer</h3>
                    <div class="flex items-center space-x-2">
                        <span id="result-indicator" class="px-3 py-1 rounded-full text-sm font-medium"></span>
                    </div>
                </div>

                <!-- Answer Content -->
                <div class="space-y-6">
                    <!-- Correct Answer -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 class="font-semibold text-green-800 mb-2">Correct Answer:</h4>
                        <p class="text-lg font-medium text-green-900">{{ flashcard.answer_text }}</p>
                        
                        <!-- Pronunciation (if available) -->
                        {% if flashcard.pronunciation_guide %}
                        <p class="text-sm text-green-700 mt-2">
                            <span class="font-medium">Pronunciation:</span> {{ flashcard.pronunciation_guide }}
                        </p>
                        {% endif %}
                    </div>

                    <!-- Detailed Explanation -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-800 mb-2">Explanation:</h4>
                        <div class="text-blue-900 space-y-2">
                            {{ flashcard.explanation_text|linebreaks }}
                        </div>
                    </div>

                    <!-- Memory Technique (if available) -->
                    {% if flashcard.memory_technique %}
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <h4 class="font-semibold text-purple-800 mb-2">Memory Technique:</h4>
                        <p class="text-purple-900">{{ flashcard.memory_technique }}</p>
                    </div>
                    {% endif %}

                    <!-- Usage Examples (if available) -->
                    {% if flashcard.usage_examples %}
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">Usage Examples:</h4>
                        <div class="text-gray-900 space-y-1">
                            {{ flashcard.usage_examples|linebreaks }}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
                    <button id="flip-back-btn" 
                            class="px-6 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                        Review Question
                    </button>
                    
                    <div class="flex space-x-3">
                        <button id="difficulty-btn" 
                                class="px-6 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                                data-difficulty="hard">
                            Hard
                        </button>
                        <button id="good-btn" 
                                class="px-6 py-2 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors"
                                data-difficulty="good">
                            Good
                        </button>
                        <button id="easy-btn" 
                                class="px-6 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                                data-difficulty="easy">
                            Easy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Indicator -->
    <div class="mt-6 bg-gray-200 rounded-full h-2">
        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
    </div>
    <p class="text-center text-sm text-gray-600 mt-2">
        <span id="current-card">1</span> of <span id="total-cards">{{ total_cards|default:"1" }}</span>
    </p>
</div>

<style>
.flashcard-wrapper {
    perspective: 1000px;
    min-height: 400px;
}

.flashcard-face {
    backface-visibility: hidden;
    transition: transform 0.6s ease-in-out;
}

.flashcard-front {
    transform: rotateY(0deg);
}

.flashcard-back {
    transform: rotateY(180deg);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}

.flashcard-wrapper.flipped .flashcard-front {
    transform: rotateY(-180deg);
}

.flashcard-wrapper.flipped .flashcard-back {
    transform: rotateY(0deg);
}

.choice-btn.selected {
    border-color: #3B82F6;
    background-color: #EBF8FF;
}

.choice-btn.correct {
    border-color: #10B981;
    background-color: #D1FAE5;
}

.choice-btn.incorrect {
    border-color: #EF4444;
    background-color: #FEE2E2;
}
</style>

<script>
class EnhancedFlashcard {
    constructor(flashcardData) {
        this.flashcard = flashcardData;
        this.currentHintLevel = 0;
        this.userAnswer = null;
        this.isAnswered = false;
        this.startTime = Date.now();

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAudio();
    }

    setupEventListeners() {
        // Hint button
        document.getElementById('hint-btn')?.addEventListener('click', () => {
            this.showHint();
        });

        // Audio button
        document.getElementById('audio-btn')?.addEventListener('click', () => {
            this.playAudio();
        });

        // Multiple choice buttons
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectChoice(e.target.closest('.choice-btn'));
            });
        });

        // Free response check button
        document.getElementById('check-answer-btn')?.addEventListener('click', () => {
            this.checkFreeResponse();
        });

        // Flip back button
        document.getElementById('flip-back-btn')?.addEventListener('click', () => {
            this.flipToFront();
        });

        // Difficulty buttons
        document.querySelectorAll('[data-difficulty]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.rateDifficulty(e.target.dataset.difficulty);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    setupAudio() {
        // Initialize speech synthesis if available
        if ('speechSynthesis' in window) {
            this.speechSynthesis = window.speechSynthesis;
        }
    }

    showHint() {
        const hints = this.flashcard.progressive_hints || [this.flashcard.hint_text];

        if (this.currentHintLevel < hints.length) {
            const hintDisplay = document.getElementById('hint-display');
            const hintText = document.getElementById('hint-text');

            hintText.textContent = hints[this.currentHintLevel];
            hintDisplay.classList.remove('hidden');

            this.currentHintLevel++;

            // Update hint button
            const hintBtn = document.getElementById('hint-btn');
            if (this.currentHintLevel >= hints.length) {
                hintBtn.style.opacity = '0.5';
                hintBtn.disabled = true;
            }
        }
    }

    playAudio() {
        if (this.speechSynthesis) {
            // Cancel any ongoing speech
            this.speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(this.flashcard.question_text);

            // Set language-specific voice
            const voices = this.speechSynthesis.getVoices();
            const languageMap = {
                'spanish': 'es',
                'french': 'fr',
                'german': 'de',
                'italian': 'it',
                'portuguese': 'pt'
            };

            const langCode = languageMap[this.flashcard.language] || 'en';
            const voice = voices.find(v => v.lang.startsWith(langCode));

            if (voice) {
                utterance.voice = voice;
            }

            utterance.rate = 0.8;
            utterance.pitch = 1.0;

            this.speechSynthesis.speak(utterance);
        }
    }

    selectChoice(choiceBtn) {
        if (this.isAnswered) return;

        // Clear previous selections
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.classList.remove('selected');
        });

        // Select current choice
        choiceBtn.classList.add('selected');
        this.userAnswer = choiceBtn.dataset.choice;

        // Auto-check after selection
        setTimeout(() => {
            this.checkAnswer();
        }, 500);
    }

    checkFreeResponse() {
        const textarea = document.getElementById('user-answer');
        this.userAnswer = textarea.value.trim();

        if (!this.userAnswer) {
            this.showNotification('Please enter an answer', 'warning');
            return;
        }

        this.checkAnswer();
    }

    checkAnswer() {
        if (this.isAnswered) return;

        this.isAnswered = true;
        const isCorrect = this.evaluateAnswer();

        // Show visual feedback
        this.showAnswerFeedback(isCorrect);

        // Flip to back after delay
        setTimeout(() => {
            this.flipToBack();
        }, 1500);

        // Record performance
        this.recordPerformance(isCorrect);
    }

    evaluateAnswer() {
        const correctAnswer = this.flashcard.answer_text.toLowerCase().trim();
        const userAnswer = this.userAnswer.toLowerCase().trim();

        // Exact match
        if (userAnswer === correctAnswer) {
            return true;
        }

        // Fuzzy matching for free response
        if (!this.flashcard.choices_json) {
            // Simple similarity check
            const similarity = this.calculateSimilarity(userAnswer, correctAnswer);
            return similarity > 0.8;
        }

        return false;
    }

    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;

        if (longer.length === 0) return 1.0;

        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    showAnswerFeedback(isCorrect) {
        if (this.flashcard.choices_json) {
            // Highlight correct/incorrect choices
            document.querySelectorAll('.choice-btn').forEach(btn => {
                const choice = btn.dataset.choice;
                if (choice === this.flashcard.answer_text) {
                    btn.classList.add('correct');
                } else if (choice === this.userAnswer && !isCorrect) {
                    btn.classList.add('incorrect');
                }
            });
        }

        // Show result notification
        const message = isCorrect ? 'Correct! Well done!' : 'Not quite right. Let\'s review.';
        const type = isCorrect ? 'success' : 'error';
        this.showNotification(message, type);
    }

    flipToBack() {
        const wrapper = document.getElementById('flashcard');
        const back = document.getElementById('flashcard-back');

        wrapper.classList.add('flipped');
        back.classList.remove('hidden');

        // Update result indicator
        const indicator = document.getElementById('result-indicator');
        const isCorrect = this.evaluateAnswer();

        if (isCorrect) {
            indicator.textContent = 'Correct!';
            indicator.className = 'px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800';
        } else {
            indicator.textContent = 'Review';
            indicator.className = 'px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800';
        }
    }

    flipToFront() {
        const wrapper = document.getElementById('flashcard');
        const back = document.getElementById('flashcard-back');

        wrapper.classList.remove('flipped');
        setTimeout(() => {
            back.classList.add('hidden');
        }, 300);
    }

    rateDifficulty(difficulty) {
        const studyTime = Date.now() - this.startTime;
        const hintsUsed = this.currentHintLevel;
        const isCorrect = this.evaluateAnswer();

        // Send performance data
        this.submitPerformance({
            difficulty: difficulty,
            studyTime: studyTime,
            hintsUsed: hintsUsed,
            correct: isCorrect,
            userAnswer: this.userAnswer
        });

        // Trigger next card or completion
        this.onComplete(difficulty);
    }

    recordPerformance(isCorrect) {
        // Record basic performance metrics
        const performance = {
            flashcard_id: this.flashcard.id,
            correct: isCorrect,
            study_time: Date.now() - this.startTime,
            hints_used: this.currentHintLevel,
            user_answer: this.userAnswer
        };

        // Store in session for batch submission
        const sessionData = JSON.parse(sessionStorage.getItem('flashcard_session') || '[]');
        sessionData.push(performance);
        sessionStorage.setItem('flashcard_session', JSON.stringify(sessionData));
    }

    submitPerformance(data) {
        // Submit to backend
        fetch('/api/flashcards/performance/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify(data)
        }).catch(error => {
            console.error('Error submitting performance:', error);
        });
    }

    handleKeyboard(e) {
        if (this.isAnswered) {
            // Back side shortcuts
            switch(e.key) {
                case '1':
                    document.getElementById('difficulty-btn')?.click();
                    break;
                case '2':
                    document.getElementById('good-btn')?.click();
                    break;
                case '3':
                    document.getElementById('easy-btn')?.click();
                    break;
                case 'r':
                    this.flipToFront();
                    break;
            }
        } else {
            // Front side shortcuts
            switch(e.key) {
                case 'h':
                    this.showHint();
                    break;
                case ' ':
                    e.preventDefault();
                    this.playAudio();
                    break;
                case 'Enter':
                    if (document.getElementById('user-answer')) {
                        this.checkFreeResponse();
                    }
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                    const choiceIndex = parseInt(e.key) - 1;
                    const choices = document.querySelectorAll('.choice-btn');
                    if (choices[choiceIndex]) {
                        choices[choiceIndex].click();
                    }
                    break;
            }
        }
    }

    showNotification(message, type = 'info') {
        // Create notification
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    getCSRFToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    onComplete(difficulty) {
        // Override this method to handle card completion
        console.log('Flashcard completed with difficulty:', difficulty);
    }
}

// Initialize flashcard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // This will be populated by the Django template
    const flashcardData = {
        id: '{{ flashcard.id }}',
        question_text: '{{ flashcard.question_text|escapejs }}',
        answer_text: '{{ flashcard.answer_text|escapejs }}',
        explanation_text: '{{ flashcard.explanation_text|escapejs }}',
        hint_text: '{{ flashcard.hint_text|escapejs }}',
        choices_json: {{ flashcard.choices_json|default:"[]"|safe }},
        progressive_hints: {{ flashcard.progressive_hints|default:"[]"|safe }},
        language: '{{ flashcard.language }}',
        cultural_context: '{{ flashcard.cultural_context|escapejs }}',
        memory_technique: '{{ flashcard.memory_technique|escapejs }}',
        usage_examples: '{{ flashcard.usage_examples|escapejs }}'
    };

    window.flashcardInstance = new EnhancedFlashcard(flashcardData);
});
</script>
