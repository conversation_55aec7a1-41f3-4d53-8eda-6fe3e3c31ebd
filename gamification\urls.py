from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    BadgeViewSet,
    AchievementViewSet,
    StreakViewSet,
    LevelViewSet,
    generate_flashcard,
    submit_flashcard_answer,
    preload_daily_content,
    get_adaptive_content,
    track_performance,
    get_learning_analytics,
    process_content_queue,
    get_content_recommendations,
    # New C.A.R.E. framework endpoints
    get_care_analytics,
    generate_care_content,
    update_care_progress,
    populate_user_queue,
)

router = DefaultRouter()
router.register(r"badges", BadgeViewSet)
router.register(r"achievements", AchievementViewSet)
router.register(r"streaks", StreakViewSet)
router.register(r"levels", LevelViewSet)

urlpatterns = [
    path("", include(router.urls)),
    # Original endpoints
    path("flashcard/", generate_flashcard, name="generate_flashcard"),
    path("answer/", submit_flashcard_answer, name="submit_flashcard_answer"),
    path("preload/", preload_daily_content, name="preload_daily_content"),
    # Adaptive learning endpoints
    path("adaptive-content/", get_adaptive_content, name="get_adaptive_content"),
    path("track-performance/", track_performance, name="track_performance"),
    path("analytics/", get_learning_analytics, name="get_learning_analytics"),
    path(
        "recommendations/",
        get_content_recommendations,
        name="get_content_recommendations",
    ),
    # C.A.R.E. Framework endpoints
    path("care/analytics/", get_care_analytics, name="get_care_analytics"),
    path("care/generate/", generate_care_content, name="generate_care_content"),
    path("care/progress/", update_care_progress, name="update_care_progress"),
    path("care/queue/", populate_user_queue, name="populate_user_queue"),
    # Admin/maintenance endpoints
    path("process-queue/", process_content_queue, name="process_content_queue"),
]
