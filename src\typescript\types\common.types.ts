/**
 * API and utility type definitions for TalonTalk
 */

// ============================================================================
// HTTP Types
// ============================================================================

export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface RequestConfig {
    method: HTTPMethod;
    headers?: Record<string, string>;
    body?: string | FormData | URLSearchParams;
    timeout?: number;
    signal?: AbortSignal;
}

export interface FetchResponse<T = unknown> {
    data: T;
    status: number;
    statusText: string;
    headers: Headers;
}

// ============================================================================
// CSRF Types
// ============================================================================

export interface CSRFToken {
    value: string;
    headerName: string;
}

// ============================================================================
// Local Storage Types
// ============================================================================

export interface StorageKey {
    LESSON_PROGRESS: string;
    USER_PREFERENCES: string;
    TUTOR_HISTORY: string;
    PHASE_COMPLETION: string;
}

export interface StoredLessonProgress {
    lessonId: string;
    currentPhase: string;
    completedPhases: string[];
    lastAccessed: string;
    timeSpent: number;
}

export interface UserPreferences {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    autoplay: boolean;
    animations: boolean;
    notifications: boolean;
}

// ============================================================================
// Event System Types
// ============================================================================

export interface EventBus {
    on<T>(event: string, handler: (data: T) => void): void;
    off(event: string, handler: Function): void;
    emit<T>(event: string, data: T): void;
    once<T>(event: string, handler: (data: T) => void): void;
}

// ============================================================================
// Analytics Types
// ============================================================================

export interface AnalyticsEvent {
    event: string;
    properties: Record<string, unknown>;
    timestamp: Date;
    userId?: string;
    sessionId?: string;
}

export interface PerformanceMetrics {
    pageLoadTime: number;
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    cumulativeLayoutShift: number;
    firstInputDelay: number;
}

// ============================================================================
// Logging Types
// ============================================================================

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    data?: unknown;
    stack?: string;
}

export interface Logger {
    debug(message: string, data?: unknown): void;
    info(message: string, data?: unknown): void;
    warn(message: string, data?: unknown): void;
    error(message: string, error?: Error, data?: unknown): void;
}

// ============================================================================
// Validation Types
// ============================================================================

export interface ValidationRule<T = unknown> {
    validate(value: T): boolean | string;
    message?: string;
}

export type ValidationSchema<T = Record<string, unknown>> = {
    [K in keyof T]?: ValidationRule<T[K]>[];
};

export interface ValidationResult {
    isValid: boolean;
    errors: Record<string, string[]>;
}

// ============================================================================
// Audio Types
// ============================================================================

export interface AudioPlayer {
    play(url: string): Promise<void>;
    pause(): void;
    stop(): void;
    setVolume(volume: number): void;
    isPlaying(): boolean;
}

export interface SpeechSynthesis {
    speak(text: string, language: string): Promise<void>;
    cancel(): void;
    pause(): void;
    resume(): void;
    getVoices(): SpeechSynthesisVoice[];
}

// ============================================================================
// Animation and UI Types
// ============================================================================

export interface Animator {
    animate(element: HTMLElement, keyframes: Keyframe[], options?: KeyframeAnimationOptions): Animation;
    fadeIn(element: HTMLElement, duration?: number): Promise<void>;
    fadeOut(element: HTMLElement, duration?: number): Promise<void>;
    slideIn(element: HTMLElement, direction?: 'up' | 'down' | 'left' | 'right', duration?: number): Promise<void>;
    slideOut(element: HTMLElement, direction?: 'up' | 'down' | 'left' | 'right', duration?: number): Promise<void>;
}

export interface Modal {
    open(): void;
    close(): void;
    toggle(): void;
    isOpen(): boolean;
    onOpen(callback: () => void): void;
    onClose(callback: () => void): void;
}

// ============================================================================
// Form Types
// ============================================================================

export interface FormData {
    [key: string]: string | number | boolean | File | null;
}

export interface FormField {
    name: string;
    type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file';
    label: string;
    placeholder?: string;
    required?: boolean;
    validation?: ValidationRule[];
    options?: Array<{ value: string; label: string }>;
}

export interface FormConfig {
    fields: FormField[];
    submitUrl: string;
    method: HTTPMethod;
    onSuccess?: (response: unknown) => void;
    onError?: (error: Error) => void;
}

// ============================================================================
// Theme Types
// ============================================================================

export interface Theme {
    name: string;
    colors: {
        primary: string;
        secondary: string;
        accent: string;
        background: string;
        surface: string;
        text: string;
        textSecondary: string;
        success: string;
        warning: string;
        error: string;
        info: string;
    };
    typography: {
        fontFamily: string;
        fontSize: {
            xs: string;
            sm: string;
            base: string;
            lg: string;
            xl: string;
            '2xl': string;
            '3xl': string;
            '4xl': string;
        };
        fontWeight: {
            light: number;
            normal: number;
            medium: number;
            semibold: number;
            bold: number;
        };
    };
    spacing: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
        '3xl': string;
        '4xl': string;
    };
    borderRadius: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        full: string;
    };
    shadows: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
    };
}

// ============================================================================
// Browser Types
// ============================================================================

export interface BrowserInfo {
    name: string;
    version: string;
    os: string;
    mobile: boolean;
    webgl: boolean;
    localStorage: boolean;
    sessionStorage: boolean;
    indexedDB: boolean;
    webAudio: boolean;
    speechSynthesis: boolean;
}

// ============================================================================
// Utility Function Types
// ============================================================================

export type Debounced<T extends (...args: any[]) => any> = T & {
    cancel(): void;
    flush(): ReturnType<T>;
};

export type Throttled<T extends (...args: any[]) => any> = T & {
    cancel(): void;
};

export interface UtilityFunctions {
    debounce<T extends (...args: any[]) => any>(fn: T, delay: number): Debounced<T>;
    throttle<T extends (...args: any[]) => any>(fn: T, limit: number): Throttled<T>;
    formatDate(date: Date, format: string): string;
    formatTime(seconds: number): string;
    generateId(prefix?: string): string;
    slugify(text: string): string;
    capitalize(text: string): string;
    truncate(text: string, length: number, suffix?: string): string;
    parseQuery(search: string): Record<string, string>;
    buildQuery(params: Record<string, string | number | boolean>): string;
}

// ============================================================================
// Global Window Extensions
// ============================================================================

declare global {
    interface Window {
        TalonTalk?: {
            config: any;
            version: string;
            build: string;
        };
    }
}
