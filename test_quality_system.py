#!/usr/bin/env python3
"""
Test the new quality-controlled flashcard generation system
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()

import logging
from ai_services.llm_flashcards import (
    LLMFlashcardService,
    generate_high_quality_flashcard,
)
from lessons.quality_control import (
    validate_and_clean_ai_flashcard,
    generate_improved_ai_prompt,
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_quality_flashcard_generation():
    """Test the new quality-controlled flashcard generation"""
    print("🧪 Testing Quality-Controlled Flashcard Generation")
    print("=" * 60)

    # Test words
    test_cases = [
        ("hello", "Hola"),
        ("food", "Comida"),
        ("water", "Agua"),
        ("house", "Casa"),
        ("thank you", "<PERSON><PERSON><PERSON>"),
    ]

    # Initialize LLM service
    try:
        llm_service = LLMFlashcardService()
        print(f"✅ LLM Service initialized: {llm_service.config.provider.value}")
    except Exception as e:
        print(f"❌ Failed to initialize LLM service: {e}")
        return

    results = {
        "total_attempts": 0,
        "successful_generations": 0,
        "quality_rejections": 0,
        "errors": 0,
    }

    for english_word, spanish_answer in test_cases:
        print(f"\n🎯 Testing: '{english_word}' -> '{spanish_answer}'")
        results["total_attempts"] += 1

        try:
            # Test the improved prompt generation
            prompt = generate_improved_ai_prompt(
                english_word, spanish_answer, "spanish"
            )
            print(f"📝 Generated prompt (first 100 chars): {prompt[:100]}...")

            # Test the quality-controlled generation
            quality_flashcard = generate_high_quality_flashcard(
                english_word, spanish_answer, llm_service
            )

            if quality_flashcard:
                results["successful_generations"] += 1
                print(f"✅ Generated quality flashcard:")
                print(f"   Question: {quality_flashcard['question']}")
                print(f"   Answer: {quality_flashcard['correct_answer']}")
                print(f"   Options: {quality_flashcard['options']}")
                print(
                    f"   Quality Check: {quality_flashcard.get('quality_checked', 'Unknown')}"
                )
            else:
                results["quality_rejections"] += 1
                print(f"❌ Quality gateway rejected flashcard")

        except Exception as e:
            results["errors"] += 1
            print(f"💥 Error generating flashcard: {e}")

    # Print summary
    print("\n" + "=" * 60)
    print("📊 QUALITY TEST RESULTS")
    print("=" * 60)
    print(f"Total Attempts: {results['total_attempts']}")
    print(f"Successful Generations: {results['successful_generations']}")
    print(f"Quality Rejections: {results['quality_rejections']}")
    print(f"Errors: {results['errors']}")

    success_rate = (results["successful_generations"] / results["total_attempts"]) * 100
    print(f"Success Rate: {success_rate:.1f}%")

    if success_rate >= 80:
        print("🎉 EXCELLENT: Quality system is working well!")
    elif success_rate >= 60:
        print("✅ GOOD: Quality system is working adequately")
    elif success_rate >= 40:
        print("⚠️  FAIR: Quality system needs improvement")
    else:
        print("❌ POOR: Quality system needs significant work")


if __name__ == "__main__":
    test_quality_flashcard_generation()
