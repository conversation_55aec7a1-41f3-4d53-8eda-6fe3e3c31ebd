"""
Content Generation Services for TalonTalk Language Learning
"""

from .content_generator import ContentGeneratorService
from .content_pipeline import ContentPipelineService

# Import new services
try:
    from .spaced_repetition import SpacedRepetitionService, spaced_repetition
    from .background_tasks import BackgroundTaskService, background_tasks

    ADVANCED_SERVICES_AVAILABLE = True
except ImportError:
    ADVANCED_SERVICES_AVAILABLE = False

# Initialize services
content_generator = ContentGeneratorService()
content_pipeline = ContentPipelineService()

if ADVANCED_SERVICES_AVAILABLE:
    __all__ = [
        "content_generator",
        "content_pipeline",
        "spaced_repetition",
        "background_tasks",
        "ContentGeneratorService",
        "ContentPipelineService",
        "SpacedRepetitionService",
        "BackgroundTaskService",
    ]
else:
    __all__ = [
        "content_generator",
        "content_pipeline",
        "ContentGeneratorService",
        "ContentPipelineService",
    ]
