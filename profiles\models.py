from django.db import models
from django.conf import settings

# Create your models here.


class Profile(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    avatar = models.ImageField(upload_to="avatars/", blank=True, null=True)
    display_name = models.CharField(max_length=100, blank=True, null=True)
    native_language = models.CharField(max_length=50)
    target_language = models.CharField(max_length=50)
    skill_level = models.CharField(max_length=50, default="Beginner")
    main_goal = models.CharField(max_length=100, default="Conversational Fluency")
    onboarding_completed = models.BooleanField(default=False)
    xp = models.PositiveIntegerField(default=0)
    streak = models.PositiveIntegerField(default=0)
    last_active = models.DateField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} Profile"
