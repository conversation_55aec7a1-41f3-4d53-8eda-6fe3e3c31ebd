"""
Adaptive Learning Engine for TalonTalk C.A.R.E. Framework
=========================================================

Enhanced adaptive learning algorithms that implement the C.A.R.E. framework:
- Contextualize: Real-world scenarios and cultural context
- Acquire: Progressive skill acquisition with multiple modalities
- Reinforce: Spaced repetition and memory consolidation
- Extend: Creative application and personalized challenges

Key Features:
1. C.A.R.E. Phase Analysis: Tracks effectiveness across all learning phases
2. Performance Analysis: Analyzes user accuracy, response times, and learning patterns
3. Difficulty Adjustment: Dynamically adjusts content difficulty based on performance
4. Content Personalization: Recommends content types and topics based on user needs
5. Learning Path Optimization: Optimizes the sequence and pacing of learning materials
6. Predictive Analytics: Predicts optimal learning times and content preferences
"""

import math
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from django.utils import timezone
from django.contrib.auth.models import User
from django.db.models import Avg, Count, Q
from django.core.cache import cache

from .models import (
    UserLearningProfile,
    UserContentPerformance,
    ContentItem,
    UserLessonQueue,
)

import logging

logger = logging.getLogger(__name__)


class AdaptiveLearningEngine:
    """
    Enhanced adaptive learning engine implementing the C.A.R.E. framework
    """

    def __init__(self):
        self.difficulty_levels = [choice[0] for choice in ContentItem.DIFFICULTY_LEVELS]
        self.content_types = [choice[0] for choice in ContentItem.CONTENT_TYPES]
        self.care_phases = ["contextualize", "acquire", "reinforce", "extend"]

        # Learning curve parameters
        self.mastery_threshold = 0.85  # 85% accuracy for mastery
        self.struggling_threshold = 0.60  # Below 60% indicates difficulty
        self.optimal_response_time_range = (3000, 8000)  # 3-8 seconds optimal

        # C.A.R.E. framework parameters
        self.care_phase_weights = {
            "contextualize": 0.20,  # 20% context setting
            "acquire": 0.40,  # 40% new learning
            "reinforce": 0.30,  # 30% practice/review
            "extend": 0.10,  # 10% creative application
        }

        self.cache_timeout = 300  # 5 minutes

    def analyze_care_performance(self, user: User) -> Dict:
        """
        Analyze user performance across C.A.R.E. framework phases
        """
        cache_key = f"care_performance_{user.id}"
        cached_result = cache.get(cache_key)

        if cached_result:
            return cached_result

        try:
            performances = user.content_performances.all()

            care_analysis = {
                "overall_score": 0.0,
                "phase_performance": {},
                "recommendations": [],
                "optimal_sequence": [],
                "timestamp": timezone.now().isoformat(),
            }

            for phase in self.care_phases:
                phase_performances = performances.filter(
                    content_item__tags__contains=[phase]
                )

                if phase_performances.exists():
                    avg_proficiency = (
                        phase_performances.aggregate(avg=Avg("proficiency_score"))[
                            "avg"
                        ]
                        or 0.0
                    )

                    total_attempts = phase_performances.count()

                    care_analysis["phase_performance"][phase] = {
                        "proficiency": round(avg_proficiency, 2),
                        "attempts": total_attempts,
                        "mastery_level": self._calculate_mastery_level(avg_proficiency),
                        "needs_attention": avg_proficiency < self.struggling_threshold,
                    }
                else:
                    care_analysis["phase_performance"][phase] = {
                        "proficiency": 0.0,
                        "attempts": 0,
                        "mastery_level": "not_started",
                        "needs_attention": True,
                    }

            # Calculate overall C.A.R.E. score
            care_analysis["overall_score"] = self._calculate_care_overall_score(
                care_analysis["phase_performance"]
            )

            # Generate C.A.R.E.-specific recommendations
            care_analysis["recommendations"] = self._generate_care_recommendations(
                care_analysis["phase_performance"]
            )

            # Determine optimal learning sequence
            care_analysis["optimal_sequence"] = self._determine_optimal_care_sequence(
                user, care_analysis["phase_performance"]
            )

            cache.set(cache_key, care_analysis, self.cache_timeout)
            return care_analysis

        except Exception as e:
            logger.error(
                f"Error analyzing C.A.R.E. performance for user {user.id}: {str(e)}"
            )
            return self._default_care_analysis()

    def _calculate_mastery_level(self, proficiency: float) -> str:
        """Calculate mastery level based on proficiency score"""
        if proficiency >= 0.9:
            return "mastered"
        elif proficiency >= 0.75:
            return "proficient"
        elif proficiency >= 0.5:
            return "developing"
        elif proficiency > 0:
            return "struggling"
        else:
            return "not_started"

    def _calculate_care_overall_score(self, phase_performance: Dict) -> float:
        """Calculate weighted overall C.A.R.E. framework score"""
        total_score = 0.0

        for phase, weight in self.care_phase_weights.items():
            phase_score = phase_performance.get(phase, {}).get("proficiency", 0.0)
            total_score += phase_score * weight

        return round(total_score, 2)

    def _generate_care_recommendations(self, phase_performance: Dict) -> List[str]:
        """Generate specific recommendations based on C.A.R.E. performance"""
        recommendations = []

        for phase, data in phase_performance.items():
            proficiency = data["proficiency"]
            attempts = data["attempts"]

            if attempts == 0:
                recommendations.append(
                    f"Start with {phase.capitalize()} phase exercises to build foundation"
                )
            elif proficiency < 0.3:
                recommendations.append(
                    f"{phase.capitalize()} phase needs significant attention - focus on basics"
                )
            elif proficiency < 0.6:
                recommendations.append(
                    f"Continue practicing {phase.capitalize()} phase exercises"
                )
            elif proficiency >= 0.8:
                if phase == "extend":
                    recommendations.append(
                        f"Excellent mastery of {phase.capitalize()} phase!"
                    )
                else:
                    recommendations.append(
                        f"Ready to advance from {phase.capitalize()} to more challenging content"
                    )

        # Overall C.A.R.E. recommendations
        contextualize_score = phase_performance.get("contextualize", {}).get(
            "proficiency", 0
        )
        acquire_score = phase_performance.get("acquire", {}).get("proficiency", 0)

        if contextualize_score > acquire_score + 0.2:
            recommendations.append(
                "Balance cultural context with more skill acquisition exercises"
            )
        elif acquire_score > contextualize_score + 0.2:
            recommendations.append(
                "Add more cultural context to better understand real-world usage"
            )

        return recommendations

    def _determine_optimal_care_sequence(
        self, user: User, phase_performance: Dict
    ) -> List[str]:
        """Determine optimal sequence of C.A.R.E. phases for next session"""
        sequence = []

        # Identify weakest phase for priority attention
        weakest_phase = min(
            phase_performance.keys(), key=lambda x: phase_performance[x]["proficiency"]
        )

        # Identify strongest phase for confidence building
        strongest_phase = max(
            phase_performance.keys(), key=lambda x: phase_performance[x]["proficiency"]
        )

        # Optimal sequence strategy:
        # 1. Start with contextualize (always begin with context)
        sequence.append("contextualize")

        # 2. Focus on weakest area for improvement
        if weakest_phase != "contextualize":
            sequence.append(weakest_phase)

        # 3. Acquire new knowledge
        if "acquire" not in sequence:
            sequence.append("acquire")

        # 4. Reinforce learning
        if "reinforce" not in sequence:
            sequence.append("reinforce")

        # 5. End with extension for engagement
        if "extend" not in sequence:
            sequence.append("extend")

        return sequence

    def optimize_content_sequence_with_care(
        self, user: User, content_ids: List[int]
    ) -> List[int]:
        """
        Optimize content sequence using C.A.R.E. framework principles
        """
        try:
            if not content_ids:
                return []

            care_analysis = self.analyze_care_performance(user)
            optimal_sequence = care_analysis["optimal_sequence"]

            content_items = ContentItem.objects.filter(id__in=content_ids)

            # Group content by C.A.R.E. phases
            phase_groups = {phase: [] for phase in self.care_phases}
            unclassified = []

            for item in content_items:
                classified = False
                for phase in self.care_phases:
                    if phase in item.tags:
                        phase_groups[phase].append(item)
                        classified = True
                        break

                if not classified:
                    unclassified.append(item)

            # Build optimized sequence following C.A.R.E. order
            optimized_ids = []

            for phase in optimal_sequence:
                phase_content = phase_groups.get(phase, [])
                if phase_content:
                    # Sort by difficulty and user performance within each phase
                    sorted_phase_content = self._sort_phase_content(user, phase_content)
                    optimized_ids.extend([item.id for item in sorted_phase_content])

            # Add unclassified content at the end
            optimized_ids.extend([item.id for item in unclassified])

            logger.info(
                f"Optimized C.A.R.E. sequence for user {user.id}: {len(optimized_ids)} items"
            )
            return optimized_ids

        except Exception as e:
            logger.error(
                f"Error optimizing C.A.R.E. sequence for user {user.id}: {str(e)}"
            )
            return content_ids

    def _sort_phase_content(
        self, user: User, content_items: List[ContentItem]
    ) -> List[ContentItem]:
        """Sort content within a C.A.R.E. phase by optimal learning progression"""
        user_performances = {
            perf.content_item_id: perf
            for perf in user.content_performances.filter(
                content_item_id__in=[item.id for item in content_items]
            )
        }

        profile = user.adaptive_learning_profile
        user_difficulty = profile.preferred_difficulty

        def sort_key(item):
            performance = user_performances.get(item.id)

            # Priority factors:
            # 1. Difficulty match (prefer slightly challenging)
            difficulty_score = 1.0 / (abs(item.difficulty - user_difficulty) + 1)

            # 2. Performance-based priority (struggling content first)
            if performance:
                proficiency_score = 1.0 - performance.proficiency_score
                review_priority = 1.0 if performance.needs_review else 0.5
            else:
                proficiency_score = 0.5  # New content gets medium priority
                review_priority = 0.0

            # 3. Content quality (success rate)
            quality_score = (
                item.success_rate / 100.0 if item.total_attempts > 0 else 0.5
            )

            return (
                (proficiency_score * 0.4)
                + (difficulty_score * 0.3)
                + (review_priority * 0.2)
                + (quality_score * 0.1)
            )

        return sorted(content_items, key=sort_key, reverse=True)

    def _default_care_analysis(self) -> Dict:
        """Return default C.A.R.E. analysis for new users"""
        return {
            "overall_score": 0.0,
            "phase_performance": {
                phase: {
                    "proficiency": 0.0,
                    "attempts": 0,
                    "mastery_level": "not_started",
                    "needs_attention": True,
                }
                for phase in self.care_phases
            },
            "recommendations": [
                "Welcome to the C.A.R.E. framework! Start with Contextualize phase to build cultural awareness.",
                "The C.A.R.E. framework will guide your learning: Context → Acquire → Reinforce → Extend",
            ],
            "optimal_sequence": ["contextualize", "acquire", "reinforce", "extend"],
            "timestamp": timezone.now().isoformat(),
        }

    def analyze_user_performance(self, user: User, days: int = 30) -> Dict:
        """
        Comprehensive analysis of user learning performance
        """
        # Get recent performance data
        since_date = timezone.now() - timedelta(days=days)
        metrics = UserContentPerformance.objects.filter(
            user=user, updated_at__gte=since_date
        ).order_by("updated_at")

        if not metrics.exists():
            return self._get_default_analysis(user)

        # Calculate core metrics using actual model fields
        total_questions = sum(m.times_seen for m in metrics)
        total_correct = sum(m.times_correct for m in metrics)

        # Calculate average response time (handle None values)
        valid_response_times = [
            m.average_response_time
            for m in metrics
            if m.average_response_time is not None
        ]
        avg_time_per_question = (
            statistics.mean(valid_response_times) if valid_response_times else 0
        )

        accuracy_rate = total_correct / total_questions if total_questions > 0 else 0

        # Analyze trends
        trend_analysis = self._analyze_performance_trends(metrics)

        # Identify strengths and weaknesses
        topic_analysis = self._analyze_topic_performance(metrics)

        # Calculate learning velocity
        learning_velocity = self._calculate_learning_velocity(metrics)

        # Determine optimal difficulty
        optimal_difficulty = self._determine_optimal_difficulty(metrics)

        # Engagement analysis
        engagement_metrics = self._analyze_engagement(metrics)

        return {
            "summary": {
                "total_sessions": metrics.count(),
                "total_questions": total_questions,
                "overall_accuracy": accuracy_rate,
                "avg_time_per_question": avg_time_per_question,
                "learning_velocity": learning_velocity,
            },
            "trends": trend_analysis,
            "topics": topic_analysis,
            "optimal_difficulty": optimal_difficulty,
            "engagement": engagement_metrics,
            "recommendations": self._generate_recommendations(
                metrics, accuracy_rate, trend_analysis
            ),
        }

    def get_personalized_content_params(
        self, user: User, session_context: Dict = None
    ) -> Dict:
        """
        Get personalized parameters for content generation
        """
        analysis = self.analyze_user_performance(user)
        profile = self._get_user_profile(user)

        # Base parameters from profile
        params = {
            "difficulty": analysis["optimal_difficulty"],
            "content_types": self._recommend_content_types(analysis, profile),
            "topics": self._recommend_topics(analysis),
            "pacing": self._recommend_pacing(analysis),
            "session_length": self._recommend_session_length(analysis, session_context),
        }

        # Adjust based on current session context
        if session_context:
            params = self._adjust_for_session_context(params, session_context, analysis)

        return params

    def update_user_profile(
        self, user: User, performance_data: Dict
    ) -> UserLearningProfile:
        """
        Update user learning profile based on recent performance
        """
        profile = self._get_user_profile(user)
        analysis = self.analyze_user_performance(user)

        # Update preferences based on performance
        optimal_difficulty = analysis["optimal_difficulty"]
        if isinstance(optimal_difficulty, str):
            profile.preferred_difficulty = self._convert_difficulty_to_int(
                optimal_difficulty
            )
        else:
            profile.preferred_difficulty = optimal_difficulty
        profile.average_accuracy = analysis["summary"]["overall_accuracy"]
        profile.total_sessions += 1

        # Update strengths and weaknesses
        profile.strengths = analysis["topics"]["strong_topics"][:5]
        profile.weaknesses = analysis["topics"]["weak_topics"][:5]

        # Update learning patterns
        profile.learning_patterns.update(
            {
                "avg_session_time": analysis["summary"]["avg_time_per_question"],
                "preferred_time_of_day": self._determine_preferred_time(user),
                "learning_velocity": analysis["summary"]["learning_velocity"],
                "last_updated": timezone.now().isoformat(),
            }
        )

        # Update motivation and engagement scores
        profile.motivation_level = analysis["engagement"]["motivation_score"]
        profile.engagement_score = analysis["engagement"]["engagement_score"]

        profile.save()
        return profile

    def predict_optimal_content(
        self, user: User, available_content: List[ContentItem]
    ) -> List[Dict]:
        """
        Predict and rank the most suitable content for the user
        """
        if not available_content:
            return []

        analysis = self.analyze_user_performance(user)
        profile = self._get_user_profile(user)

        scored_content = []

        for content in available_content:
            score = self._calculate_content_suitability_score(
                content, analysis, profile
            )

            scored_content.append(
                {
                    "content": content,
                    "suitability_score": score,
                    "reasons": self._explain_content_score(
                        content, analysis, profile, score
                    ),
                }
            )

        # Sort by suitability score (highest first)
        scored_content.sort(key=lambda x: x["suitability_score"], reverse=True)

        return scored_content

    def adjust_difficulty_dynamically(
        self, user: User, current_session_performance: Dict
    ) -> str:
        """
        Dynamically adjust difficulty during a session based on real-time performance
        """
        session_accuracy = current_session_performance.get("accuracy", 0)
        session_response_times = current_session_performance.get("response_times", [])
        current_difficulty = current_session_performance.get(
            "current_difficulty", "beginner"
        )

        # Calculate average response time
        avg_response_time = (
            statistics.mean(session_response_times) if session_response_times else 5000
        )

        # Get difficulty level index
        if isinstance(current_difficulty, str):
            current_difficulty_int = self._convert_difficulty_to_int(current_difficulty)
        else:
            current_difficulty_int = current_difficulty

        try:
            current_level_idx = self.difficulty_levels.index(current_difficulty_int)
        except ValueError:
            # Default to beginner if difficulty not found
            current_level_idx = 0

        # Decision logic for difficulty adjustment
        if (
            session_accuracy >= self.mastery_threshold
            and avg_response_time < self.optimal_response_time_range[1]
        ):
            # User is mastering current level quickly - increase difficulty
            if current_level_idx < len(self.difficulty_levels) - 1:
                new_difficulty = self.difficulty_levels[current_level_idx + 1]
                reason = "High accuracy and fast response times indicate readiness for higher difficulty"
            else:
                new_difficulty = current_difficulty
                reason = "Already at maximum difficulty level"

        elif (
            session_accuracy <= self.struggling_threshold
            or avg_response_time > self.optimal_response_time_range[1] * 1.5
        ):
            # User is struggling - decrease difficulty
            if current_level_idx > 0:
                new_difficulty = self.difficulty_levels[current_level_idx - 1]
                reason = "Low accuracy or slow response times indicate need for easier content"
            else:
                new_difficulty = current_difficulty
                reason = "Already at minimum difficulty level"

        else:
            # Maintain current difficulty
            new_difficulty = current_difficulty
            reason = "Performance indicators suggest current difficulty is appropriate"

        logger.info(
            f"Difficulty adjustment for user {user.id}: {current_difficulty} -> {new_difficulty}. Reason: {reason}"
        )

        return new_difficulty

    def generate_learning_insights(self, user: User) -> Dict:
        """
        Generate actionable insights about the user's learning journey
        """
        analysis = self.analyze_user_performance(user)
        profile = self._get_user_profile(user)

        insights = {
            "performance_insights": self._generate_performance_insights(analysis),
            "learning_patterns": self._generate_pattern_insights(analysis, profile),
            "recommendations": self._generate_detailed_recommendations(
                analysis, profile
            ),
            "progress_tracking": self._generate_progress_insights(analysis),
            "motivation_tips": self._generate_motivation_tips(analysis, profile),
        }

        return insights

    # Private helper methods

    def _get_default_analysis(self, user: User) -> Dict:
        """Return default analysis for new users"""
        return {
            "summary": {
                "total_sessions": 0,
                "total_questions": 0,
                "overall_accuracy": 0.0,
                "avg_time_per_question": 0.0,
                "learning_velocity": 0.0,
            },
            "trends": {"trend": "insufficient_data"},
            "topics": {"strong_topics": [], "weak_topics": []},
            "optimal_difficulty": "beginner",
            "engagement": {"motivation_score": 0.5, "engagement_score": 0.5},
            "recommendations": [
                "Start with beginner-level content",
                "Focus on building foundational vocabulary",
            ],
        }

    def _get_user_profile(self, user: User) -> UserLearningProfile:
        """Get or create user learning profile"""
        profile, created = UserLearningProfile.objects.get_or_create(
            user=user,
            defaults={
                "preferred_difficulty": 1,  # 1 = Beginner
                "preferred_content_types": ["flashcard"],
                "learning_pace": "moderate",
            },
        )
        return profile

    def _analyze_performance_trends(
        self, metrics: List[UserContentPerformance]
    ) -> Dict:
        """Analyze performance trends over time"""
        if len(metrics) < 3:
            return {"trend": "insufficient_data"}

        # Calculate accuracy trend
        accuracies = [m.accuracy_rate() for m in metrics]

        # Simple linear trend calculation
        x_values = list(range(len(accuracies)))
        trend_slope = self._calculate_trend_slope(x_values, accuracies)

        if trend_slope > 0.01:
            trend = "improving"
        elif trend_slope < -0.01:
            trend = "declining"
        else:
            trend = "stable"

        # Calculate recent vs overall performance
        recent_accuracy = (
            statistics.mean(accuracies[-5:])
            if len(accuracies) >= 5
            else statistics.mean(accuracies)
        )
        overall_accuracy = statistics.mean(accuracies)

        return {
            "trend": trend,
            "slope": trend_slope,
            "recent_accuracy": recent_accuracy,
            "overall_accuracy": overall_accuracy,
            "improvement_rate": recent_accuracy - overall_accuracy,
        }

    def _calculate_trend_slope(
        self, x_values: List[int], y_values: List[float]
    ) -> float:
        """Calculate linear trend slope"""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0

        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x_squared = sum(x * x for x in x_values)

        denominator = n * sum_x_squared - sum_x * sum_x
        if denominator == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope

    def _analyze_topic_performance(self, metrics: List[UserContentPerformance]) -> Dict:
        """Analyze performance by topic"""
        topic_performance = {}

        for metric in metrics:
            for topic in metric.topics_covered:
                if topic not in topic_performance:
                    topic_performance[topic] = {"correct": 0, "total": 0}

                topic_performance[topic]["correct"] += metric.correct_answers
                topic_performance[topic]["total"] += metric.question_count

        # Calculate accuracy per topic
        topic_accuracies = {}
        for topic, data in topic_performance.items():
            if data["total"] > 0:
                topic_accuracies[topic] = data["correct"] / data["total"]

        # Sort topics by performance
        sorted_topics = sorted(
            topic_accuracies.items(), key=lambda x: x[1], reverse=True
        )

        strong_topics = [
            topic
            for topic, accuracy in sorted_topics
            if accuracy >= self.mastery_threshold
        ]
        weak_topics = [
            topic
            for topic, accuracy in sorted_topics
            if accuracy <= self.struggling_threshold
        ]

        return {
            "topic_accuracies": topic_accuracies,
            "strong_topics": strong_topics,
            "weak_topics": weak_topics,
            "total_topics_covered": len(topic_accuracies),
        }

    def _calculate_learning_velocity(
        self, metrics: List[UserContentPerformance]
    ) -> float:
        """Calculate how quickly the user is learning (improvement rate)"""
        if len(metrics) < 2:
            return 0.0

        # Calculate improvement in accuracy over time
        accuracies = [m.accuracy_rate() for m in metrics]

        # Use recent sessions vs earlier sessions
        if len(accuracies) >= 6:
            recent_avg = statistics.mean(accuracies[-3:])
            earlier_avg = statistics.mean(accuracies[:3])
            velocity = (recent_avg - earlier_avg) / len(accuracies)
        else:
            velocity = (accuracies[-1] - accuracies[0]) / len(accuracies)

        return max(0.0, velocity)  # Ensure non-negative

    def _determine_optimal_difficulty(
        self, metrics: List[UserContentPerformance]
    ) -> str:
        """Determine optimal difficulty level based on performance"""
        if not metrics:
            return "beginner"

        # Get recent performance
        recent_metrics = metrics[-10:] if len(metrics) >= 10 else metrics

        # Calculate weighted accuracy (more weight on recent sessions)
        weighted_accuracy = 0.0
        total_weight = 0.0

        for i, metric in enumerate(recent_metrics):
            weight = (i + 1) / len(recent_metrics)  # More weight for recent
            weighted_accuracy += metric.accuracy_rate() * weight
            total_weight += weight

        if total_weight > 0:
            weighted_accuracy /= total_weight

        # Determine difficulty based on accuracy
        if weighted_accuracy >= 0.9:
            current_difficulty = recent_metrics[-1].difficulty_attempted
            current_idx = self.difficulty_levels.index(current_difficulty)
            # Move up if possible
            if current_idx < len(self.difficulty_levels) - 1:
                return self.difficulty_levels[current_idx + 1]
            else:
                return current_difficulty
        elif weighted_accuracy >= 0.75:
            # Maintain current level
            return recent_metrics[-1].difficulty_attempted
        elif weighted_accuracy >= 0.6:
            # Stay at current or go down slightly
            current_difficulty = recent_metrics[-1].difficulty_attempted
            current_idx = self.difficulty_levels.index(current_difficulty)
            if current_idx > 0 and weighted_accuracy < 0.65:
                return self.difficulty_levels[current_idx - 1]
            else:
                return current_difficulty
        else:
            # Move down in difficulty
            current_difficulty = recent_metrics[-1].difficulty_attempted
            current_idx = self.difficulty_levels.index(current_difficulty)
            if current_idx > 0:
                return self.difficulty_levels[current_idx - 1]
            else:
                return "beginner"

    def _analyze_engagement(self, metrics: List[UserContentPerformance]) -> Dict:
        """Analyze user engagement patterns"""
        if not metrics:
            return {"motivation_score": 0.5, "engagement_score": 0.5}

        # Calculate engagement indicators using available fields
        # Use accuracy rate as a proxy for completion rate
        accuracy_rates = [m.times_correct / max(m.times_seen, 1) for m in metrics]
        avg_accuracy_rate = statistics.mean(accuracy_rates)

        # Use average response time if available
        valid_response_times = [
            m.average_response_time
            for m in metrics
            if m.average_response_time is not None
        ]
        avg_response_time = (
            statistics.mean(valid_response_times) if valid_response_times else 5.0
        )

        # Session frequency (sessions per week)
        time_span = (metrics[-1].updated_at - metrics[0].updated_at).days
        sessions_per_week = len(metrics) / max(time_span / 7, 1)

        # Calculate scores (0-1 scale)
        # Motivation score based on accuracy rate and session frequency
        motivation_score = min(
            1.0, (avg_accuracy_rate + min(sessions_per_week / 5, 1.0)) / 2
        )

        # Engagement score based on response time and consistency
        optimal_response_time = 8.0  # 8 seconds
        time_score = (
            1.0 - abs(avg_response_time - optimal_response_time) / optimal_response_time
        )
        time_score = max(0.0, min(1.0, time_score))

        engagement_score = (time_score + min(sessions_per_week / 3, 1.0)) / 2

        return {
            "motivation_score": motivation_score,
            "engagement_score": engagement_score,
            "avg_response_time": avg_response_time,
            "avg_accuracy_rate": avg_accuracy_rate,
            "sessions_per_week": sessions_per_week,
        }

    def _generate_recommendations(
        self, metrics: List[UserContentPerformance], accuracy: float, trends: Dict
    ) -> List[str]:
        """Generate learning recommendations"""
        recommendations = []

        if accuracy < 0.6:
            recommendations.append(
                "Consider reviewing foundational concepts before advancing"
            )
            recommendations.append("Practice with easier content to build confidence")
        elif accuracy > 0.85:
            recommendations.append("You're ready for more challenging content")
            recommendations.append("Consider exploring advanced topics")

        if trends.get("trend") == "declining":
            recommendations.append("Take a break to avoid learning fatigue")
            recommendations.append("Review previous lessons to reinforce learning")
        elif trends.get("trend") == "improving":
            recommendations.append("Great progress! Keep up the consistent practice")

        return recommendations

    def _generate_performance_insights(self, analysis: Dict) -> List[str]:
        """Generate performance-based insights"""
        insights = []
        summary = analysis.get("summary", {})

        accuracy = summary.get("accuracy_rate", 0)
        total_questions = summary.get("total_questions", 0)

        if total_questions == 0:
            insights.append("Start practicing to get personalized insights!")
            return insights

        if accuracy >= 0.9:
            insights.append("Excellent accuracy! You're mastering the material.")
        elif accuracy >= 0.7:
            insights.append("Good progress! Keep practicing to improve accuracy.")
        else:
            insights.append(
                "Focus on understanding before speed. Quality over quantity!"
            )

        return insights

    def _generate_pattern_insights(self, analysis: Dict, profile) -> List[str]:
        """Generate learning pattern insights"""
        return ["You learn best with consistent daily practice."]

    def _generate_detailed_recommendations(self, analysis: Dict, profile) -> List[str]:
        """Generate detailed learning recommendations"""
        return ["Try mixing different question types for better retention."]

    def _generate_progress_insights(self, analysis: Dict) -> List[str]:
        """Generate progress tracking insights"""
        return ["You're on track with your learning goals!"]

    def _generate_motivation_tips(self, analysis: Dict, profile) -> List[str]:
        """Generate motivation tips"""
        return ["Remember: Every expert was once a beginner!"]

    def _determine_preferred_time(self, user: User) -> str:
        """Determine user's preferred time of day based on activity patterns"""
        try:
            # Get recent performance data to analyze activity patterns
            recent_performance = UserContentPerformance.objects.filter(
                user=user, updated_at__gte=timezone.now() - timedelta(days=30)
            )

            if not recent_performance.exists():
                return "morning"  # Default preference

            # Analyze activity times (simplified heuristic)
            morning_count = recent_performance.filter(updated_at__hour__lt=12).count()
            afternoon_count = recent_performance.filter(
                updated_at__hour__gte=12, updated_at__hour__lt=18
            ).count()
            evening_count = recent_performance.filter(updated_at__hour__gte=18).count()

            if morning_count >= afternoon_count and morning_count >= evening_count:
                return "morning"
            elif afternoon_count >= evening_count:
                return "afternoon"
            else:
                return "evening"

        except Exception:
            return "morning"  # Safe default

    def _convert_difficulty_to_int(self, difficulty_str: str) -> int:
        """Convert difficulty string to integer value"""
        difficulty_map = {
            "beginner": 1,
            "elementary": 2,
            "intermediate": 3,
            "upper intermediate": 4,
            "upper_intermediate": 4,
            "advanced": 5,
        }
        return difficulty_map.get(difficulty_str.lower(), 1)  # Default to beginner


# Singleton instance
adaptive_engine = AdaptiveLearningEngine()
