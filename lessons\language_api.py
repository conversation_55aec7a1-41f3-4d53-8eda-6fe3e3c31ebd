"""
Language Management API for TalonTalk
RESTful endpoints for language selection and management
"""

import json
import logging
from typing import Dict, Any

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required

from .language_manager import LanguageManager
from .route_validators import comprehensive_care_validator
from .advanced_quality_engine import QualityMonitor

logger = logging.getLogger(__name__)


@require_http_methods(["GET"])
def get_supported_languages(request):
    """
    Get list of all supported languages with metadata
    """
    try:
        active_only = request.GET.get("active_only", "true").lower() == "true"
        languages = LanguageManager.get_supported_languages(active_only=active_only)

        # Format for frontend consumption
        language_list = []
        for code, info in languages.items():
            language_list.append(
                {
                    "code": code,
                    "name": info["name"],
                    "native_name": info["native_name"],
                    "flag": info["flag"],
                    "difficulty": info["difficulty_for_english"],
                    "active": info["active"],
                    "content_quality": info["content_quality"],
                    "ai_support": info["ai_support"],
                    "speech_code": info["speech_code"],
                    "rtl": info["rtl"],
                }
            )

        return JsonResponse(
            {
                "success": True,
                "languages": language_list,
                "total_count": len(language_list),
                "active_count": len([l for l in language_list if l["active"]]),
            }
        )

    except Exception as e:
        logger.error(f"Error getting supported languages: {e}")
        return JsonResponse(
            {"success": False, "error": "Failed to retrieve supported languages"},
            status=500,
        )


@comprehensive_care_validator(
    required_params=["language"],
    action="modify_language_settings",
    validate_quality=False,
    rate_limit=False,
)
@require_http_methods(["POST"])
@csrf_exempt
def set_user_language(request):
    """
    Set user's preferred language
    """
    try:
        validated_data = getattr(request, "validated_data", {})
        language_code = validated_data.get("language")

        # Validate language is active
        if not LanguageManager.is_language_active(language_code):
            return JsonResponse(
                {
                    "success": False,
                    "error": f"Language '{language_code}' is not currently available",
                    "error_code": "LANGUAGE_INACTIVE",
                },
                status=400,
            )

        # Set user language
        success = LanguageManager.set_user_language(request.user, language_code)

        if success:
            language_info = LanguageManager.get_language_info(language_code)
            return JsonResponse(
                {
                    "success": True,
                    "message": f"Language set to {language_info['name']}",
                    "language": {
                        "code": language_code,
                        "name": language_info["name"],
                        "native_name": language_info["native_name"],
                        "flag": language_info["flag"],
                    },
                }
            )
        else:
            return JsonResponse(
                {"success": False, "error": "Failed to update language preference"},
                status=500,
            )

    except Exception as e:
        logger.error(f"Error setting user language: {e}")
        return JsonResponse(
            {"success": False, "error": "Internal server error"}, status=500
        )


@login_required
@require_http_methods(["GET"])
def get_user_language(request):
    """
    Get user's current language preference
    """
    try:
        language_code = LanguageManager.get_user_language(request.user)
        language_info = LanguageManager.get_language_info(language_code)

        if not language_info:
            # Fallback to Spanish if user's language is not found
            language_code = "spanish"
            language_info = LanguageManager.get_language_info(language_code)

        return JsonResponse(
            {
                "success": True,
                "language": {
                    "code": language_code,
                    "name": language_info["name"],
                    "native_name": language_info["native_name"],
                    "flag": language_info["flag"],
                    "active": language_info["active"],
                    "speech_config": LanguageManager.get_speech_synthesis_config(
                        language_code
                    ),
                },
            }
        )

    except Exception as e:
        logger.error(f"Error getting user language: {e}")
        return JsonResponse(
            {"success": False, "error": "Failed to retrieve user language"}, status=500
        )


@require_http_methods(["GET"])
def validate_language_support(request, language_code):
    """
    Validate if a language is supported and get quality information
    """
    try:
        validation_result = LanguageManager.validate_language_content_quality(
            language_code
        )
        language_info = LanguageManager.get_language_info(language_code)

        response_data = {
            "success": True,
            "language_code": language_code,
            "supported": LanguageManager.is_language_supported(language_code),
            "active": LanguageManager.is_language_active(language_code),
            "validation": validation_result,
        }

        if language_info:
            response_data["language_info"] = {
                "name": language_info["name"],
                "native_name": language_info["native_name"],
                "flag": language_info["flag"],
                "content_quality": language_info["content_quality"],
                "ai_support": language_info["ai_support"],
            }

        return JsonResponse(response_data)

    except Exception as e:
        logger.error(f"Error validating language support: {e}")
        return JsonResponse(
            {"success": False, "error": "Failed to validate language support"},
            status=500,
        )


@comprehensive_care_validator(
    action="view_quality_report", validate_quality=False, rate_limit=True
)
@require_http_methods(["GET"])
def get_language_quality_report(request):
    """
    Get quality report for a specific language or overall system
    """
    try:
        language = request.GET.get("language")
        time_period = int(request.GET.get("days", 7))

        quality_monitor = QualityMonitor()

        if language:
            # Language-specific report
            if not LanguageManager.is_language_supported(language):
                return JsonResponse(
                    {
                        "success": False,
                        "error": f"Language '{language}' is not supported",
                    },
                    status=400,
                )

            report = quality_monitor.generate_quality_report(language=language)
        else:
            # Overall system report
            report = quality_monitor.generate_quality_report()

        # Add time period metrics
        metrics = quality_monitor.get_quality_metrics(time_period=time_period)
        report["time_period_metrics"] = metrics

        return JsonResponse({"success": True, "report": report})

    except ValueError:
        return JsonResponse(
            {"success": False, "error": "Invalid time period specified"}, status=400
        )
    except Exception as e:
        logger.error(f"Error generating quality report: {e}")
        return JsonResponse(
            {"success": False, "error": "Failed to generate quality report"}, status=500
        )


@require_http_methods(["GET"])
def get_language_statistics(request):
    """
    Get usage statistics for all languages
    """
    try:
        from .models import ContentItem, UserLearningProfile
        from django.db.models import Count

        # Content statistics by language
        content_stats = (
            ContentItem.objects.values("language")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # User preferences by language
        user_stats = (
            UserLearningProfile.objects.values("target_language")
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        # Format response
        languages = LanguageManager.get_supported_languages(active_only=False)
        language_stats = []

        for code, info in languages.items():
            content_count = next(
                (item["count"] for item in content_stats if item["language"] == code), 0
            )
            user_count = next(
                (
                    item["count"]
                    for item in user_stats
                    if item["target_language"] == code
                ),
                0,
            )

            language_stats.append(
                {
                    "code": code,
                    "name": info["name"],
                    "native_name": info["native_name"],
                    "flag": info["flag"],
                    "active": info["active"],
                    "content_count": content_count,
                    "user_count": user_count,
                    "content_quality": info["content_quality"],
                    "ai_support": info["ai_support"],
                }
            )

        # Sort by total activity (content + users)
        language_stats.sort(
            key=lambda x: x["content_count"] + x["user_count"], reverse=True
        )

        return JsonResponse(
            {
                "success": True,
                "statistics": language_stats,
                "total_languages": len(language_stats),
                "active_languages": len([l for l in language_stats if l["active"]]),
                "total_content": sum(l["content_count"] for l in language_stats),
                "total_users": sum(l["user_count"] for l in language_stats),
            }
        )

    except Exception as e:
        logger.error(f"Error getting language statistics: {e}")
        return JsonResponse(
            {"success": False, "error": "Failed to retrieve language statistics"},
            status=500,
        )


@login_required
def quality_dashboard_view(request):
    """
    Render the quality monitoring dashboard
    """
    # Only allow staff users to access quality dashboard
    if not request.user.is_staff:
        from django.http import HttpResponseForbidden

        return HttpResponseForbidden("Access denied. Staff privileges required.")

    from django.shortcuts import render

    return render(
        request,
        "quality/dashboard.html",
        {
            "page_title": "Quality Dashboard",
            "user": request.user,
        },
    )
