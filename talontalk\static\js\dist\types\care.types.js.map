{"version": 3, "file": "care.types.js", "sourceRoot": "", "sources": ["../../../../../src/typescript/types/care.types.ts"], "names": [], "mappings": "AAAA;;GAEG;AA6OH,+EAA+E;AAC/E,iDAAiD;AACjD,+EAA+E;AAE/E,MAAM,OAAO,SAAU,SAAQ,KAAK;IAClC,YACE,OAAe,EACC,IAAY,EACZ,KAAiB,EACjB,KAAe;QAE/B,KAAK,CAAC,OAAO,CAAC,CAAC;QAJC,SAAI,GAAJ,IAAI,CAAQ;QACZ,UAAK,GAAL,KAAK,CAAY;QACjB,UAAK,GAAL,KAAK,CAAU;QAG/B,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IAC1B,CAAC;CACF;AAED,MAAM,OAAO,QAAS,SAAQ,SAAS;IACrC,YACE,OAAe,EACC,UAAkB,EAClB,QAAgB,EAChC,KAAe;QAEf,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAJ9B,eAAU,GAAV,UAAU,CAAQ;QAClB,aAAQ,GAAR,QAAQ,CAAQ;QAIhC,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;IACzB,CAAC;CACF;AAED,MAAM,OAAO,eAAgB,SAAQ,SAAS;IAC5C,YACE,OAAe,EACC,KAAa,EAC7B,KAAe;QAEf,KAAK,CAAC,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAHrC,UAAK,GAAL,KAAK,CAAQ;QAI7B,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF"}