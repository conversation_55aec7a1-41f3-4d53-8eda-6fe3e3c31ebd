<!-- Welcome Back Modal with Content Pre-loading -->
<div id="welcome-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-2xl shadow-2xl max-w-md mx-4 p-8 text-center transform transition-all duration-500 scale-95 opacity-0" id="modal-content">
        <!-- User Greeting -->
        <div class="mb-6">
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5s3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18s-3.332.477-4.5 1.253"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">Welcome back!</h2>
            <p class="text-gray-600" id="personalized-greeting">We're preparing your lessons for today...</p>
        </div>

        <!-- Streak Display -->
        <div class="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-4 mb-6">
            <div class="flex items-center justify-center space-x-2">
                <span class="text-2xl">🔥</span>
                <span class="text-lg font-semibold text-orange-600" id="streak-count">Loading...</span>
                <span class="text-sm text-orange-500">day streak</span>
            </div>
        </div>

        <!-- Loading Progress -->
        <div class="space-y-4">
            <div class="flex items-center space-x-3" id="step-1">
                <div class="loading-spinner w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                <span class="text-sm text-gray-600">Personalizing your lessons...</span>
                <svg class="w-5 h-5 text-green-500 hidden" id="step-1-done" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <div class="flex items-center space-x-3" id="step-2">
                <div class="loading-spinner w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin opacity-50"></div>
                <span class="text-sm text-gray-600 opacity-50">Generating AI flashcards...</span>
                <svg class="w-5 h-5 text-green-500 hidden" id="step-2-done" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <div class="flex items-center space-x-3" id="step-3">
                <div class="loading-spinner w-5 h-5 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin opacity-50"></div>
                <span class="text-sm text-gray-600 opacity-50">Syncing your progress...</span>
                <svg class="w-5 h-5 text-green-500 hidden" id="step-3-done" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="mt-6">
            <div class="bg-gray-200 rounded-full h-2">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500" id="progress-bar" style="width: 0%"></div>
            </div>
            <p class="text-xs text-gray-500 mt-2" id="progress-text">Preparing your learning experience...</p>
        </div>

        <!-- Today's Goal Preview -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg hidden" id="goals-preview">
            <h3 class="font-semibold text-gray-800 mb-2">Today's Goals</h3>
            <div class="flex justify-between text-sm">
                <span class="text-gray-600">New Words:</span>
                <span class="font-medium" id="daily-new-words">5</span>
            </div>
            <div class="flex justify-between text-sm">
                <span class="text-gray-600">Review Items:</span>
                <span class="font-medium" id="daily-reviews">8</span>
            </div>
            <div class="flex justify-between text-sm">
                <span class="text-gray-600">Estimated Time:</span>
                <span class="font-medium" id="daily-time">12 min</span>
            </div>
        </div>
    </div>
</div>

<style>
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>

<script>
class WelcomeModalManager {
    constructor() {
        this.modal = document.getElementById('welcome-modal');
        this.modalContent = document.getElementById('modal-content');
        this.progressBar = document.getElementById('progress-bar');
        this.progressText = document.getElementById('progress-text');
        this.currentStep = 1;
        this.totalSteps = 3;
        this.preloadedContent = {
            todaysLessons: [],
            userProgress: null,
            dailyGoals: null
        };
    }

    async show() {
        // Show modal with animation
        this.modal.classList.remove('hidden');
        await this.sleep(100);
        this.modalContent.classList.remove('scale-95', 'opacity-0');
        this.modalContent.classList.add('scale-100', 'opacity-100');

        // Start the pre-loading process
        await this.executePreloadingSteps();
    }

    async executePreloadingSteps() {
        try {
            // Step 1: Personalize greeting and load user data
            await this.step1_PersonalizeGreeting();
            
            // Step 2: Generate and cache today's flashcards
            await this.step2_GenerateFlashcards();
            
            // Step 3: Sync progress and prepare daily goals
            await this.step3_SyncProgress();
            
            // Show completion and close modal
            await this.showCompletionAndClose();
            
        } catch (error) {
            console.error('Pre-loading failed:', error);
            await this.handlePreloadingError();
        }
    }

    async step1_PersonalizeGreeting() {
        this.activateStep(1);
        this.updateProgress(10, "Loading your profile...");

        try {
            // Fetch user profile and streak data
            const userProfile = await this.fetchUserProfile();
            const streakData = await this.fetchStreakData();

            // Update greeting
            document.getElementById('personalized-greeting').textContent = 
                `Great to see you back, ${userProfile.firstName || 'Learner'}!`;
            
            // Update streak
            document.getElementById('streak-count').textContent = streakData.currentStreak;

            this.updateProgress(33, "Profile loaded!");
            this.completeStep(1);
            
        } catch (error) {
            console.error('Step 1 failed:', error);
            this.completeStep(1); // Continue anyway
        }
    }

    async step2_GenerateFlashcards() {
        this.activateStep(2);
        this.updateProgress(40, "Generating your personalized lessons...");

        try {
            // Generate multiple lessons in parallel for faster loading
            const lessonPromises = [
                this.generateLesson('vocabulary', 'beginner', 5),
                this.generateLesson('grammar', 'beginner', 3),
                this.generateLesson('conversation', 'beginner', 4)
            ];

            const lessons = await Promise.all(lessonPromises);
            this.preloadedContent.todaysLessons = lessons.flat();

            // Cache lessons in localStorage for offline access
            localStorage.setItem('dailyLessons', JSON.stringify({
                date: new Date().toDateString(),
                lessons: this.preloadedContent.todaysLessons
            }));

            this.updateProgress(66, "Lessons ready!");
            this.completeStep(2);
            
        } catch (error) {
            console.error('Step 2 failed:', error);
            // Fall back to cached lessons if available
            const cached = this.getCachedLessons();
            if (cached) {
                this.preloadedContent.todaysLessons = cached;
                this.updateProgress(66, "Using cached lessons...");
            }
            this.completeStep(2);
        }
    }

    async step3_SyncProgress() {
        this.activateStep(3);
        this.updateProgress(70, "Syncing your progress...");

        try {
            // Calculate daily goals based on user's history
            const dailyGoals = await this.calculateDailyGoals();
            this.preloadedContent.dailyGoals = dailyGoals;

            // Update goals preview
            document.getElementById('daily-new-words').textContent = dailyGoals.newWords;
            document.getElementById('daily-reviews').textContent = dailyGoals.reviews;
            document.getElementById('daily-time').textContent = `${dailyGoals.estimatedMinutes} min`;
            
            // Show goals preview
            document.getElementById('goals-preview').classList.remove('hidden');

            this.updateProgress(100, "Everything is ready!");
            this.completeStep(3);
            
        } catch (error) {
            console.error('Step 3 failed:', error);
            this.completeStep(3);
        }
    }

    async showCompletionAndClose() {
        // Show success message
        this.updateProgress(100, "Ready to learn! Let's go! 🚀");
        await this.sleep(1500);

        // Close modal with animation
        this.modalContent.classList.remove('scale-100', 'opacity-100');
        this.modalContent.classList.add('scale-95', 'opacity-0');
        await this.sleep(300);
        this.modal.classList.add('hidden');

        // Trigger ready event
        window.dispatchEvent(new CustomEvent('contentPreloaded', {
            detail: this.preloadedContent
        }));
    }

    async handlePreloadingError() {
        this.updateProgress(100, "Something went wrong, but you can still learn!");
        await this.sleep(2000);
        await this.showCompletionAndClose();
    }

    // Helper methods
    activateStep(stepNumber) {
        const step = document.getElementById(`step-${stepNumber}`);
        step.querySelector('.loading-spinner').classList.remove('opacity-50');
        step.querySelector('span').classList.remove('opacity-50');
    }

    completeStep(stepNumber) {
        const step = document.getElementById(`step-${stepNumber}`);
        step.querySelector('.loading-spinner').classList.add('hidden');
        step.querySelector(`#step-${stepNumber}-done`).classList.remove('hidden');
    }

    updateProgress(percentage, text) {
        this.progressBar.style.width = `${percentage}%`;
        this.progressText.textContent = text;
    }

    async generateLesson(topic, difficulty, count) {
        const response = await fetch(`/api/flashcard/?topic=${topic}&difficulty=${difficulty}&lesson_length=${count}`, {
            credentials: 'same-origin',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || '',
            }
        });
        
        if (!response.ok) throw new Error(`Failed to generate ${topic} lesson`);
        
        const data = await response.json();
        return data.flashcards || [];
    }

    async fetchUserProfile() {
        // Mock for now - replace with actual API call
        return {
            firstName: 'Alex',
            targetLanguage: 'Spanish',
            level: 'Beginner'
        };
    }

    async fetchStreakData() {
        // Mock for now - replace with actual API call
        return {
            currentStreak: 7,
            longestStreak: 15,
            lastStudyDate: new Date().toDateString()
        };
    }

    async calculateDailyGoals() {
        // Intelligent goal calculation based on user's progress
        const baseGoals = {
            newWords: 5,
            reviews: Math.floor(Math.random() * 10) + 5,
            estimatedMinutes: 12
        };

        // Adjust based on user's performance (mock logic)
        return baseGoals;
    }

    getCachedLessons() {
        const cached = localStorage.getItem('dailyLessons');
        if (!cached) return null;
        
        const data = JSON.parse(cached);
        // Check if cache is from today
        if (data.date === new Date().toDateString()) {
            return data.lessons;
        }
        return null;
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize and show modal when user logs in
const welcomeModal = new WelcomeModalManager();

// Show modal on page load (simulate login)
document.addEventListener('DOMContentLoaded', () => {
    // Only show if user just logged in (you can add logic here)
    const justLoggedIn = sessionStorage.getItem('justLoggedIn');
    if (justLoggedIn) {
        sessionStorage.removeItem('justLoggedIn');
        welcomeModal.show();
    }
});

// Listen for preloaded content
window.addEventListener('contentPreloaded', (event) => {
    console.log('Content preloaded:', event.detail);
    // Now your flashcard practice can use the preloaded content
    window.preloadedLessons = event.detail.todaysLessons;
});
</script>
