"""
Content Preloading Service
=========================

This service handles the intelligent preloading and caching of learning content.
It works with the AI service to generate content in advance, reducing wait times
and enabling seamless offline learning experiences.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
from django.utils import timezone
from django.core.cache import cache
from django.db import transaction
from django.contrib.auth.models import User

# Import AI services
try:
    from ai_services.llm_flashcards import (
        LLMFlashcardService,
        FlashcardRequest,
        DifficultyLevel as AIDifficultyLevel,
        ExerciseType,
    )

    AI_SERVICE_AVAILABLE = True
except ImportError:
    AI_SERVICE_AVAILABLE = False

from .models import (
    ContentItem,
    UserContentPerformance,
    UserLearningProfile,
    UserLessonQueue,
)

logger = logging.getLogger(__name__)


class ContentPreloadingService:
    """
    Main service for content preloading and adaptive learning
    """

    def __init__(self):
        self.ai_service = LLMFlashcardService() if AI_SERVICE_AVAILABLE else None
        self.cache_timeout = 60 * 60 * 24  # 24 hours

    def preload_user_content(
        self,
        user: User,
        content_types: List[str] = None,
        force_regenerate: bool = False,
    ) -> Dict:
        """
        Preload content for a specific user based on their learning profile
        """
        if content_types is None:
            content_types = ["flashcard", "lesson"]

        profile = self._get_or_create_user_profile(user)
        results = {}

        for content_type in content_types:
            try:
                content = self._preload_content_type(
                    user=user,
                    content_type=content_type,
                    profile=profile,
                    force_regenerate=force_regenerate,
                )
                results[content_type] = content
            except Exception as e:
                logger.error(
                    f"Failed to preload {content_type} for user {user.id}: {e}"
                )
                results[content_type] = {"error": str(e)}

        return results

    def preload_anonymous_content(
        self, language: str = "spanish", difficulty: str = "beginner"
    ) -> Dict:
        """
        Preload content for anonymous users
        """
        cache_key = f"anonymous_content_{language}_{difficulty}"
        cached_content = cache.get(cache_key)

        if cached_content:
            logger.info(
                f"Returning cached anonymous content for {language}/{difficulty}"
            )
            return cached_content

        try:
            # Generate content for anonymous user
            flashcards = self._generate_flashcard_lesson(
                language=language, difficulty=difficulty, lesson_length=10, user=None
            )

            content = {
                "flashcards": flashcards,
                "lessons": self._create_lesson_structure(
                    flashcards, language, difficulty
                ),
                "language": language,
                "difficulty": difficulty,
                "generated_at": timezone.now().isoformat(),
                "cache_key": cache_key,
            }

            # Cache for 4 hours for anonymous users
            cache.set(cache_key, content, timeout=60 * 60 * 4)

            return content

        except Exception as e:
            logger.error(f"Failed to generate anonymous content: {e}")
            return self._get_fallback_content(language, difficulty)

    def get_adaptive_content(self, user: User, session_context: Dict = None) -> Dict:
        """
        Get adaptive content based on user's learning patterns and current session
        """
        profile = self._get_or_create_user_profile(user)

        # Analyze recent performance
        recent_metrics = self._get_recent_performance(user, days=7)

        # Determine optimal difficulty and content type
        adaptive_params = self._calculate_adaptive_parameters(
            profile, recent_metrics, session_context
        )

        # Get or generate content based on adaptive parameters
        content = self._get_adaptive_content_from_cache_or_generate(
            user, adaptive_params
        )

        return {
            "content": content,
            "adaptive_params": adaptive_params,
            "recommendation_reason": self._explain_recommendation(adaptive_params),
            "user_profile_summary": self._summarize_user_profile(profile),
        }

    def queue_background_generation(self, user: User = None, priority: int = 5) -> str:
        """
        Queue content generation for background processing
        """
        if user:
            profile = self._get_or_create_user_profile(user)
            params = self._get_next_content_params(profile)
        else:
            # Default parameters for anonymous content
            params = {
                "language": "spanish",
                "difficulty": "beginner",
                "content_type": "flashcard",
                "quantity": 10,
            }

        # For now, we'll use a simple cache-based queue
        # TODO: Implement proper queue with Django-RQ
        queue_id = f"content_queue_{user.id if user else 'anonymous'}_{timezone.now().timestamp()}"
        cache.set(
            f"queue_{queue_id}",
            {
                "user_id": user.id if user else None,
                "content_type": params["content_type"],
                "language": params["language"],
                "difficulty": params["difficulty"],
                "topic": params.get("topic"),
                "quantity": params["quantity"],
                "priority": priority,
                "status": "pending",
                "created_at": timezone.now().isoformat(),
            },
            timeout=60 * 60 * 24,
        )  # 24 hours

        logger.info(f"Queued content generation: {queue_id}")
        return queue_id

    def process_generation_queue(self, max_items: int = 5) -> Dict:
        """
        Process pending items in the content generation queue
        """
        # For now, simulate queue processing
        # TODO: Implement proper queue processing with Django-RQ
        results = {
            "processed": 0,
            "errors": 0,
            "items": [],
            "message": "Queue processing simulated. Use Django-RQ for production.",
        }

        # Generate some content directly as an example
        try:
            # Create a few sample content items
            sample_content = [
                {
                    "type": "flashcard",
                    "question_text": f"Sample question {timezone.now().timestamp()}",
                    "answer_text": "Sample answer",
                    "choices_json": [
                        "Option 1",
                        "Option 2",
                        "Option 3",
                        "Sample answer",
                    ],
                    "explanation_text": "This is a sample flashcard for testing.",
                    "difficulty": 2,
                    "language": "spanish",
                    "tags": ["sample", "test"],
                }
            ]

            for content_data in sample_content:
                ContentItem.objects.create(**content_data)
                results["processed"] += 1
                results["items"].append(content_data["question_text"])

        except Exception as e:
            logger.error(f"Error processing queue: {e}")
            results["errors"] += 1

        return results

    def cleanup_expired_content(self) -> Dict:
        """
        Clean up expired content (simplified for current implementation)
        """
        # Clean up old ContentItems that haven't been used
        old_items = ContentItem.objects.filter(
            created_at__lt=timezone.now() - timedelta(days=30),
            total_attempts=0,  # Never been used
        )
        deleted_count = old_items.count()
        old_items.delete()

        return {
            "expired_count": 0,  # We don't have expiring content yet
            "deleted_count": deleted_count,
            "message": "Cleanup completed",
        }

    # Private helper methods

    def _get_or_create_user_profile(self, user: User) -> UserLearningProfile:
        """Get or create user learning profile"""
        profile, created = UserLearningProfile.objects.get_or_create(
            user=user,
            defaults={
                "preferred_difficulty": "beginner",
                "preferred_content_types": ["flashcard", "lesson"],
                "learning_pace": "moderate",
            },
        )

        if created:
            logger.info(f"Created new learning profile for user {user.id}")

        return profile

    def _preload_content_type(
        self,
        user: User,
        content_type: str,
        profile: UserLearningProfile,
        force_regenerate: bool,
    ) -> Dict:
        """Preload a specific type of content for a user"""

        # Check for existing valid content
        if not force_regenerate:
            existing_content = ContentItem.objects.filter(
                language=getattr(profile, "language", "spanish"),
                type=content_type,
                is_active=True,
            ).first()

            if existing_content:
                return {
                    "status": "cached",
                    "content_id": str(existing_content.id),
                    "data": {
                        "question": existing_content.question_text,
                        "answer": existing_content.answer_text,
                        "choices": existing_content.choices_json,
                        "explanation": existing_content.explanation_text,
                    },
                }

        # Generate new content
        if content_type == "flashcard":
            content_data = self._generate_flashcard_lesson(
                language=getattr(profile, "target_language", "spanish"),
                difficulty=profile.preferred_difficulty,
                lesson_length=10,
                user=user,
            )
        elif content_type == "lesson":
            content_data = self._generate_structured_lesson(
                language=getattr(profile, "target_language", "spanish"),
                difficulty=profile.preferred_difficulty,
                user=user,
            )
        else:
            raise ValueError(f"Unsupported content type: {content_type}")

        # Store content as ContentItem
        content_item = ContentItem.objects.create(
            type=content_type,
            language=getattr(profile, "language", "spanish"),
            difficulty=profile.preferred_difficulty,
            question_text=content_data.get("flashcards", [{}])[0].get(
                "question", "Sample question"
            ),
            answer_text=content_data.get("flashcards", [{}])[0].get(
                "correct_answer", "Sample answer"
            ),
            choices_json=content_data.get("flashcards", [{}])[0].get("options", []),
            explanation_text=content_data.get("flashcards", [{}])[0].get(
                "explanation", ""
            ),
            is_active=True,
        )

        return {
            "status": "generated",
            "content_id": str(content_item.id),
            "data": content_data,
        }

    def _generate_flashcard_lesson(
        self, language: str, difficulty: str, lesson_length: int, user: User = None
    ) -> List[Dict]:
        """Generate a lesson of flashcards using AI or fallback"""

        if not self.ai_service:
            return self._get_demo_flashcards(language, difficulty, lesson_length)

        try:
            # Create AI request
            flashcard_request = FlashcardRequest(
                language="english",
                target_language=language,
                difficulty=AIDifficultyLevel(difficulty.lower()),
                exercise_type=ExerciseType.multiple_choice,
                context=f"Generate diverse {difficulty} level flashcards for {language} learning",
            )

            # Generate using AI
            ai_responses = self.ai_service.generate_lesson(
                flashcard_request, lesson_length
            )

            # Convert to our format
            flashcards = []
            for i, resp in enumerate(ai_responses):
                flashcards.append(
                    {
                        "id": f"fc_{i}_{int(timezone.now().timestamp())}",
                        "question": resp.question,
                        "question_type": "multiple_choice",
                        "options": resp.options or [],
                        "correct_answer": resp.correct_answer,
                        "hint": resp.hint,
                        "explanation": resp.explanation,
                        "example_sentence": getattr(resp, "example_sentence", ""),
                        "pronunciation_guide": getattr(resp, "pronunciation_guide", ""),
                        "difficulty": difficulty,
                        "language": language,
                        "ai_generated": True,
                        "generated_at": timezone.now().isoformat(),
                    }
                )

            return flashcards

        except Exception as e:
            logger.warning(f"AI generation failed, using fallback: {e}")
            return self._get_demo_flashcards(language, difficulty, lesson_length)

    def _get_demo_flashcards(
        self, language: str, difficulty: str, count: int
    ) -> List[Dict]:
        """Generate demo flashcards as fallback"""
        demo_cards = [
            {
                "id": f"demo_1_{int(timezone.now().timestamp())}",
                "question": f'How do you say "Hello" in {language.title()}?',
                "question_type": "multiple_choice",
                "options": ["Hola", "Adiós", "Gracias", "Por favor"],
                "correct_answer": "Hola",
                "hint": "It's a common greeting.",
                "explanation": "Hola is the standard greeting in Spanish.",
                "example_sentence": "Hola, ¿cómo estás?",
                "pronunciation_guide": "OH-lah",
                "difficulty": difficulty,
                "language": language,
                "ai_generated": False,
            },
            # Add more demo cards...
        ]

        # Repeat and modify demo cards to reach desired count
        result = []
        for i in range(count):
            card = demo_cards[i % len(demo_cards)].copy()
            card["id"] = f"demo_{i}_{int(timezone.now().timestamp())}"
            result.append(card)

        return result

    def _create_lesson_structure(
        self, flashcards: List[Dict], language: str, difficulty: str
    ) -> List[Dict]:
        """Create structured lessons from flashcards"""
        lessons = []

        # Group flashcards into lessons
        cards_per_lesson = 5
        for i in range(0, len(flashcards), cards_per_lesson):
            lesson_cards = flashcards[i : i + cards_per_lesson]

            lessons.append(
                {
                    "id": f"lesson_{i // cards_per_lesson + 1}",
                    "title": f"{difficulty.title()} {language.title()} - Part {i // cards_per_lesson + 1}",
                    "description": f"Practice {len(lesson_cards)} {language} concepts",
                    "difficulty": difficulty,
                    "language": language,
                    "flashcards": lesson_cards,
                    "estimated_duration": f"{len(lesson_cards) * 2} minutes",
                    "topics": self._extract_topics_from_cards(lesson_cards),
                }
            )

        return lessons

    def _extract_topics_from_cards(self, cards: List[Dict]) -> List[str]:
        """Extract learning topics from flashcards"""
        # Simple topic extraction - could be enhanced with NLP
        topics = set()
        for card in cards:
            if "greeting" in card.get("question", "").lower():
                topics.add("Greetings")
            elif "number" in card.get("question", "").lower():
                topics.add("Numbers")
            # Add more topic detection logic

        return list(topics) if topics else ["General Vocabulary"]

    def _get_fallback_content(self, language: str, difficulty: str) -> Dict:
        """Get fallback content when generation fails"""
        return {
            "flashcards": self._get_demo_flashcards(language, difficulty, 5),
            "lessons": [],
            "language": language,
            "difficulty": difficulty,
            "generated_at": timezone.now().isoformat(),
            "is_fallback": True,
            "message": "Using demo content. Full content generation temporarily unavailable.",
        }

    def _get_recent_performance(
        self, user: User, days: int = 7
    ) -> List["UserContentPerformance"]:
        """Get recent performance metrics for adaptive learning"""
        since_date = timezone.now() - timedelta(days=days)
        from .models import UserContentPerformance

        return UserContentPerformance.objects.filter(
            user=user, updated_at__gte=since_date
        ).order_by("-updated_at")

    def _calculate_adaptive_parameters(
        self,
        profile: UserLearningProfile,
        recent_metrics: List["UserContentPerformance"],
        session_context: Dict = None,
    ) -> Dict:
        """Calculate optimal parameters for adaptive content"""
        # Default parameters
        params = {
            "difficulty": profile.preferred_difficulty,
            "content_types": profile.preferred_content_types,
            "pace": profile.learning_pace,
            "focus_areas": [],
        }

        if recent_metrics:
            avg_accuracy = sum(m.accuracy_rate for m in recent_metrics) / len(
                recent_metrics
            )

            # Get difficulty levels from ContentItem model
            from .models import ContentItem

            difficulty_choices = dict(ContentItem.DIFFICULTY_LEVELS)
            difficulty_values = list(difficulty_choices.keys())

            # Get current difficulty as integer
            current_difficulty = getattr(profile, "preferred_difficulty", 1)
            if isinstance(current_difficulty, str):
                # Convert string to integer if needed
                reverse_difficulty = {
                    v.lower(): k for k, v in difficulty_choices.items()
                }
                current_difficulty = reverse_difficulty.get(
                    current_difficulty.lower(), 1
                )

            # Adjust difficulty based on performance
            if avg_accuracy > 0.8:
                # User is doing well, increase difficulty
                if current_difficulty < max(difficulty_values):
                    params["difficulty"] = current_difficulty + 1
                    params["focus_areas"].append("challenge")
            elif avg_accuracy < 0.6:
                # User struggling, reduce difficulty
                if current_difficulty > min(difficulty_values):
                    params["difficulty"] = current_difficulty - 1
                    params["focus_areas"].append("reinforcement")

        return params

    def _calculate_content_relevance(
        self, content_data: Dict, profile: UserLearningProfile
    ) -> float:
        """Calculate how relevant content is to user's learning needs"""
        # Simple relevance scoring - could be enhanced with ML
        score = 0.5  # Base score

        # Adjust based on profile preferences
        if isinstance(content_data, list) and content_data:
            first_item = content_data[0]
            if first_item.get("difficulty") == profile.preferred_difficulty:
                score += 0.2
            if first_item.get("question_type") in profile.preferred_content_types:
                score += 0.2

        return min(1.0, score)

    def _get_adaptive_content_from_cache_or_generate(
        self, user: User, adaptive_params: Dict
    ) -> List[Dict]:
        """Get adaptive content from cache or generate new content"""
        try:
            # Try to get from cache first
            content_items = ContentItem.objects.filter(
                content_type="flashcard",
                difficulty=adaptive_params.get("difficulty", 1),
                language=adaptive_params.get("language", "spanish"),
                created_at__gte=timezone.now() - timedelta(hours=24),
            )[: adaptive_params.get("limit", 5)]

            if content_items.exists():
                return [
                    {
                        "id": item.id,
                        "question": item.question_text,
                        "answer": item.answer_text,
                        "type": item.type,
                        "options": item.choices_list if item.type == "mcq" else None,
                        "difficulty": item.difficulty,
                        "cached": True,
                    }
                    for item in content_items
                ]

            # Generate new content if cache is empty
            return self._generate_adaptive_content(user, adaptive_params)

        except Exception as e:
            logger.error(f"Error getting adaptive content: {e}")
            return []

    def _generate_adaptive_content(
        self, user: User, adaptive_params: Dict
    ) -> List[Dict]:
        """Generate new adaptive content"""
        try:
            # Use the LLM service to generate content
            from ai_services.llm_flashcards import (
                FlashcardService,
                FlashcardRequest,
                DifficultyLevel,
                ExerciseType,
            )

            llm_service = FlashcardService()

            request = FlashcardRequest(
                language="english",
                target_language=adaptive_params.get("language", "spanish"),
                difficulty=DifficultyLevel(
                    adaptive_params.get("difficulty_name", "beginner")
                ),
                exercise_type=ExerciseType(
                    adaptive_params.get("exercise_type", "multiple_choice")
                ),
                context=f"Adaptive content for user level: {adaptive_params.get('difficulty_name', 'beginner')}",
            )

            responses = llm_service.generate_lesson(
                request, lesson_length=adaptive_params.get("limit", 5)
            )

            return [
                {
                    "id": f"generated-{i}",
                    "question": resp.question,
                    "answer": resp.correct_answer,
                    "type": (
                        resp.question_type
                        if hasattr(resp, "question_type")
                        else "multiple_choice"
                    ),
                    "options": resp.options if hasattr(resp, "options") else [],
                    "difficulty": adaptive_params.get("difficulty", 1),
                    "cached": False,
                }
                for i, resp in enumerate(responses)
            ]

        except Exception as e:
            logger.error(f"Error generating adaptive content: {e}")
            return []

    def _explain_recommendation(self, adaptive_params: Dict) -> str:
        """Explain why this content was recommended"""
        if adaptive_params.get("difficulty_adjustment"):
            return f"Adjusted to {adaptive_params['difficulty']} difficulty based on your recent performance"
        elif adaptive_params.get("review_mode"):
            return "Review content you've struggled with previously"
        elif adaptive_params.get("new_content"):
            return "New content to expand your knowledge"
        else:
            return "Personalized content based on your learning progress"

    def _summarize_user_profile(self, profile) -> Dict:
        """Summarize user profile for adaptive content"""
        if not profile:
            return {
                "total_xp": 0,
                "preferred_difficulty": "beginner",
                "learning_pace": "moderate",
                "strengths": [],
                "weaknesses": [],
            }

        return {
            "total_xp": getattr(profile, "total_xp", 0),
            "preferred_difficulty": getattr(profile, "preferred_difficulty", 1),
            "learning_pace": getattr(profile, "learning_pace", "moderate"),
            "strengths": getattr(profile, "strengths", []),
            "weaknesses": getattr(profile, "weaknesses", []),
            "total_sessions": getattr(profile, "total_sessions", 0),
            "average_accuracy": getattr(profile, "average_accuracy", 0.0),
        }


# Singleton instance
content_preloader = ContentPreloadingService()
