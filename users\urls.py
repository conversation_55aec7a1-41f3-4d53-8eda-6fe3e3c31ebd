from django.urls import path
from rest_framework.routers import DefaultRouter
from .views import (
    UserViewSet,
    RegisterView,
    CustomAuthToken,
    EmailConfirmationView,
    PasswordResetRequestView,
    PasswordResetConfirmView,
)

router = DefaultRouter()
router.register(r"users", UserViewSet)

urlpatterns = [
    path("register/", RegisterView.as_view(), name="register"),
    path("login/", CustomAuthToken.as_view(), name="login"),
    path(
        "confirm-email/<uidb64>/<token>/",
        EmailConfirmationView.as_view(),
        name="confirm-email",
    ),
    path("reset-password/", PasswordResetRequestView.as_view(), name="reset-password"),
    path(
        "reset-password-confirm/<uidb64>/<token>/",
        PasswordResetConfirmView.as_view(),
        name="reset-password-confirm",
    ),
]
urlpatterns += router.urls
