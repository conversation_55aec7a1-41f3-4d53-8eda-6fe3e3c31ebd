"""
Background Tasks for TalonTalk Content Generation
Uses Django-RQ for asynchronous content processing
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from django.utils import timezone
from django.conf import settings
from django_rq import job
import django_rq

from lessons.models import (
    ContentItem,
    UserLessonQueue,
    UserLearningProfile,
    UserContentPerformance,
)
from lessons.services.content_generator import ContentGeneratorService
from lessons.services.spaced_repetition import SpacedRepetitionService

logger = logging.getLogger(__name__)


class BackgroundTaskService:
    """
    Manages background tasks for content generation and queue management
    """

    def __init__(self):
        self.content_generator = ContentGeneratorService()
        self.spaced_repetition = SpacedRepetitionService()
        self.logger = logging.getLogger(__name__)

    def queue_content_generation(
        self, user_id: int, language: str = "spanish", priority: str = "normal"
    ) -> str:
        """
        Queue background content generation for a user

        Args:
            user_id: User ID
            language: Target language
            priority: Task priority ('high', 'normal', 'low')

        Returns:
            Job ID for tracking
        """
        try:
            # Get appropriate queue based on priority
            queue_name = f"content_{priority}"
            queue = django_rq.get_queue(queue_name)

            # Queue the content generation job
            job = queue.enqueue(
                generate_user_content_batch,
                user_id=user_id,
                language=language,
                job_timeout="10m",
                description=f"Generate content for user {user_id}",
            )

            self.logger.info(
                f"Queued content generation job {job.id} for user {user_id}"
            )
            return job.id

        except Exception as e:
            self.logger.error(f"Error queuing content generation: {e}")
            return None

    def queue_daily_content_prep(self) -> List[str]:
        """
        Queue daily content preparation for all active users

        Returns:
            List of job IDs
        """
        try:
            # Get users who need daily content preparation
            active_users = UserLearningProfile.objects.filter(
                is_active=True, last_activity__gte=timezone.now() - timedelta(days=7)
            ).values_list("user_id", flat=True)

            job_ids = []
            queue = django_rq.get_queue("content_normal")

            for user_id in active_users:
                job = queue.enqueue(
                    prepare_daily_content,
                    user_id=user_id,
                    job_timeout="5m",
                    description=f"Daily content prep for user {user_id}",
                )
                job_ids.append(job.id)

            self.logger.info(
                f"Queued daily content preparation for {len(job_ids)} users"
            )
            return job_ids

        except Exception as e:
            self.logger.error(f"Error queuing daily content prep: {e}")
            return []

    def queue_spaced_repetition_update(
        self,
        user_id: int,
        content_item_id: int,
        quality_score: int,
        response_time_ms: Optional[int] = None,
    ) -> str:
        """
        Queue spaced repetition algorithm update

        Args:
            user_id: User ID
            content_item_id: ContentItem ID
            quality_score: Quality of response (0-5)
            response_time_ms: Response time in milliseconds

        Returns:
            Job ID
        """
        try:
            queue = django_rq.get_queue(
                "content_high"
            )  # High priority for user feedback

            job = queue.enqueue(
                update_spaced_repetition,
                user_id=user_id,
                content_item_id=content_item_id,
                quality_score=quality_score,
                response_time_ms=response_time_ms,
                job_timeout="2m",
                description=f"Update spaced repetition for user {user_id}",
            )

            return job.id

        except Exception as e:
            self.logger.error(f"Error queuing spaced repetition update: {e}")
            return None


# ============================================================================
# Background Task Functions (executed by RQ workers)
# ============================================================================


@job("content_normal", timeout="10m")
def generate_user_content_batch(user_id: int, language: str = "spanish"):
    """
    Background task to generate a batch of content for a specific user
    """
    try:
        from django.db import models

        logger.info(f"Starting content generation for user {user_id}")

        # Get user's learning profile
        profile, created = UserLearningProfile.objects.get_or_create(
            user_id=user_id, defaults={"target_language": language}
        )

        # Determine appropriate difficulty level
        difficulty = profile.current_difficulty_level or 1

        # Determine content types needed
        performance_data = (
            UserContentPerformance.objects.filter(user_id=user_id)
            .values("content_item__type")
            .annotate(count=models.Count("id"))
        )

        # Balance content types
        content_types = ["flashcard", "mcq", "translation"]
        if difficulty >= 3:
            content_types.extend(["grammar", "listening"])

        # Generate content batch
        content_generator = ContentGeneratorService()
        result = content_generator.generate_care_content_batch(
            language=language,
            difficulty_level=difficulty,
            content_types=content_types,
            batch_size=10,
        )

        if result.get("success"):
            # Update user's content queue
            update_user_content_queue(user_id, result["content_items"])
            logger.info(
                f"Generated {len(result['content_items'])} items for user {user_id}"
            )
        else:
            logger.error(
                f"Content generation failed for user {user_id}: {result.get('errors')}"
            )

    except Exception as e:
        logger.error(f"Error in generate_user_content_batch for user {user_id}: {e}")
        raise


@job("content_normal", timeout="5m")
def prepare_daily_content(user_id: int):
    """
    Background task to prepare daily content for a user
    """
    try:
        logger.info(f"Preparing daily content for user {user_id}")

        # Get content due for review
        spaced_repetition = SpacedRepetitionService()
        due_content = spaced_repetition.get_due_content(user_id, limit=15)

        # Get some new content to mix in
        seen_content_ids = UserContentPerformance.objects.filter(
            user_id=user_id
        ).values_list("content_item_id", flat=True)

        new_content = (
            ContentItem.objects.filter(
                language="spanish", is_active=True  # TODO: Get from user profile
            )
            .exclude(id__in=seen_content_ids)
            .order_by("?")[:5]
        )

        # Combine and shuffle
        daily_content = list(due_content) + list(new_content)

        # Update or create daily queue
        queue, created = UserLessonQueue.objects.update_or_create(
            user_id=user_id,
            queue_type="daily",
            defaults={
                "ordered_content_ids": [item.id for item in daily_content],
                "status": "active",
                "expires_at": timezone.now() + timedelta(days=1),
            },
        )

        logger.info(
            f"Prepared daily content queue with {len(daily_content)} items for user {user_id}"
        )

    except Exception as e:
        logger.error(f"Error preparing daily content for user {user_id}: {e}")
        raise


@job("content_high", timeout="2m")
def update_spaced_repetition(
    user_id: int,
    content_item_id: int,
    quality_score: int,
    response_time_ms: Optional[int] = None,
):
    """
    Background task to update spaced repetition algorithm
    """
    try:
        content_item = ContentItem.objects.get(id=content_item_id)

        spaced_repetition = SpacedRepetitionService()
        next_review, easiness = spaced_repetition.calculate_next_review(
            user_id=user_id,
            content_item=content_item,
            quality=quality_score,
            response_time_ms=response_time_ms,
        )

        logger.info(
            f"Updated spaced repetition: user {user_id}, content {content_item_id}, "
            f"next review: {next_review.strftime('%Y-%m-%d')}"
        )

    except ContentItem.DoesNotExist:
        logger.error(f"ContentItem {content_item_id} not found")
    except Exception as e:
        logger.error(f"Error updating spaced repetition: {e}")
        raise


@job("content_low", timeout="30m")
def cleanup_expired_content():
    """
    Background task to clean up expired content and optimize database
    """
    try:
        logger.info("Starting content cleanup")

        # Clean up expired queues
        expired_queues = UserLessonQueue.objects.filter(expires_at__lt=timezone.now())
        expired_count = expired_queues.count()
        expired_queues.delete()

        # Archive old performance records (keep last 6 months)
        old_performances = UserContentPerformance.objects.filter(
            last_reviewed__lt=timezone.now() - timedelta(days=180),
            repetition_count=0,  # Only archive items not in active learning
        )
        archived_count = old_performances.count()
        # old_performances.delete()  # Uncomment when ready to actually delete

        # Update content item statistics
        content_items = ContentItem.objects.filter(is_active=True)
        for item in content_items:
            # Recalculate aggregated stats
            performances = UserContentPerformance.objects.filter(content_item=item)
            if performances.exists():
                total_attempts = sum(p.attempts for p in performances)
                total_correct = sum(p.correct_attempts for p in performances)

                item.total_attempts = total_attempts
                item.total_correct = total_correct
                item.save(update_fields=["total_attempts", "total_correct"])

        logger.info(
            f"Cleanup complete: {expired_count} expired queues, "
            f"{archived_count} old performances, updated {content_items.count()} items"
        )

    except Exception as e:
        logger.error(f"Error in cleanup_expired_content: {e}")
        raise


def update_user_content_queue(user_id: int, content_items: List[ContentItem]):
    """
    Helper function to update user's content queue with new items
    """
    try:
        # Get or create the user's main learning queue
        queue, created = UserLessonQueue.objects.get_or_create(
            user_id=user_id,
            queue_type="new_content",
            defaults={
                "ordered_content_ids": [],
                "status": "active",
                "expires_at": timezone.now() + timedelta(days=7),
            },
        )

        # Add new content IDs to the queue
        new_ids = [item.id for item in content_items]
        existing_ids = queue.ordered_content_ids or []

        # Merge, removing duplicates while preserving order
        updated_ids = existing_ids + [id for id in new_ids if id not in existing_ids]

        queue.ordered_content_ids = updated_ids
        queue.save()

        logger.info(
            f"Updated content queue for user {user_id} with {len(new_ids)} new items"
        )

    except Exception as e:
        logger.error(f"Error updating user content queue: {e}")


# Initialize the service
background_tasks = BackgroundTaskService()
