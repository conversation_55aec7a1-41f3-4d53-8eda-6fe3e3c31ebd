<!-- Contextualize Phase: Set the Scene -->
<div class="care-phase-content contextualize-phase" data-phase="contextualize">
    <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl p-8 text-white mb-8">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <i class="fas fa-globe-americas text-3xl"></i>
            </div>
            <div>
                <h2 class="text-3xl font-bold">Contextualize</h2>
                <p class="text-emerald-100">Let's set the scene for your learning journey</p>
            </div>
        </div>
        <p class="text-lg text-emerald-50">{{ lesson.description }}</p>
    </div>

    <!-- Context Setting Content -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Real-World Scenario -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-map-marker-alt text-emerald-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">Real-World Scenario</h3>
            </div>
            <div id="contextScenario" class="space-y-4">
                <!-- Dynamic content will be loaded here -->
                <div class="bg-emerald-50 p-4 rounded-lg">
                    <p class="text-gray-800 italic">"Imagine you're visiting a café in Madrid for the first time. You want to order coffee and a pastry, but you're not sure how to communicate with the barista..."</p>
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-600">
                    <i class="fas fa-lightbulb"></i>
                    <span>This scenario will guide today's lesson</span>
                </div>
            </div>
        </div>

        <!-- Cultural Context -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-users text-emerald-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">Cultural Context</h3>
            </div>
            <div id="culturalContext" class="space-y-4">
                <!-- Dynamic content will be loaded here -->
                <div class="space-y-3">
                    <div class="flex items-start gap-3">
                        <i class="fas fa-coffee text-amber-500 mt-1"></i>
                        <div>
                            <p class="font-medium text-gray-900">Café Culture in Spain</p>
                            <p class="text-sm text-gray-600">Coffee is typically enjoyed standing at the bar, and locals often order specific types like "café con leche" or "cortado".</p>
                        </div>
                    </div>
                    <div class="flex items-start gap-3">
                        <i class="fas fa-clock text-blue-500 mt-1"></i>
                        <div>
                            <p class="font-medium text-gray-900">Timing Matters</p>
                            <p class="text-sm text-gray-600">Spaniards often have coffee and pastries in the morning or afternoon, not typically after meals.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Learning Objectives -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-target text-emerald-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">What You'll Learn Today</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-3">
                <div class="flex items-center gap-3">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span class="text-gray-800">Basic café vocabulary (10+ words)</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span class="text-gray-800">Polite ordering phrases</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span class="text-gray-800">Numbers for prices (1-100)</span>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex items-center gap-3">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span class="text-gray-800">Payment expressions</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span class="text-gray-800">Common café interactions</span>
                </div>
                <div class="flex items-center gap-3">
                    <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span class="text-gray-800">Cultural etiquette tips</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Context Builder -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-puzzle-piece text-emerald-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">Build Your Context</h3>
        </div>
        <p class="text-gray-600 mb-6">Before we dive in, let's personalize this scenario for you!</p>
        
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">What time of day are you visiting the café?</label>
                <div class="grid grid-cols-3 gap-3">
                    <button class="context-option p-3 border border-gray-200 rounded-lg hover:border-emerald-500 hover:bg-emerald-50 transition-colors" data-option="morning">
                        <i class="fas fa-sun text-amber-500 mb-2"></i>
                        <div class="text-sm font-medium">Morning</div>
                        <div class="text-xs text-gray-500">8-11 AM</div>
                    </button>
                    <button class="context-option p-3 border border-gray-200 rounded-lg hover:border-emerald-500 hover:bg-emerald-50 transition-colors" data-option="afternoon">
                        <i class="fas fa-coffee text-amber-600 mb-2"></i>
                        <div class="text-sm font-medium">Afternoon</div>
                        <div class="text-xs text-gray-500">2-5 PM</div>
                    </button>
                    <button class="context-option p-3 border border-gray-200 rounded-lg hover:border-emerald-500 hover:bg-emerald-50 transition-colors" data-option="evening">
                        <i class="fas fa-moon text-indigo-500 mb-2"></i>
                        <div class="text-sm font-medium">Evening</div>
                        <div class="text-xs text-gray-500">6-8 PM</div>
                    </button>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">What's your main goal?</label>
                <div class="grid grid-cols-2 gap-3">
                    <button class="context-option p-3 border border-gray-200 rounded-lg hover:border-emerald-500 hover:bg-emerald-50 transition-colors text-left" data-option="quick-coffee">
                        <i class="fas fa-bolt text-yellow-500 mr-2"></i>
                        <span class="font-medium">Quick coffee to go</span>
                    </button>
                    <button class="context-option p-3 border border-gray-200 rounded-lg hover:border-emerald-500 hover:bg-emerald-50 transition-colors text-left" data-option="relaxed-visit">
                        <i class="fas fa-chair text-blue-500 mr-2"></i>
                        <span class="font-medium">Relaxed sit-down experience</span>
                    </button>
                </div>
            </div>
        </div>

        <div id="contextSummary" class="mt-6 p-4 bg-emerald-50 rounded-lg hidden">
            <div class="flex items-center gap-2 mb-2">
                <i class="fas fa-check-circle text-emerald-600"></i>
                <span class="font-medium text-emerald-800">Your Personal Scenario:</span>
            </div>
            <p id="contextSummaryText" class="text-emerald-700"></p>
        </div>
    </div>
</div>

<script>
// Contextualize phase interactivity
document.addEventListener('DOMContentLoaded', function() {
    const contextOptions = document.querySelectorAll('.context-option');
    const contextSummary = document.getElementById('contextSummary');
    const contextSummaryText = document.getElementById('contextSummaryText');
    
    let selectedContext = {
        time: null,
        goal: null
    };

    contextOptions.forEach(option => {
        option.addEventListener('click', function() {
            const optionType = this.closest('div').previousElementSibling.textContent.includes('time') ? 'time' : 'goal';
            const optionValue = this.dataset.option;
            
            // Remove active state from siblings
            this.parentElement.querySelectorAll('.context-option').forEach(btn => {
                btn.classList.remove('border-emerald-500', 'bg-emerald-50');
            });
            
            // Add active state
            this.classList.add('border-emerald-500', 'bg-emerald-50');
            
            // Update selected context
            selectedContext[optionType] = optionValue;
            
            // Update summary if both options selected
            if (selectedContext.time && selectedContext.goal) {
                updateContextSummary();
            }
        });
    });

    function updateContextSummary() {
        const timeMap = {
            'morning': 'a busy morning',
            'afternoon': 'a relaxing afternoon',
            'evening': 'a quiet evening'
        };
        
        const goalMap = {
            'quick-coffee': 'grab a quick coffee to go',
            'relaxed-visit': 'enjoy a leisurely coffee break'
        };
        
        const summaryText = `You're visiting a Madrid café during ${timeMap[selectedContext.time]} to ${goalMap[selectedContext.goal]}. Perfect! Let's learn the Spanish you'll need for this exact situation.`;
        
        contextSummaryText.textContent = summaryText;
        contextSummary.classList.remove('hidden');
        
        // Enable next phase button
        const nextBtn = document.getElementById('nextPhaseBtn');
        if (nextBtn) {
            nextBtn.disabled = false;
            nextBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
});
</script>
