/**
 * Welcome Modal Integration - TypeScript Edition
 * Handles automatic display of welcome modal on login and daily visits
 */
interface WelcomeModalConfig {
    showOnMount?: boolean;
    autoShow?: boolean;
    checkDaily?: boolean;
    steps?: WelcomeStep[];
    onComplete?: () => void;
    onSkip?: () => void;
}
interface WelcomeStep {
    id: string;
    title: string;
    content: string;
    action?: () => Promise<void>;
    skippable: boolean;
    duration?: number;
}
interface UserWelcomeState {
    hasSeenWelcome: boolean;
    lastVisit: string | null;
    completedSteps: string[];
    preferences: {
        skipWelcome: boolean;
        showDaily: boolean;
    };
}
declare class WelcomeModalIntegration {
    private config;
    private userState;
    private currentStep;
    private modal;
    constructor(config?: WelcomeModalConfig);
    private init;
    private loadModal;
    private createModal;
    private generateModalHTML;
    private setupEventListeners;
    private checkShouldShowModal;
    showModal(): void;
    hideModal(): void;
    private renderCurrentStep;
    private getDefaultSteps;
    private nextStep;
    private prevStep;
    private skipWelcome;
    private completeWelcome;
    private loadUserState;
    private saveUserState;
    show(): void;
    hide(): void;
    reset(): void;
    getUserState(): UserWelcomeState;
}
declare global {
    interface Window {
        WelcomeModalIntegration: typeof WelcomeModalIntegration;
        welcomeModal?: WelcomeModalIntegration;
    }
}
export default WelcomeModalIntegration;
//# sourceMappingURL=welcome-modal-integration.d.ts.map