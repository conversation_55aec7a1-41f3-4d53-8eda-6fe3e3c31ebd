/**
 * C.A.R.E. Framework Interactive Lesson System - TypeScript Edition
 * Handles phase navigation, progress tracking, and AI tutor interaction with full type safety
 */
import type { EventCallback } from './types/index.js';
interface CARELessonManagerConfig {
    lessonId: string;
    initialPhase?: string;
    onPhaseChange?: EventCallback<string>;
    onLessonComplete?: EventCallback<void>;
    debugMode?: boolean;
}
declare class CARELessonManager {
    private currentPhase;
    private phaseProgress;
    private readonly totalPhases;
    private readonly phases;
    private readonly lessonId;
    private debugMode;
    private eventCallbacks;
    constructor(config: CARELessonManagerConfig);
    private init;
    private setupEventListeners;
    private setupAITutorListeners;
    loadPhase(phaseName: string): void;
    private loadPhaseContent;
    private renderPhaseContent;
    private generatePhaseHTML;
    private generateContextualizeHTML;
    private generateAcquireHTML;
    private generateReinforceHTML;
    private generateExtendHTML;
    private showFallbackContent;
    private setupPhaseSpecificListeners;
    private setupReinforceListeners;
    private setupExtendListeners;
    private handlePracticeAnswer;
    private handleTranslationCheck;
    private handlePronunciation;
    nextPhase(): void;
    private completeLesson;
    private updatePhaseIndicators;
    private updateProgressBar;
    private openAITutor;
    private closeAITutor;
    private sendTutorMessage;
    private generateTutorResponse;
    on<T>(event: string, callback: EventCallback<T>): void;
    off<T>(event: string, callback: EventCallback<T>): void;
    emit<T>(event: string, data: T): void;
    private getCSRFToken;
    private log;
    getCurrentPhase(): string;
    getProgress(): number;
    getTotalPhases(): number;
}
declare global {
    interface Window {
        careManager?: CARELessonManager;
        CARELessonManager: typeof CARELessonManager;
    }
}
export default CARELessonManager;
//# sourceMappingURL=care-lesson.d.ts.map