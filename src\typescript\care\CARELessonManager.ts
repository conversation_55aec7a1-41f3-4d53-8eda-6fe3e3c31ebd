/**
 * C.A.R.E. Framework Interactive Lesson System - TypeScript Implementation
 * Handles phase navigation, progress tracking, and AI tutor interaction
 */

import type {
    PhaseType,
    CARELessonState,
    AITutorState,
    CAREConfig,
    APIResponse,
    PhaseContent,
    ContextualizeContent,
    AcquireContent,
    ReinforceContent,
    ExtendContent,
    CAREEvent,
    Exercise,
    EventHandler
} from '../types/care.types.js';

import {
    CAREError,
    APIError
} from '../types/care.types.js';

import type {
    RequestConfig,
    CSRFToken,
    Logger,
    EventBus,
    Animator
} from '../types/common.types.js';

/**
 * Main C.A.R.E. Lesson Manager Class
 */
export class CARELessonManager {
    private state: CARELessonState;
    private config: CAREConfig;
    private eventBus: EventBus;
    private logger: Logger;
    private animator: Animator;
    private aiTutorState: AITutorState;

    constructor(config: Partial<CAREConfig> = {}) {
        this.config = this.mergeConfig(config);
        this.state = this.initializeState();
        this.aiTutorState = this.initializeAITutorState();

        // Initialize dependencies
        this.eventBus = this.createEventBus();
        this.logger = this.createLogger();
        this.animator = this.createAnimator();

        this.init();
    }

    /**
     * Initialize the lesson manager
     */
    private async init(): Promise<void> {
        try {
            this.logger.info('Initializing C.A.R.E. Lesson Manager');

            await this.setupEventListeners();
            await this.loadPhase(this.state.currentPhase);
            this.updateProgressBar();

            this.logger.info('C.A.R.E. Lesson Manager initialized successfully');
        } catch (error) {
            this.logger.error('Failed to initialize C.A.R.E. Lesson Manager', error as Error);
            throw new CAREError(
                'Failed to initialize lesson manager',
                'INIT_ERROR',
                undefined,
                error
            );
        }
    }

    /**
     * Set up event listeners for navigation and interactions
     */
    private async setupEventListeners(): Promise<void> {
        // Phase navigation buttons
        const navItems = document.querySelectorAll<HTMLElement>('.care-nav-item');
        navItems.forEach(btn => {
            btn.addEventListener('click', this.handlePhaseNavigation.bind(this));
        });

        // Phase indicators (also clickable)
        const phaseIndicators = document.querySelectorAll<HTMLElement>('.care-phase-indicator');
        phaseIndicators.forEach(btn => {
            btn.addEventListener('click', this.handlePhaseNavigation.bind(this));
        });

        // AI Tutor
        this.setupAITutorListeners();

        this.logger.debug('Event listeners set up successfully');
    }

    /**
     * Handle phase navigation events
     */
    private handlePhaseNavigation(event: Event): void {
        const target = event.target as HTMLElement;
        const phase = target.dataset.phase || target.closest('.care-nav-item, .care-phase-indicator')?.getAttribute('data-phase');

        if (phase && this.isValidPhase(phase)) {
            this.loadPhase(phase as PhaseType);
        }
    }

    /**
     * Load a specific phase
     */
    public async loadPhase(phaseName: PhaseType): Promise<void> {
        try {
            this.logger.info(`Loading phase: ${phaseName}`);
            this.setState({ isLoading: true, error: '' });

            // Hide all phases with animation
            await this.hideAllPhases();

            // Show target phase
            await this.showPhase(phaseName);

            // Update UI state
            this.updatePhaseIndicators(phaseName);
            this.updateState(phaseName);

            // Load phase-specific content
            await this.loadPhaseContent(phaseName);

            // Emit phase navigation event
            this.emitEvent({
                type: 'phase_navigation',
                from_phase: this.state.currentPhase,
                to_phase: phaseName,
                timestamp: new Date()
            });

            this.setState({ isLoading: false });
            this.logger.info(`Phase ${phaseName} loaded successfully`);
        } catch (error) {
            this.setState({ isLoading: false, error: `Failed to load ${phaseName} phase` });
            this.logger.error(`Failed to load phase ${phaseName}`, error as Error);
            throw new CAREError(
                `Failed to load phase: ${phaseName}`,
                'PHASE_LOAD_ERROR',
                phaseName,
                error
            );
        }
    }

    /**
     * Load phase-specific content from API
     */
    private async loadPhaseContent(phaseName: PhaseType): Promise<void> {
        try {
            this.logger.debug(`Loading content for phase: ${phaseName}`);

            const response = await this.apiRequest<PhaseContent>(`/care/api/phase/${phaseName}/`);

            if (response.success && response.content) {
                this.renderPhaseContent(phaseName, response.content);
            } else {
                throw new APIError(
                    response.error || 'Failed to load phase content',
                    500,
                    `/care/api/phase/${phaseName}/`
                );
            }
        } catch (error) {
            this.logger.error(`Error loading ${phaseName} content`, error as Error);
            this.showFallbackContent(phaseName);
        }
    }

    /**
     * Render phase content in the UI
     */
    private renderPhaseContent(phaseName: PhaseType, content: PhaseContent): void {
        this.logger.debug(`Rendering ${phaseName} with content`, content);

        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (!phaseContainer) {
            throw new CAREError(
                `Phase container not found: ${phaseName}Phase`,
                'DOM_ERROR',
                phaseName
            );
        }

        const contentHTML = this.generatePhaseHTML(phaseName, content);
        phaseContainer.innerHTML = contentHTML;

        // Set up phase-specific event listeners
        this.setupPhaseSpecificListeners(phaseName);

        this.logger.debug(`Phase ${phaseName} rendered successfully`);
    }

    /**
     * Generate HTML for specific phase content
     */
    private generatePhaseHTML(phaseName: PhaseType, content: PhaseContent): string {
        switch (phaseName) {
            case 'contextualize':
                return this.generateContextualizeHTML(content as ContextualizeContent);
            case 'acquire':
                return this.generateAcquireHTML(content as AcquireContent);
            case 'reinforce':
                return this.generateReinforceHTML(content as ReinforceContent);
            case 'extend':
                return this.generateExtendHTML(content as ExtendContent);
            default:
                return '<p class="text-gray-500">Phase content loading...</p>';
        }
    }

    /**
     * Generate HTML for Contextualize phase
     */
    private generateContextualizeHTML(content: ContextualizeContent): string {
        const scenario = content.scenario || {};
        const culturalContext = content.cultural_context || {};
        const keyPhrases = content.key_phrases || [];

        return `
      <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white p-6">
          <div class="flex items-center gap-3 mb-2">
            <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <i class="fas fa-globe-americas text-lg"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold">Contextualize</h2>
              <p class="text-emerald-100">Set the scene for your learning journey</p>
            </div>
          </div>
        </div>
        
        <!-- Content -->
        <div class="p-6 space-y-6">
          <!-- Scenario Card -->
          <div class="bg-gray-50 rounded-xl p-6">
            <div class="flex items-start gap-3 mb-4">
              <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-map-marker-alt text-emerald-600"></i>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">${this.escapeHtml(scenario.title || 'Real-World Scenario')}</h3>
                <p class="text-gray-700 leading-relaxed">
                  ${this.escapeHtml(scenario.description || "Imagine you're visiting a café in Madrid during a busy morning. You want to order coffee and a pastry, but you need to know the right phrases and cultural context.")}
                </p>
                ${scenario.location ? `<div class="mt-3 flex items-center gap-2 text-sm text-gray-600">
                  <i class="fas fa-location-dot"></i>
                  <span>${this.escapeHtml(scenario.location)}</span>
                </div>` : ''}
              </div>
            </div>
          </div>
          
          <!-- Two Column Layout -->
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Cultural Context -->
            <div class="bg-blue-50 rounded-xl p-6">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-users text-blue-600"></i>
                </div>
                <h4 class="text-lg font-semibold text-gray-900">${this.escapeHtml(culturalContext.title || 'Cultural Context')}</h4>
              </div>
              <ul class="space-y-3">
                ${culturalContext.facts ? culturalContext.facts.map(fact => `
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>${this.escapeHtml(fact)}</span>
                  </li>
                `).join('') : `
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Spanish cafés open early (around 6 AM)</span>
                  </li>
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Locals often stand at the bar for coffee</span>
                  </li>
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Tipping is appreciated but not mandatory</span>
                  </li>
                `}
              </ul>
            </div>
            
            <!-- Key Phrases -->
            <div class="bg-purple-50 rounded-xl p-6">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-comment text-purple-600"></i>
                </div>
                <h4 class="text-lg font-semibold text-gray-900">Key Phrases</h4>
              </div>
              <div class="space-y-4">
                ${keyPhrases.slice(0, 3).map(phrase => `
                  <div class="bg-white rounded-lg p-4 border border-purple-100">
                    <div class="text-lg font-semibold text-gray-900 mb-1">${this.escapeHtml(phrase.spanish)}</div>
                    <div class="text-gray-600 mb-2">${this.escapeHtml(phrase.english)}</div>
                    <div class="text-sm text-purple-600 font-mono">[${this.escapeHtml(phrase.pronunciation)}]</div>
                  </div>
                `).join('')}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Button -->
        <div class="p-6 bg-gray-50 border-t border-gray-100">
          <div class="flex justify-center">
            <button class="next-phase-btn bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all">
              Ready to Learn Vocabulary →
            </button>
          </div>
        </div>
      </div>
    `;
    }

    /**
     * Generate HTML for Acquire phase
     */
    private generateAcquireHTML(content: AcquireContent): string {
        const vocabulary = content.vocabulary || [];
        const grammar = content.grammar || {};

        return `
      <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6">
          <div class="flex items-center gap-3 mb-2">
            <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <i class="fas fa-lightbulb text-lg"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold">Acquire</h2>
              <p class="text-blue-100">Learn essential vocabulary and phrases</p>
            </div>
          </div>
        </div>
        
        <!-- Content -->
        <div class="p-6 space-y-6">
          <!-- Two Column Layout -->
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Vocabulary Section -->
            <div class="space-y-4">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-book text-blue-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">Essential Vocabulary</h3>
              </div>
              <div class="space-y-3">
                ${vocabulary.map(item => `
                  <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                    <div class="flex justify-between items-start mb-2">
                      <div class="text-lg font-bold text-gray-900">${this.escapeHtml(item.word)}</div>
                      <div class="text-sm text-blue-600 font-medium">${this.escapeHtml(item.translation)}</div>
                    </div>
                    <div class="text-sm text-gray-600 font-mono mb-2">[${this.escapeHtml(item.pronunciation)}]</div>
                    <div class="border-t border-gray-200 pt-2 mt-2">
                      <div class="text-sm font-medium text-gray-700">${this.escapeHtml(item.example)}</div>
                      <div class="text-sm text-gray-500">${this.escapeHtml(item.example_translation)}</div>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
            
            <!-- Grammar Section -->
            <div class="space-y-4">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-cogs text-green-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">${this.escapeHtml(grammar.topic || 'Grammar Patterns')}</h3>
              </div>
              <div class="space-y-4">
                ${grammar.structures ? grammar.structures.map(structure => `
                  <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                    <div class="text-lg font-bold text-gray-900 mb-2">${this.escapeHtml(structure.pattern)}</div>
                    <div class="text-gray-600 mb-3">${this.escapeHtml(structure.meaning)}</div>
                    <div class="space-y-2">
                      ${structure.examples.map(example => `
                        <div class="bg-white rounded p-3 text-sm border border-green-200">
                          ${this.escapeHtml(example)}
                        </div>
                      `).join('')}
                    </div>
                  </div>
                `).join('') : `
                  <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                    <div class="text-lg font-bold text-gray-900 mb-2">Basic Greetings</div>
                    <div class="text-gray-600 mb-3">Essential phrases for polite conversation</div>
                    <div class="bg-white rounded p-3 text-sm border border-green-200">
                      "Buenos días" - Good morning
                    </div>
                  </div>
                `}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Button -->
        <div class="p-6 bg-gray-50 border-t border-gray-100">
          <div class="flex justify-center">
            <button class="next-phase-btn bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all">
              Practice What You Learned →
            </button>
          </div>
        </div>
      </div>
    `;
    }

    /**
     * Generate HTML for Reinforce phase
     */
    private generateReinforceHTML(content: ReinforceContent): string {
        const exercises = content.exercises || [];

        return `
      <div class="reinforce-theme text-white p-8 rounded-2xl shadow-xl">
        <div class="text-center mb-8">
          <h2 class="text-4xl font-bold mb-4">💪 Reinforce</h2>
          <p class="text-xl opacity-90">Practice and strengthen your knowledge</p>
        </div>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
          <h3 class="text-2xl font-semibold mb-4">Interactive Practice</h3>
          <div id="practiceArea" class="space-y-6">
            ${exercises.map((exercise, index) => this.generateExerciseHTML(exercise, index)).join('')}
          </div>
        </div>
        
        <div class="text-center">
          <button class="next-phase-btn bg-white text-purple-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
            Apply in Real Conversations →
          </button>
        </div>
      </div>
    `;
    }

    /**
     * Generate HTML for Extend phase
     */
    private generateExtendHTML(content: ExtendContent): string {
        const realWorldApps = content.real_world_applications || [];
        const expansionTopics = content.expansion_topics || [];
        const homework = content.homework;

        return `
      <div class="extend-theme text-white p-8 rounded-2xl shadow-xl">
        <div class="text-center mb-8">
          <h2 class="text-4xl font-bold mb-4">🚀 Extend</h2>
          <p class="text-xl opacity-90">Apply your knowledge in real-world situations</p>
        </div>
        
        <div class="grid md:grid-cols-2 gap-6 mb-8">
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <h3 class="text-2xl font-semibold mb-4">Real-World Practice</h3>
            <div class="space-y-4">
              ${realWorldApps.map(app => `
                <div class="bg-white/20 rounded-lg p-4">
                  <h4 class="font-bold text-lg mb-2">${this.escapeHtml(app.title)}</h4>
                  <p class="text-sm opacity-90 mb-3">${this.escapeHtml(app.description)}</p>
                  <div class="text-xs opacity-80 mb-2">Scenario: ${this.escapeHtml(app.scenario)}</div>
                  <div class="space-y-1">
                    ${app.tasks ? app.tasks.map(task => `
                      <div class="text-xs bg-white/10 rounded px-2 py-1">• ${this.escapeHtml(task)}</div>
                    `).join('') : ''}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <h3 class="text-2xl font-semibold mb-4">Expand Your Knowledge</h3>
            <div class="space-y-4">
              ${expansionTopics.map(topic => `
                <div class="bg-white/20 rounded-lg p-4">
                  <h4 class="font-bold mb-2">${this.escapeHtml(topic.topic)}</h4>
                  <div class="text-sm space-y-2">
                    <div>
                      <strong>Vocabulary:</strong> ${topic.vocabulary.map(this.escapeHtml).join(', ')}
                    </div>
                    <div>
                      <strong>Phrases:</strong>
                      ${topic.phrases.map(phrase => `<div class="ml-2">• ${this.escapeHtml(phrase)}</div>`).join('')}
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
        
        ${homework ? `
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
            <h3 class="text-2xl font-semibold mb-4">${this.escapeHtml(homework.title)}</h3>
            <p class="mb-4">${this.escapeHtml(homework.description)}</p>
            <div class="space-y-2">
              ${homework.steps ? homework.steps.map((step, index) => `
                <div class="flex items-start space-x-2">
                  <span class="bg-white/20 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">${index + 1}</span>
                  <span>${this.escapeHtml(step)}</span>
                </div>
              `).join('') : ''}
            </div>
          </div>
        ` : ''}
        
        <div class="text-center">
          <button class="next-phase-btn bg-white text-amber-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
            Complete Lesson ✓
          </button>
        </div>
      </div>
    `;
    }

    /**
     * Generate HTML for individual exercises
     */
    private generateExerciseHTML(exercise: Exercise, index: number): string {
        switch (exercise.type) {
            case 'multiple_choice':
                return `
          <div class="practice-question bg-white/20 rounded-lg p-4" data-exercise-index="${index}">
            <p class="text-lg mb-3">${this.escapeHtml(exercise.question)}</p>
            <div class="grid grid-cols-1 gap-2">
              ${exercise.options.map((option, optIndex) => `
                <button class="practice-option bg-white/20 hover:bg-white/30 p-3 rounded-lg transition-colors text-left" 
                        data-answer="${optIndex === exercise.correct ? 'correct' : 'wrong'}"
                        data-explanation="${this.escapeHtml(exercise.explanation)}"
                        data-option-index="${optIndex}">
                  ${this.escapeHtml(option)}
                </button>
              `).join('')}
            </div>
          </div>
        `;
            case 'translation':
                return `
          <div class="practice-question bg-white/20 rounded-lg p-4" data-exercise-index="${index}">
            <p class="text-lg mb-3">${this.escapeHtml(exercise.question)}</p>
            <input type="text" class="translation-input w-full p-3 rounded-lg bg-white/20 text-white placeholder-gray-300" 
                   placeholder="Type your translation here..."
                   data-answer="${this.escapeHtml(exercise.answer)}"
                   data-explanation="${this.escapeHtml(exercise.explanation)}">
            <button class="check-translation mt-2 bg-white/30 hover:bg-white/40 px-4 py-2 rounded-lg">Check Answer</button>
          </div>
        `;
            case 'pronunciation':
                return `
          <div class="practice-question bg-white/20 rounded-lg p-4" data-exercise-index="${index}">
            <p class="text-lg mb-2">Practice pronunciation:</p>
            <div class="text-center">
              <p class="text-2xl font-bold mb-2">${this.escapeHtml(exercise.phrase)}</p>
              <p class="text-lg opacity-80 mb-2">${this.escapeHtml(exercise.translation)}</p>
              <p class="text-sm opacity-60 mb-4">[${this.escapeHtml(exercise.phonetic)}]</p>
              <button class="pronunciation-btn bg-white/30 hover:bg-white/40 px-6 py-3 rounded-lg">
                🔊 Listen & Practice
              </button>
            </div>
          </div>
        `;
            default:
                return `<div class="text-gray-500">Unknown exercise type</div>`;
        }
    }

    /**
     * Set up phase-specific event listeners
     */
    private setupPhaseSpecificListeners(phaseName: PhaseType): void {
        // Next phase buttons
        const nextBtns = document.querySelectorAll<HTMLButtonElement>('.next-phase-btn');
        nextBtns.forEach(btn => {
            btn.addEventListener('click', () => this.nextPhase());
        });

        // Exercise-specific listeners
        if (phaseName === 'reinforce') {
            this.setupReinforceListeners();
        }

        this.logger.debug(`Phase-specific listeners set up for ${phaseName}`);
    }

    /**
     * Set up listeners for Reinforce phase exercises
     */
    private setupReinforceListeners(): void {
        // Multiple choice options
        const practiceOptions = document.querySelectorAll<HTMLButtonElement>('.practice-option');
        practiceOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                this.handlePracticeOption(e.target as HTMLButtonElement);
            });
        });

        // Translation check buttons
        const checkBtns = document.querySelectorAll<HTMLButtonElement>('.check-translation');
        checkBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleTranslationCheck(e.target as HTMLButtonElement);
            });
        });

        // Pronunciation buttons
        const pronBtns = document.querySelectorAll<HTMLButtonElement>('.pronunciation-btn');
        pronBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handlePronunciation(e.target as HTMLButtonElement);
            });
        });
    }

    /**
     * Handle practice option clicks
     */
    private handlePracticeOption(button: HTMLButtonElement): void {
        const isCorrect = button.dataset.answer === 'correct';
        const explanation = button.dataset.explanation || '';

        // Visual feedback
        if (isCorrect) {
            button.classList.add('bg-green-500');
            button.innerHTML += ' ✓';
        } else {
            button.classList.add('bg-red-500');
            button.innerHTML += ' ✗';
        }

        // Disable all options
        const allOptions = document.querySelectorAll<HTMLButtonElement>('.practice-option');
        allOptions.forEach(btn => {
            btn.disabled = true;
            btn.classList.add('opacity-75');
        });

        // Show feedback
        setTimeout(() => {
            alert(isCorrect ? 'Correct! Well done!' : `Not quite right. ${explanation}`);
        }, 500);

        // Emit exercise completion event
        this.emitEvent({
            type: 'exercise_completion',
            exercise_type: 'multiple_choice',
            correct: isCorrect,
            time_taken: 0, // TODO: implement time tracking
            timestamp: new Date()
        });
    }

    /**
     * Handle translation check
     */
    private handleTranslationCheck(button: HTMLButtonElement): void {
        const input = button.previousElementSibling as HTMLInputElement;
        const userAnswer = input.value.trim().toLowerCase();
        const correctAnswer = input.dataset.answer?.toLowerCase() || '';
        const explanation = input.dataset.explanation || '';

        const isCorrect = userAnswer === correctAnswer;

        // Show feedback
        alert(isCorrect ? 'Correct! Well done!' : `Not quite right. The correct answer is: "${correctAnswer}". ${explanation}`);

        // Emit exercise completion event
        this.emitEvent({
            type: 'exercise_completion',
            exercise_type: 'translation',
            correct: isCorrect,
            time_taken: 0, // TODO: implement time tracking
            timestamp: new Date()
        });
    }

    /**
     * Handle pronunciation practice
     */
    private handlePronunciation(_button: HTMLButtonElement): void {
        // TODO: Implement speech synthesis
        alert('Pronunciation practice feature coming soon!');

        // Emit exercise completion event
        this.emitEvent({
            type: 'exercise_completion',
            exercise_type: 'pronunciation',
            correct: true, // For now, assume all pronunciation attempts are successful
            time_taken: 0,
            timestamp: new Date()
        });
    }

    /**
     * Set up AI Tutor event listeners
     */
    private setupAITutorListeners(): void {
        const aiTutorBtn = document.getElementById('aiTutorBtn');
        const closeTutorBtn = document.getElementById('closeTutorBtn');
        const sendTutorBtn = document.getElementById('sendTutorBtn');
        const tutorInput = document.getElementById('tutorInput') as HTMLInputElement;

        if (aiTutorBtn) {
            aiTutorBtn.addEventListener('click', () => this.openAITutor());
        }

        if (closeTutorBtn) {
            closeTutorBtn.addEventListener('click', () => this.closeAITutor());
        }

        if (sendTutorBtn) {
            sendTutorBtn.addEventListener('click', () => this.sendTutorMessage());
        }

        if (tutorInput) {
            tutorInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendTutorMessage();
                }
            });
        }
    }

    /**
     * Open AI Tutor modal
     */
    private openAITutor(): void {
        this.aiTutorState.isOpen = true;
        const modal = document.getElementById('aiTutorModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    /**
     * Close AI Tutor modal
     */
    private closeAITutor(): void {
        this.aiTutorState.isOpen = false;
        const modal = document.getElementById('aiTutorModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * Send message to AI Tutor
     */
    private async sendTutorMessage(): Promise<void> {
        const input = document.getElementById('tutorInput') as HTMLInputElement;
        const message = input.value.trim();

        if (!message) return;

        // Add user message to chat
        this.addTutorMessage('user', message);
        input.value = '';

        // Set typing indicator
        this.aiTutorState.isTyping = true;

        try {
            // Simulate AI response (replace with actual API call)
            setTimeout(() => {
                const response = this.generateTutorResponse(message);
                this.addTutorMessage('ai', response);
                this.aiTutorState.isTyping = false;
            }, 1000);
        } catch (error) {
            this.logger.error('Failed to get AI tutor response', error as Error);
            this.addTutorMessage('ai', 'Sorry, I encountered an error. Please try again.');
            this.aiTutorState.isTyping = false;
        }
    }

    /**
     * Add message to AI Tutor chat
     */
    private addTutorMessage(sender: 'user' | 'ai', content: string): void {
        const chatArea = document.getElementById('tutorChatArea');
        if (!chatArea) return;

        const messageId = this.generateId();
        const message = {
            id: messageId,
            sender,
            content,
            timestamp: new Date()
        };

        this.aiTutorState.messages.push(message);

        const messageElement = document.createElement('div');
        messageElement.className = `${sender}-message ${sender === 'user' ? 'bg-blue-600 text-white ml-8' : 'bg-blue-50'
            } p-4 rounded-lg mb-4`;
        messageElement.innerHTML = `<p>${this.escapeHtml(content)}</p>`;

        chatArea.appendChild(messageElement);
        chatArea.scrollTop = chatArea.scrollHeight;
    }

    /**
     * Generate AI tutor response (placeholder)
     */
    private generateTutorResponse(_userMessage: string): string {
        const responses = [
            "Let me explain that concept in more detail...",
            "That's related to what we learned in the previous phase.",
            "Here's a helpful tip for remembering that phrase...",
            "In Spanish culture, this is particularly important because...",
            "Let me give you another example to clarify..."
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    /**
     * Move to next phase
     */
    public nextPhase(): void {
        const currentIndex = this.state.phases.indexOf(this.state.currentPhase);
        if (currentIndex < this.state.phases.length - 1) {
            const nextPhase = this.state.phases[currentIndex + 1];
            this.loadPhase(nextPhase);
        } else {
            this.completeLesson();
        }
    }

    /**
     * Complete the lesson
     */
    private completeLesson(): void {
        this.logger.info('Lesson completed');
        // TODO: Implement lesson completion logic
        alert('Congratulations! You have completed the lesson.');
    }

    /**
     * Update progress bar
     */
    private updateProgressBar(): void {
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            const progress = (this.state.phaseProgress / this.state.totalPhases) * 100;
            progressBar.style.width = `${progress}%`;
        }
    }

    /**
     * Update phase indicators
     */
    private updatePhaseIndicators(activePhaseName: PhaseType): void {
        const indicators = document.querySelectorAll<HTMLElement>('.care-phase-indicator');
        indicators.forEach(indicator => {
            const phase = indicator.dataset.phase;
            if (phase === activePhaseName) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
    }

    /**
     * Hide all phases with animation
     */
    private async hideAllPhases(): Promise<void> {
        const phases = document.querySelectorAll<HTMLElement>('.care-phase-content');
        const animations = Array.from(phases).map(phase => {
            return this.animator.fadeOut(phase, 300);
        });
        await Promise.all(animations);
        phases.forEach(phase => phase.classList.add('hidden'));
    }

    /**
     * Show specific phase with animation
     */
    private async showPhase(phaseName: PhaseType): Promise<void> {
        const phaseElement = document.getElementById(`${phaseName}Phase`);
        if (phaseElement) {
            phaseElement.classList.remove('hidden');
            await this.animator.fadeIn(phaseElement, 300);
        }
    }

    /**
     * Show fallback content for a phase
     */
    private showFallbackContent(phaseName: PhaseType): void {
        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (phaseContainer) {
            phaseContainer.innerHTML = `
        <div class="text-center p-8">
          <h3 class="text-xl font-semibold text-gray-700 mb-4">Content Loading...</h3>
          <p class="text-gray-500">Please wait while we load the ${phaseName} content.</p>
        </div>
      `;
        }
    }

    /**
     * Update lesson state
     */
    private updateState(phaseName: PhaseType): void {
        this.state.currentPhase = phaseName;
        this.state.phaseProgress = this.state.phases.indexOf(phaseName) + 1;
        this.updateProgressBar();
    }

    /**
     * Update partial state
     */
    private setState(updates: Partial<CARELessonState>): void {
        this.state = { ...this.state, ...updates };
    }

    /**
     * Make API request
     */
    private async apiRequest<T>(endpoint: string, config: Partial<RequestConfig> = {}): Promise<APIResponse<T>> {
        const url = `${this.config.apiBaseUrl}${endpoint}`;
        const csrfToken = this.getCSRFToken();

        const defaultConfig: RequestConfig = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken.value
            }
        };

        const finalConfig: RequestInit = {
            ...defaultConfig,
            ...config,
            body: config.body as BodyInit
        };

        try {
            const response = await fetch(url, finalConfig);
            const data = await response.json();

            if (!response.ok) {
                throw new APIError(
                    data.error || 'API request failed',
                    response.status,
                    endpoint
                );
            }

            return data;
        } catch (error) {
            this.logger.error(`API request failed: ${endpoint}`, error as Error);
            throw error;
        }
    }

    /**
     * Get CSRF token
     */
    private getCSRFToken(): CSRFToken {
        const token = document.querySelector<HTMLInputElement>('[name=csrfmiddlewaretoken]');
        return {
            value: token?.value || '',
            headerName: 'X-CSRFToken'
        };
    }

    /**
     * Emit events through event bus
     */
    private emitEvent(event: CAREEvent): void {
        this.eventBus.emit(event.type, event);
    }

    /**
     * Utility functions
     */
    private isValidPhase(phase: string): phase is PhaseType {
        return this.state.phases.includes(phase as PhaseType);
    }

    private escapeHtml(text: string): string {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    private generateId(prefix = 'care'): string {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Initialize default configuration
     */
    private mergeConfig(config: Partial<CAREConfig>): CAREConfig {
        return {
            apiBaseUrl: '',
            enableAnalytics: true,
            enableAITutor: true,
            autoSaveProgress: true,
            transitionDuration: 300,
            maxRetries: 3,
            ...config
        };
    }

    /**
     * Initialize default state
     */
    private initializeState(): CARELessonState {
        return {
            currentPhase: 'contextualize',
            phaseProgress: 0,
            totalPhases: 4,
            phases: ['contextualize', 'acquire', 'reinforce', 'extend'],
            isLoading: false
        };
    }

    /**
     * Initialize AI Tutor state
     */
    private initializeAITutorState(): AITutorState {
        return {
            isOpen: false,
            messages: [],
            isTyping: false
        };
    }

    /**
     * Create event bus implementation
     */
    private createEventBus(): EventBus {
        const listeners = new Map<string, Function[]>();

        return {
            on: (event: string, handler: Function) => {
                if (!listeners.has(event)) {
                    listeners.set(event, []);
                }
                listeners.get(event)!.push(handler);
            },
            off: (event: string, handler: Function) => {
                const eventListeners = listeners.get(event);
                if (eventListeners) {
                    const index = eventListeners.indexOf(handler);
                    if (index > -1) {
                        eventListeners.splice(index, 1);
                    }
                }
            },
            emit: (event: string, data: unknown) => {
                const eventListeners = listeners.get(event);
                if (eventListeners) {
                    eventListeners.forEach(handler => handler(data));
                }
            },
            once: (event: string, handler: Function) => {
                const onceHandler = (data: unknown) => {
                    handler(data);
                    this.eventBus.off(event, onceHandler);
                };
                this.eventBus.on(event, onceHandler);
            }
        };
    }

    /**
     * Create logger implementation
     */
    private createLogger(): Logger {
        return {
            debug: (message: string, data?: unknown) => {
                console.debug(`[CARE] ${message}`, data);
            },
            info: (message: string, data?: unknown) => {
                console.info(`[CARE] ${message}`, data);
            },
            warn: (message: string, data?: unknown) => {
                console.warn(`[CARE] ${message}`, data);
            },
            error: (message: string, error?: Error, data?: unknown) => {
                console.error(`[CARE] ${message}`, error, data);
            }
        };
    }

    /**
     * Create animator implementation
     */
    private createAnimator(): Animator {
        return {
            animate: (element: HTMLElement, keyframes: Keyframe[], options?: KeyframeAnimationOptions) => {
                return element.animate(keyframes, options);
            },
            fadeIn: async (element: HTMLElement, duration = 300) => {
                const animation = element.animate([
                    { opacity: 0 },
                    { opacity: 1 }
                ], { duration, easing: 'ease-out' });
                await animation.finished;
            },
            fadeOut: async (element: HTMLElement, duration = 300) => {
                const animation = element.animate([
                    { opacity: 1 },
                    { opacity: 0 }
                ], { duration, easing: 'ease-in' });
                await animation.finished;
            },
            slideIn: async (element: HTMLElement, direction = 'up', duration = 300) => {
                const transforms = {
                    up: ['translateY(20px)', 'translateY(0)'],
                    down: ['translateY(-20px)', 'translateY(0)'],
                    left: ['translateX(20px)', 'translateX(0)'],
                    right: ['translateX(-20px)', 'translateX(0)']
                };

                const [from, to] = transforms[direction];
                const animation = element.animate([
                    { transform: from, opacity: 0 },
                    { transform: to, opacity: 1 }
                ], { duration, easing: 'ease-out' });
                await animation.finished;
            },
            slideOut: async (element: HTMLElement, direction = 'up', duration = 300) => {
                const transforms = {
                    up: ['translateY(0)', 'translateY(-20px)'],
                    down: ['translateY(0)', 'translateY(20px)'],
                    left: ['translateX(0)', 'translateX(-20px)'],
                    right: ['translateX(0)', 'translateX(20px)']
                };

                const [from, to] = transforms[direction];
                const animation = element.animate([
                    { transform: from, opacity: 1 },
                    { transform: to, opacity: 0 }
                ], { duration, easing: 'ease-in' });
                await animation.finished;
            }
        };
    }

    /**
     * Public API for external usage
     */
    public getCurrentPhase(): PhaseType {
        return this.state.currentPhase;
    }

    public getProgress(): number {
        return (this.state.phaseProgress / this.state.totalPhases) * 100;
    }

    public addEventListener<T extends CAREEvent>(eventType: T['type'], handler: EventHandler<T>): void {
        this.eventBus.on(eventType, handler);
    }

    public removeEventListener<T extends CAREEvent>(eventType: T['type'], handler: EventHandler<T>): void {
        this.eventBus.off(eventType, handler);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing C.A.R.E. Lesson Manager (TypeScript)...');
    (window as any).careManager = new CARELessonManager();
});
