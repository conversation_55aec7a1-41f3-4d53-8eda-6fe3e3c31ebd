# AI Services Requirements for TalonTalk Language Learning
# Core dependencies for LLM integration and speech recognition

# LLM Providers
openai>=1.50.0                 # OpenAI, OpenRouter, DeepSeek APIs
anthropic>=0.34.0              # Anthropic Claude API
requests>=2.31.0               # HTTP requests for custom APIs

# FastAPI Microservice  
fastapi>=0.104.0               # Web framework for AI endpoints
uvicorn[standard]>=0.24.0      # ASGI server
pydantic>=2.5.0                # Data validation

# Speech Recognition (Future)
# faster-whisper>=0.10.0       # FastWhisper for local speech recognition
# deepgram-sdk>=3.2.0          # Deepgram for cloud speech recognition

# Utilities
python-dotenv>=1.0.0           # Environment variable management
python-multipart>=0.0.6       # Form data handling
