# TalonTalk TypeScript Implementation

This directory contains the complete TypeScript implementation of the TalonTalk C.A.R.E. (Contextualize, Acquire, Reinforce, Extend) framework.

## 🚀 Why TypeScript?

The TypeScript implementation provides:

- **Type Safety**: Catch errors at compile time, not runtime
- **Better IDE Support**: Enhanced autocomplete, refactoring, and navigation
- **Improved Documentation**: Types serve as living documentation
- **Easier Maintenance**: Refactoring is safer and more reliable
- **Modern JavaScript**: Use latest ES features with confidence
- **Scalability**: Better code organization for large projects

## 📁 Project Structure

```
src/typescript/
├── types/                 # Type definitions
│   ├── care.types.ts     # C.A.R.E. framework types
│   └── common.types.ts   # Common utility types
├── care/                 # Main C.A.R.E. implementation
│   └── CARELessonManager.ts
├── utils/                # Utility functions
│   └── index.ts
└── index.ts             # Main entry point
```

## 🛠️ Setup and Build

### Prerequisites

- Node.js 16+ (for TypeScript compiler)
- Python 3.x (for Django backend)

### Installation

1. **Install TypeScript globally** (if not already installed):
   ```bash
   npm install -g typescript
   ```

2. **Install project dependencies**:
   ```bash
   npm install
   ```

### Building

**Option 1: Quick Build**
```bash
npm run build
```

**Option 2: Development Mode (watches for changes)**
```bash
npm run dev
```

**Option 3: Manual Build**
```bash
# Compile TypeScript
tsc

# Copy to Django static files
npm run copy-static
```

### Build Output

After building, you'll find:
- **dist/**: Compiled JavaScript files with type declarations
- **talontalk/static/js/dist/**: Files copied for Django to serve

## 🎯 Key Features

### Type-Safe C.A.R.E. Framework

```typescript
// Strongly typed phase content
interface ContextualizeContent {
  scenario: Scenario;
  cultural_context: CulturalContext;
  key_phrases: KeyPhrase[];
}

// Type-safe phase loading
public async loadPhase(phaseName: PhaseType): Promise<void> {
  // TypeScript ensures phaseName is valid
}
```

### Comprehensive Error Handling

```typescript
// Custom error classes with type information
export class CAREError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly phase?: PhaseType,
    public readonly cause?: unknown
  ) { /* ... */ }
}
```

### Event System

```typescript
// Type-safe event handling
this.addEventListener('phase_navigation', (event) => {
  // event is properly typed as PhaseNavigationEvent
  console.log(`Navigated from ${event.from_phase} to ${event.to_phase}`);
});
```

### Utility Functions

```typescript
// Debounced search with proper typing
const debouncedSearch = debounce((query: string) => {
  searchAPI(query);
}, 300);
```

## 🧩 API Reference

### CARELessonManager

The main class that manages the C.A.R.E. lesson experience.

```typescript
const manager = new CARELessonManager({
  apiBaseUrl: '/api',
  enableAITutor: true,
  autoSaveProgress: true
});

// Navigate to a phase
await manager.loadPhase('contextualize');

// Get current progress
const progress = manager.getProgress(); // Returns 0-100

// Listen for events
manager.addEventListener('exercise_completion', (event) => {
  console.log(`Exercise completed: ${event.correct ? 'Correct' : 'Incorrect'}`);
});
```

### Key Types

```typescript
type PhaseType = 'contextualize' | 'acquire' | 'reinforce' | 'extend';

interface VocabularyItem {
  word: string;
  translation: string;
  pronunciation: string;
  example: string;
  example_translation: string;
}

interface Exercise {
  type: 'multiple_choice' | 'translation' | 'pronunciation';
  question: string;
  // ... additional properties based on type
}
```

## 🔧 Development

### Adding New Features

1. **Define types** in `types/` directory
2. **Implement functionality** in appropriate module
3. **Export from** `index.ts`
4. **Build and test**

### Example: Adding New Exercise Type

```typescript
// 1. Add to types/care.types.ts
interface DrawingExercise {
  type: 'drawing';
  prompt: string;
  expected_elements: string[];
}

type Exercise = MultipleChoiceExercise | TranslationExercise | DrawingExercise;

// 2. Implement in CARELessonManager.ts
private generateExerciseHTML(exercise: Exercise): string {
  switch (exercise.type) {
    case 'drawing':
      return this.generateDrawingHTML(exercise);
    // ... other cases
  }
}
```

### Code Style

- Use **PascalCase** for classes and interfaces
- Use **camelCase** for functions and variables
- Use **kebab-case** for file names
- Use **UPPER_SNAKE_CASE** for constants
- Prefer `interface` over `type` for object shapes
- Use `type` for unions and computed types

## 🧪 Testing

*Testing framework coming soon...*

```bash
npm test
```

## 🚢 Deployment

### Development
```bash
npm run dev        # Watch mode for development
```

### Production
```bash
npm run deploy     # Clean build for production
```

The built files are automatically copied to Django's static files directory and will be served by Django.

## 🔄 Migration from JavaScript

The TypeScript implementation is designed to be a drop-in replacement for the JavaScript version:

1. **Same API**: All public methods remain the same
2. **Same HTML structure**: No changes needed to templates
3. **Enhanced features**: Better error handling and type safety
4. **Gradual adoption**: Can run alongside JavaScript version

### Migration Steps

1. Build TypeScript implementation
2. Update template to include TypeScript build instead of JavaScript
3. Test functionality
4. Remove JavaScript version when ready

## 📚 Learning Resources

- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [TypeScript Deep Dive](https://basarat.gitbook.io/typescript/)
- [Effective TypeScript](https://effectivetypescript.com/)

## 🤝 Contributing

1. Follow existing code style and patterns
2. Add types for all new features
3. Update documentation for API changes
4. Test thoroughly before submitting

## 📈 Performance

TypeScript compilation adds a build step but provides:
- **Smaller bundles**: Dead code elimination
- **Better optimization**: Type information helps bundlers
- **Fewer runtime errors**: Catch issues at compile time
- **Improved caching**: Browser can cache type-checked code longer

## 🆘 Troubleshooting

### Common Issues

**TypeScript compilation errors:**
```bash
npm run type-check
```

**Missing types:**
Check that all imports use correct file extensions (`.js` for output)

**Build failures:**
```bash
npm run clean
npm run build
```

**Django not serving files:**
Ensure files are copied to `talontalk/static/js/dist/`

---

✨ **The TypeScript implementation is ready for production use and provides a superior development experience compared to the JavaScript version!**
