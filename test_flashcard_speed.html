<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flashcard Speed Test - LinguaJoy</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-card {
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>

<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-blue-900 mb-4">🚀 Flashcard Speed Test</h1>
                <p class="text-lg text-gray-600">Testing the improved flashcard preloading system</p>
            </div>

            <!-- Test Results -->
            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <!-- Speed Test -->
                <div class="test-card bg-white rounded-xl p-6 shadow-lg">
                    <h2 class="text-xl font-bold text-blue-800 mb-4">⏱️ Speed Test</h2>
                    <div id="speed-results" class="space-y-2">
                        <div class="text-sm text-gray-500">Click "Run Speed Test" to compare...</div>
                    </div>
                    <button onclick="runSpeedTest()"
                        class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Run Speed Test
                    </button>
                </div>

                <!-- Preloading Test -->
                <div class="test-card bg-white rounded-xl p-6 shadow-lg">
                    <h2 class="text-xl font-bold text-green-800 mb-4">📚 Preloading Test</h2>
                    <div id="preload-results" class="space-y-2">
                        <div class="text-sm text-gray-500">Click "Test Preloading" to check...</div>
                    </div>
                    <button onclick="testPreloading()"
                        class="mt-4 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        Test Preloading
                    </button>
                </div>
            </div>

            <!-- Flashcard Preview -->
            <div class="test-card bg-white rounded-xl p-6 shadow-lg mb-8">
                <h2 class="text-xl font-bold text-purple-800 mb-4">📄 Flashcard Preview</h2>
                <div id="flashcard-preview" class="text-gray-500">
                    Load flashcards to see preview...
                </div>
            </div>

            <!-- Session Simulation -->
            <div class="test-card bg-white rounded-xl p-6 shadow-lg">
                <h2 class="text-xl font-bold text-orange-800 mb-4">🎮 Session Simulation</h2>
                <div class="flex space-x-4 mb-4">
                    <button onclick="simulateOldSession()"
                        class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                        Old System (Slow)
                    </button>
                    <button onclick="simulateNewSession()"
                        class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        New System (Fast)
                    </button>
                </div>
                <div id="session-simulation" class="bg-gray-50 p-4 rounded-lg min-h-32">
                    <div class="text-gray-500">Choose a simulation to see the difference...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let preloadedFlashcards = [];

        async function runSpeedTest() {
            const resultsDiv = document.getElementById('speed-results');
            resultsDiv.innerHTML = '<div class="text-blue-600">🔄 Testing API speed...</div>';

            try {
                // Test single API call
                const start = performance.now();
                const response = await fetch('/api/flashcard/?difficulty=beginner&type=multiple_choice&language=spanish&lesson_length=10');
                const data = await response.json();
                const singleCallTime = performance.now() - start;

                // Simulate multiple calls (old system)
                const multiStart = performance.now();
                for (let i = 0; i < 3; i++) {
                    await fetch('/api/flashcard/?difficulty=beginner&type=multiple_choice&language=spanish');
                }
                const multiCallTime = (performance.now() - multiStart) * (10 / 3); // Extrapolate to 10 calls

                // Display results
                resultsDiv.innerHTML = `
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span>🐌 Old System (10 calls):</span>
                            <span class="font-bold text-red-600">${multiCallTime.toFixed(0)}ms</span>
                        </div>
                        <div class="flex justify-between">
                            <span>🚀 New System (1 call):</span>
                            <span class="font-bold text-green-600">${singleCallTime.toFixed(0)}ms</span>
                        </div>
                        <div class="flex justify-between border-t pt-2">
                            <span>⚡ Speed Improvement:</span>
                            <span class="font-bold text-blue-600">${(multiCallTime / singleCallTime).toFixed(1)}x faster</span>
                        </div>
                    </div>
                `;

                if (data.success && data.flashcards) {
                    preloadedFlashcards = data.flashcards;
                }

            } catch (error) {
                resultsDiv.innerHTML = `<div class="text-red-600">❌ Error: ${error.message}</div>`;
            }
        }

        async function testPreloading() {
            const resultsDiv = document.getElementById('preload-results');
            resultsDiv.innerHTML = '<div class="text-green-600">🔄 Testing preloading...</div>';

            try {
                const response = await fetch('/api/flashcard/?difficulty=beginner&type=multiple_choice&language=spanish&lesson_length=10');
                const data = await response.json();

                if (data.success) {
                    const singleCard = data.flashcard;
                    const multipleCards = data.flashcards || [];

                    resultsDiv.innerHTML = `
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Single flashcard:</span>
                                <span class="font-bold text-blue-600">${singleCard ? '✅' : '❌'}</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Multiple flashcards:</span>
                                <span class="font-bold text-green-600">${multipleCards.length} cards</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Preloading possible:</span>
                                <span class="font-bold ${multipleCards.length >= 5 ? 'text-green-600' : 'text-red-600'}">
                                    ${multipleCards.length >= 5 ? '🚀 YES' : '❌ NO'}
                                </span>
                            </div>
                        </div>
                    `;

                    // Update preview
                    if (multipleCards.length > 0) {
                        preloadedFlashcards = multipleCards;
                        updateFlashcardPreview();
                    }

                } else {
                    resultsDiv.innerHTML = `<div class="text-red-600">❌ API Error: ${data.error || 'Unknown'}</div>`;
                }

            } catch (error) {
                resultsDiv.innerHTML = `<div class="text-red-600">❌ Error: ${error.message}</div>`;
            }
        }

        function updateFlashcardPreview() {
            const previewDiv = document.getElementById('flashcard-preview');

            if (preloadedFlashcards.length === 0) {
                previewDiv.innerHTML = '<div class="text-gray-500">No flashcards loaded</div>';
                return;
            }

            let html = `<div class="space-y-3">`;
            html += `<div class="text-sm font-medium text-gray-700">Loaded ${preloadedFlashcards.length} flashcards:</div>`;

            preloadedFlashcards.slice(0, 3).forEach((card, index) => {
                html += `
                    <div class="border-l-4 border-blue-400 pl-4 py-2">
                        <div class="font-medium text-gray-800">${index + 1}. ${card.question}</div>
                        <div class="text-sm text-gray-600">Answer: ${card.correct_answer}</div>
                        ${card.options ? `<div class="text-xs text-gray-500">Options: ${card.options.join(', ')}</div>` : ''}
                    </div>
                `;
            });

            if (preloadedFlashcards.length > 3) {
                html += `<div class="text-sm text-gray-500 italic">... and ${preloadedFlashcards.length - 3} more</div>`;
            }

            html += `</div>`;
            previewDiv.innerHTML = html;
        }

        async function simulateOldSession() {
            const simDiv = document.getElementById('session-simulation');
            simDiv.innerHTML = `
                <div class="space-y-2">
                    <div class="text-red-600 font-medium">🐌 Old System Simulation</div>
                    <div class="text-sm text-gray-600">Each question requires API call + 1.5s delay...</div>
                </div>
            `;

            // Simulate the old system delays
            for (let i = 1; i <= 5; i++) {
                await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API call
                simDiv.innerHTML += `<div class="text-xs text-gray-500">⏳ Loading question ${i}... (API call + delay)</div>`;
                await new Promise(resolve => setTimeout(resolve, 200)); // Simulate the 1.5s delay (shortened for demo)
                simDiv.innerHTML += `<div class="text-sm">📝 Question ${i}: What does "Hola" mean?</div>`;
            }

            simDiv.innerHTML += `<div class="text-red-600 font-medium mt-2">❌ Total time: ~12.5 seconds (5 questions × 2.5s each)</div>`;
        }

        async function simulateNewSession() {
            const simDiv = document.getElementById('session-simulation');
            simDiv.innerHTML = `
                <div class="space-y-2">
                    <div class="text-green-600 font-medium">🚀 New System Simulation</div>
                    <div class="text-sm text-gray-600">Preload all questions at start, then instant transitions...</div>
                </div>
            `;

            // Simulate preloading
            await new Promise(resolve => setTimeout(resolve, 500));
            simDiv.innerHTML += `<div class="text-sm text-green-600">✅ Preloaded 10 questions in 0.5s</div>`;

            // Simulate instant transitions
            for (let i = 1; i <= 5; i++) {
                await new Promise(resolve => setTimeout(resolve, 100)); // Just for visual effect
                simDiv.innerHTML += `<div class="text-sm">⚡ Question ${i}: Instant display (no API call needed)</div>`;
            }

            simDiv.innerHTML += `<div class="text-green-600 font-medium mt-2">🚀 Total time: ~1 second (0.5s preload + instant transitions)</div>`;
        }

        // Auto-run tests on page load
        window.addEventListener('load', function () {
            setTimeout(testPreloading, 1000);
        });
    </script>
</body>

</html>