/**
 * Welcome Modal Integration Script
 * Handles automatic display of welcome modal on login and daily visits
 */
declare class WelcomeModalIntegration {
    static show(): void;
    static getPreloadedContent(): any;
    modalShown: boolean;
    init(): Promise<void>;
    checkShouldShowModal(): Promise<any>;
    waitForModal(callback: any, maxAttempts?: number): void;
    showWelcomeModal(): void;
    setupModalEventListeners(): void;
    handleLearningStart(preloadedContent: any): void;
    navigateToPractice(): void;
    handleModalCompletion(result: any): void;
    trackModalDisplay(): void;
    trackUserChoice(action: any): void;
    updateUserPreferences(preferences: any): Promise<void>;
}
//# sourceMappingURL=welcome-modal-integration.d.ts.map