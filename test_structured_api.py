#!/usr/bin/env python
"""
Test API endpoints for the Structured Prompting Framework
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:8000"
USERNAME = "testuser"  # Change this to your test user
PASSWORD = "testpass123"  # Change this to your test user's password


def test_api_endpoints():
    """Test the new structured prompting API endpoints"""

    print("🧪 TESTING STRUCTURED PROMPTING API ENDPOINTS")
    print("=" * 50)

    # Step 1: Login to get session
    print("1️⃣ Authenticating...")
    session = requests.Session()

    # Get login page to get CSRF token
    login_page = session.get(f"{BASE_URL}/accounts/login/")
    if login_page.status_code != 200:
        print(f"❌ Could not access login page: {login_page.status_code}")
        return

    # Login (this assumes you have a test user)
    login_data = {
        "username": USERNAME,
        "password": PASSWORD,
        "csrfmiddlewaretoken": session.cookies.get("csrftoken", ""),
    }

    login_response = session.post(f"{BASE_URL}/accounts/login/", data=login_data)

    # Check if login was successful (redirect to dashboard)
    if login_response.status_code == 200 and "dashboard" not in login_response.url:
        print("⚠️  Login might have failed, but continuing with tests...")
    else:
        print("✅ Authentication successful")

    # Get CSRF token for API calls
    csrf_token = session.cookies.get("csrftoken", "")
    headers = {
        "Content-Type": "application/json",
        "X-CSRFToken": csrf_token,
        "Referer": BASE_URL,
    }

    # Step 2: Test adaptive flashcards endpoint
    print("\n2️⃣ Testing Adaptive Flashcards API...")

    try:
        response = session.post(
            f"{BASE_URL}/api/adaptive-flashcards/", headers=headers, json={}
        )

        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success', False)}")
            print(f"   Method: {data.get('method', 'unknown')}")
            print(f"   Personalized: {data.get('personalized', False)}")

            if "flashcards" in data:
                print(f"   Flashcards received: {len(data['flashcards'])}")
            elif "flashcard" in data:
                print(f"   Single flashcard received")
                flashcard = data["flashcard"]
                print(f"   Question: {flashcard.get('question_text', 'N/A')[:50]}...")
        else:
            print(f"   ❌ Error: {response.text}")

    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Step 3: Test structured lesson endpoint
    print("\n3️⃣ Testing Structured Lesson API...")

    try:
        # Test adaptive review
        response = session.post(
            f"{BASE_URL}/api/structured-lesson/",
            headers=headers,
            json={"session_type": "adaptive_review", "session_length": 3},
        )

        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success', False)}")
            print(f"   Session Type: {data.get('session_type', 'unknown')}")

            if "result" in data:
                result = data["result"]
                print(f"   Result keys: {list(result.keys())}")
                if "questions" in result:
                    print(f"   Questions generated: {len(result['questions'])}")
        else:
            print(f"   ❌ Error: {response.text}")

    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Step 4: Test with lesson ID (focused practice)
    print("\n4️⃣ Testing Focused Practice with Lesson ID...")

    try:
        response = session.post(
            f"{BASE_URL}/api/structured-lesson/",
            headers=headers,
            json={
                "session_type": "focused_practice",
                "lesson_id": 1,  # Assuming lesson ID 1 exists
                "session_length": 2,
            },
        )

        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success', False)}")
            print(f"   Session Type: {data.get('session_type', 'unknown')}")
        else:
            print(f"   ❌ Error: {response.text}")

    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Step 5: Test original flashcard endpoint (should use structured framework now)
    print("\n5️⃣ Testing Enhanced Original Flashcard Endpoint...")

    try:
        response = session.get(
            f"{BASE_URL}/api/flashcard/",
            params={"language": "spanish", "difficulty": "beginner", "lesson_id": 1},
        )

        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {data.get('success', False)}")
            print(f"   Method: {data.get('method', 'unknown')}")
            print(f"   Cached: {data.get('cached', False)}")

            if "flashcard" in data:
                flashcard = data["flashcard"]
                generation_method = flashcard.get("generation_method", "unknown")
                print(f"   Generation Method: {generation_method}")
        else:
            print(f"   ❌ Error: {response.text}")

    except Exception as e:
        print(f"   ❌ Exception: {e}")

    print(f"\n🏁 API ENDPOINT TESTING COMPLETE")
    print("=" * 50)

    print("\n💡 ANALYSIS:")
    print(
        "• If you see 'structured_framework' generation method, the new system is working!"
    )
    print("• If you see 'demo_fallback', it's falling back gracefully")
    print("• Any 401/403 errors indicate authentication issues")
    print("• 500 errors indicate server-side problems that need investigation")


if __name__ == "__main__":
    print("⚠️  Make sure Django server is running: python manage.py runserver")
    print(f"⚠️  Update USERNAME and PASSWORD in this script for your test user")
    print()

    try:
        test_api_endpoints()
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing stopped by user")
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
