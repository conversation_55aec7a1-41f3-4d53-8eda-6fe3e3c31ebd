{% extends 'base.html' %}
{% block content %}
<style>
  body {
    background: linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%);
  }
  .onboarding-glow {
    box-shadow: 0 8px 32px 0 rgba(60, 72, 180, 0.18), 0 1.5px 6px 0 rgba(80, 200, 180, 0.08);
    border: 1.5px solid #e0e7ff;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(2px);
  }
  .btn-primary, .btn-success {
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px 0 rgba(60, 72, 180, 0.10);
  }
  .btn-primary:focus, .btn-success:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
  }
  .btn-primary {
    background: linear-gradient(90deg, #6366f1 60%, #38bdf8 100%);
    color: #fff;
    border: none;
  }
  .btn-primary:hover {
    background: linear-gradient(90deg, #4f46e5 60%, #0ea5e9 100%);
  }
  .btn-success {
    background: linear-gradient(90deg, #22c55e 60%, #38bdf8 100%);
    color: #fff;
    border: none;
  }
  .btn-success:hover {
    background: linear-gradient(90deg, #16a34a 60%, #0ea5e9 100%);
  }
  .btn-active, .skill-btn.btn-active {
    background: #6366f1 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: 0 2px 8px 0 rgba(99,102,241,0.10);
  }
  .input-error, .select-error {
    border-color: #ef4444 !important;
    background: #fef2f2 !important;
  }
  .onboarding-step {
    transition: opacity 0.3s, transform 0.3s;
  }
  .onboarding-step.hidden {
    opacity: 0;
    pointer-events: none;
    position: absolute;
    left: 0; right: 0;
    z-index: -1;
    transform: scale(0.98);
  }
  .onboarding-step:not(.hidden) {
    opacity: 1;
    position: relative;
    z-index: 1;
    transform: scale(1);
  }
  .error-message {
    animation: fadeIn 0.2s;
  }
</style>
<div class="min-h-screen flex items-center justify-center bg-transparent p-0">
  <div class="w-full max-w-2xl onboarding-glow rounded-3xl shadow-2xl p-10 space-y-8 animate-fade-in">
    <!-- Progress Indicator -->
    <div>
      <div class="text-center mb-1 text-base font-medium text-gray-500 tracking-wide">Step <span id="current-step-text">1</span> of 5</div>
      <div class="w-full bg-gray-200 rounded-full h-3">
        <div id="progress-bar" class="bg-gradient-to-r from-indigo-500 to-sky-400 h-3 rounded-full" style="width: 20%; transition: width 0.3s ease-in-out;"></div>
      </div>
    </div>
    <form id="onboarding-form" autocomplete="off">
      <!-- Step 1: Welcome -->
      <div class="onboarding-step" id="step-1">
        <h2 class="text-4xl font-extrabold text-indigo-600 mb-4 text-center">Welcome to TalonTalk!</h2>
        <p class="mb-8 text-gray-700 text-lg text-center">Let's personalize your learning experience in just a few quick steps.</p>
        <button type="button" class="btn btn-primary w-full py-3 text-lg font-semibold rounded-xl" onclick="navigateToStep(2)">Get Started</button>
      </div>
      <!-- Step 2: Personal Info -->
      <div class="onboarding-step hidden" id="step-2">
        <h2 class="text-3xl font-semibold text-indigo-600 mb-6">First, what should we call you?</h2>
        <div class="space-y-6">
          <div>
            <label class="block mb-2 font-medium text-gray-700">Display Name</label>
            <input type="text" name="display_name" class="input input-bordered w-full rounded-lg py-3 px-4 text-lg" placeholder="e.g., Alex" required>
          </div>
          <div>
            <label class="block mb-2 font-medium text-gray-700">What is your native language?</label>
            <select name="native_language" class="select select-bordered w-full rounded-lg py-3 px-4 text-lg" required>
              <option disabled selected>Select your language</option>
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Chinese">Mandarin Chinese</option>
              <option value="Portuguese">Portuguese</option>
              <option value="Italian">Italian</option>
              <option value="Japanese">Japanese</option>
              <option value="Korean">Korean</option>
              <option value="Russian">Russian</option>
              <option value="Arabic">Arabic</option>
              <option value="Hindi">Hindi</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>
        <div class="flex justify-between mt-10">
          <button type="button" class="btn btn-ghost text-lg" onclick="navigateToStep(1)">Back</button>
          <button type="button" class="btn btn-primary text-lg" onclick="navigateToStep(3)">Next</button>
        </div>
      </div>
      <!-- Step 3: Language & Skill Level -->
      <div class="onboarding-step hidden" id="step-3">
        <h2 class="text-3xl font-semibold text-indigo-600 mb-6">What language are you excited to learn?</h2>
        <div class="space-y-6">
            <div>
                <label class="block mb-2 font-medium text-gray-700">I want to learn...</label>
                <select name="target_language" class="select select-bordered w-full rounded-lg py-3 px-4 text-lg" required>
                    <option disabled selected>Select a language</option>
                    <option value="French">French</option>
                    <option value="Spanish">Spanish</option>
                    <option value="German">German</option>
                    <option value="Japanese">Japanese</option>
                    <option value="Italian">Italian</option>
                    <option value="Portuguese">Portuguese</option>
                    <option value="Chinese">Mandarin Chinese</option>
                    <option value="Korean">Korean</option>
                    <option value="Russian">Russian</option>
                    <option value="Arabic">Arabic</option>
                    <option value="Dutch">Dutch</option>
                    <option value="Swedish">Swedish</option>
                </select>
            </div>
            <div>
                <label class="block mb-2 font-medium text-gray-700">How would you describe your current skill level?</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button type="button" class="btn btn-outline h-20 flex flex-col items-center justify-center skill-btn rounded-xl text-lg" onclick="selectSkill('Beginner', this)">
                        <span class="font-semibold">Beginner</span>
                        <span class="text-xs text-gray-500 mt-1">Just starting</span>
                    </button>
                    <button type="button" class="btn btn-outline h-20 flex flex-col items-center justify-center skill-btn rounded-xl text-lg" onclick="selectSkill('Intermediate', this)">
                        <span class="font-semibold">Intermediate</span>
                        <span class="text-xs text-gray-500 mt-1">Some experience</span>
                    </button>
                    <button type="button" class="btn btn-outline h-20 flex flex-col items-center justify-center skill-btn rounded-xl text-lg" onclick="selectSkill('Advanced', this)">
                        <span class="font-semibold">Advanced</span>
                        <span class="text-xs text-gray-500 mt-1">Pretty fluent</span>
                    </button>
                </div>
                <input type="hidden" name="skill_level" id="skill-level-input" required>
            </div>
        </div>
        <div class="flex justify-between mt-10">
            <button type="button" class="btn btn-ghost text-lg" onclick="navigateToStep(2)">Back</button>
            <button type="button" class="btn btn-primary text-lg" onclick="navigateToStep(4)">Next</button>
        </div>
      </div>
      <!-- Step 4: Learning Goals -->
      <div class="onboarding-step hidden" id="step-4">
        <h2 class="text-3xl font-semibold text-indigo-600 mb-6">What's your main goal?</h2>
        <p class="mb-4 text-gray-500 text-base">This helps us recommend the best learning path for you.</p>
        <select name="main_goal" class="select select-bordered w-full rounded-lg py-3 px-4 text-lg" required>
          <option disabled selected>Select a goal</option>
          <option value="Conversational Fluency">Conversational Fluency</option>
          <option value="Traveling Abroad">Traveling Abroad</option>
          <option value="Career & Business">Career & Business</option>
          <option value="Exams & Certification">Exams & Certification</option>
          <option value="Connecting with Family">Connecting with Family</option>
          <option value="Academic Studies">Academic Studies</option>
          <option value="Just for fun!">Just for fun!</option>
        </select>
        <div class="flex justify-between mt-10">
          <button type="button" class="btn btn-ghost text-lg" onclick="navigateToStep(3)">Back</button>
          <button type="button" class="btn btn-primary text-lg" onclick="navigateToStep(5)">Finish</button>
        </div>
      </div>
      <!-- Step 5: Complete -->
      <div class="onboarding-step hidden" id="step-5">
        <div class="text-center">
          <svg class="mx-auto h-14 w-14 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 class="text-3xl font-bold text-indigo-600 mt-4 mb-2">You're all set!</h2>
          <p class="mb-8 text-gray-700 text-lg">We've personalized your dashboard to help you reach your goals. Your TalonTalk journey begins now.</p>
          <button type="button" class="btn btn-success w-full py-3 text-lg font-semibold rounded-xl" onclick="finishOnboarding()">Go to Dashboard</button>
        </div>
      </div>
    </form>
  </div>
</div>
<script>
let currentStep = 1;
const totalSteps = 5;
function navigateToStep(step) {
  // Simple validation for required fields in the current step before proceeding
  if (step > currentStep) {
    const currentStepElement = document.getElementById('step-' + currentStep);
    const inputs = currentStepElement.querySelectorAll('[required]');
    let allValid = true;
    inputs.forEach(input => {
      if (!input.value) {
        input.classList.add('input-error', 'select-error'); // Highlight empty required fields
        allValid = false;
      } else {
        input.classList.remove('input-error', 'select-error');
      }
    });
    if (!allValid) {
      // Show a brief error message
      const existingError = currentStepElement.querySelector('.error-message');
      if (existingError) existingError.remove();
      const msg = document.createElement('div');
      msg.className = 'error-message text-red-500 text-base mt-3 font-semibold';
      msg.textContent = 'Please fill in all required fields before continuing.';
      currentStepElement.appendChild(msg);
      setTimeout(() => msg.remove(), 3500);
      return;
    }
  }
  currentStep = step;
  // Hide all steps
  document.querySelectorAll('.onboarding-step').forEach(el => el.classList.add('hidden'));
  // Show the target step
  document.getElementById('step-' + step).classList.remove('hidden');
  // Update Progress Bar
  const progressBar = document.getElementById('progress-bar');
  const progress = (step / totalSteps) * 100;
  progressBar.style.width = progress + '%';
  document.getElementById('current-step-text').innerText = step;
  // Focus first input for accessibility
  setTimeout(() => {
    const firstInput = document.getElementById('step-' + step).querySelector('input,select,button');
    if (firstInput) firstInput.focus();
  }, 100);
}
function selectSkill(skill, button) {
    // Set the value of the hidden input
    document.getElementById('skill-level-input').value = skill;
    // Visual feedback for the selected button
    document.querySelectorAll('.skill-btn').forEach(btn => {
        btn.classList.remove('btn-active');
    });
    button.classList.add('btn-active');
}
function finishOnboarding() {
  const form = document.getElementById('onboarding-form');
  const formData = new FormData(form);
  const data = Object.fromEntries(formData.entries());
  // Send data to backend
  fetch('/onboarding/complete/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': getCookie('csrftoken')
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(result => {
    if (result.success) {
      window.location.href = result.redirect_url || '/dashboard/';
    } else {
      alert('There was an error completing your onboarding. Please try again.');
    }
  })
  .catch(error => {
    alert('There was a network error. Please check your connection and try again.');
  });
}
// Helper function to get CSRF token from cookies
function getCookie(name) {
  let cookieValue = null;
  if (document.cookie && document.cookie !== '') {
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      if (cookie.substring(0, name.length + 1) === (name + '=')) {
        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
        break;
      }
    }
  }
  return cookieValue;
}
// Accessibility: allow Enter to go to next step if on a single input step
['display_name','native_language','target_language','main_goal'].forEach(name => {
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
      const el = document.querySelector(`[name="${name}"]`);
      if (el && document.activeElement === el) {
        e.preventDefault();
        if (name === 'main_goal') navigateToStep(5);
        else if (name === 'target_language') navigateToStep(4);
        else if (name === 'native_language') navigateToStep(3);
        else if (name === 'display_name') navigateToStep(3);
      }
    }
  });
});
</script>
{% endblock %}
