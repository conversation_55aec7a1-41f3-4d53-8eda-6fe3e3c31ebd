<!-- Extend Phase: Apply in New Contexts -->
<div class="care-phase-content extend-phase hidden" data-phase="extend">
    <div class="bg-gradient-to-r from-amber-500 to-amber-600 rounded-2xl p-8 text-white mb-8">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <i class="fas fa-rocket text-3xl"></i>
            </div>
            <div>
                <h2 class="text-3xl font-bold">Extend</h2>
                <p class="text-amber-100">Apply your knowledge in creative new contexts</p>
            </div>
        </div>
        <p class="text-lg text-amber-50">Take your learning beyond the café - explore new situations!</p>
    </div>

    <!-- Creative Application Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Scenario Extension -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-lightbulb text-amber-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">New Scenario Challenge</h3>
            </div>
            <div class="space-y-4">
                <div class="p-4 bg-amber-50 rounded-lg border border-amber-200">
                    <h4 class="font-medium text-amber-900 mb-2">🏪 At the Grocery Store</h4>
                    <p class="text-amber-800 text-sm mb-3">You need to buy ingredients for coffee at home. Use your café vocabulary in a new context!</p>
                    <div class="space-y-2">
                        <div class="text-sm">
                            <span class="font-medium text-amber-900">Challenge:</span>
                            <span class="text-amber-700">Ask for "leche" and describe the type of "café" you need</span>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 class="font-medium text-blue-900 mb-2">🏨 Hotel Breakfast</h4>
                    <p class="text-blue-800 text-sm mb-3">You're at a hotel buffet and want to request fresh coffee from the staff.</p>
                    <div class="space-y-2">
                        <div class="text-sm">
                            <span class="font-medium text-blue-900">Challenge:</span>
                            <span class="text-blue-700">Politely ask for coffee service using your café phrases</span>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 bg-green-50 rounded-lg border border-green-200">
                    <h4 class="font-medium text-green-900 mb-2">👥 Teaching a Friend</h4>
                    <p class="text-green-800 text-sm mb-3">Explain to a Spanish-speaking friend how to order coffee in your language.</p>
                    <div class="space-y-2">
                        <div class="text-sm">
                            <span class="font-medium text-green-900">Challenge:</span>
                            <span class="text-green-700">Use your vocabulary to explain coffee culture differences</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Creative Writing -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-pen-fancy text-amber-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">Write Your Story</h3>
            </div>
            <p class="text-gray-600 mb-4">Create a short story using your new vocabulary. Be creative!</p>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Story Prompt:</label>
                    <select id="storyPrompt" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                        <option value="">Choose a story starter...</option>
                        <option value="mystery">A mysterious customer leaves a note at the café...</option>
                        <option value="romance">Two people meet over spilled coffee...</option>
                        <option value="adventure">The café barista discovers a hidden map...</option>
                        <option value="comedy">Everything goes wrong during the morning rush...</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Your Story (Use at least 5 Spanish words):</label>
                    <textarea id="userStory" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent resize-none" placeholder="Start writing your story here..."></textarea>
                </div>
                
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <span id="wordCount">0</span> words | 
                        <span id="spanishWordCount">0</span> Spanish words detected
                    </div>
                    <button id="checkStory" class="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors" disabled>
                        Check Story
                    </button>
                </div>
                
                <div id="storyFeedback" class="hidden p-4 rounded-lg">
                    <!-- Story feedback will appear here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Real-World Application -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-globe text-amber-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">Real-World Mission</h3>
        </div>
        <p class="text-gray-600 mb-6">Ready for a real challenge? Here's your mission for today:</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div class="p-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-lg">
                    <h4 class="font-bold mb-2">🎯 Today's Mission</h4>
                    <p class="text-amber-50 text-sm">Find a Spanish-speaking café or restaurant in your area (or call one) and practice ordering using the phrases you learned!</p>
                </div>
                
                <div class="space-y-3">
                    <div class="flex items-center gap-3">
                        <input type="checkbox" id="mission1" class="mission-checkbox">
                        <label for="mission1" class="text-gray-700">Look up a local Spanish/Latino café or restaurant</label>
                    </div>
                    <div class="flex items-center gap-3">
                        <input type="checkbox" id="mission2" class="mission-checkbox">
                        <label for="mission2" class="text-gray-700">Practice your ordering phrase 3 times out loud</label>
                    </div>
                    <div class="flex items-center gap-3">
                        <input type="checkbox" id="mission3" class="mission-checkbox">
                        <label for="mission3" class="text-gray-700">Visit or call and order something in Spanish</label>
                    </div>
                    <div class="flex items-center gap-3">
                        <input type="checkbox" id="mission4" class="mission-checkbox">
                        <label for="mission4" class="text-gray-700">Share your experience (notes below)</label>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900">Reflection Notes:</h4>
                <textarea id="missionNotes" rows="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent resize-none" placeholder="How did it go? What did you learn? What was challenging?"></textarea>
                
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Rate your confidence:</label>
                    <div class="flex gap-2">
                        <button class="confidence-rating w-10 h-10 rounded-full border border-gray-300 hover:border-amber-500 transition-colors" data-rating="1">1</button>
                        <button class="confidence-rating w-10 h-10 rounded-full border border-gray-300 hover:border-amber-500 transition-colors" data-rating="2">2</button>
                        <button class="confidence-rating w-10 h-10 rounded-full border border-gray-300 hover:border-amber-500 transition-colors" data-rating="3">3</button>
                        <button class="confidence-rating w-10 h-10 rounded-full border border-gray-300 hover:border-amber-500 transition-colors" data-rating="4">4</button>
                        <button class="confidence-rating w-10 h-10 rounded-full border border-gray-300 hover:border-amber-500 transition-colors" data-rating="5">5</button>
                    </div>
                    <div class="text-sm text-gray-500">1 = Not confident, 5 = Very confident</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Learning Portfolio -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-graduation-cap text-amber-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">Your Learning Portfolio</h3>
        </div>
        <p class="text-gray-600 mb-6">Celebrate what you've accomplished in this lesson!</p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="text-center p-4 bg-emerald-50 rounded-lg">
                <div class="text-3xl text-emerald-600 mb-2">🌍</div>
                <div class="font-medium text-emerald-900">Context Mastery</div>
                <div class="text-sm text-emerald-700">Café culture & situations</div>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-3xl text-blue-600 mb-2">📚</div>
                <div class="font-medium text-blue-900">Vocabulary Acquired</div>
                <div class="text-sm text-blue-700">10+ new words & phrases</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-3xl text-purple-600 mb-2">💪</div>
                <div class="font-medium text-purple-900">Skills Reinforced</div>
                <div class="text-sm text-purple-700">Practice & conversation</div>
            </div>
        </div>
        
        <div class="p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg border border-amber-200">
            <h4 class="font-medium text-amber-900 mb-2">🏆 Achievements Unlocked:</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                <div class="flex items-center gap-2 text-amber-700">
                    <i class="fas fa-check-circle text-green-500"></i>
                    <span>Café Explorer</span>
                </div>
                <div class="flex items-center gap-2 text-amber-700">
                    <i class="fas fa-check-circle text-green-500"></i>
                    <span>Vocabulary Builder</span>
                </div>
                <div class="flex items-center gap-2 text-amber-700">
                    <i class="fas fa-check-circle text-green-500"></i>
                    <span>Conversation Starter</span>
                </div>
                <div class="flex items-center gap-2 text-amber-700">
                    <i class="fas fa-check-circle text-green-500"></i>
                    <span>Cultural Ambassador</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Next Steps -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-arrow-right text-amber-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">What's Next?</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Continue Your Journey:</h4>
                <div class="space-y-3">
                    <div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:border-amber-500 transition-colors">
                        <i class="fas fa-utensils text-amber-600"></i>
                        <div>
                            <div class="font-medium">Restaurant Ordering</div>
                            <div class="text-sm text-gray-500">Expand to full meal conversations</div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:border-amber-500 transition-colors">
                        <i class="fas fa-shopping-cart text-amber-600"></i>
                        <div>
                            <div class="font-medium">Grocery Shopping</div>
                            <div class="text-sm text-gray-500">Food vocabulary & quantities</div>
                        </div>
                    </div>
                    <div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:border-amber-500 transition-colors">
                        <i class="fas fa-map text-amber-600"></i>
                        <div>
                            <div class="font-medium">Asking for Directions</div>
                            <div class="text-sm text-gray-500">Navigate Spanish-speaking cities</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 mb-3">Study Tips:</h4>
                <div class="space-y-3 text-sm text-gray-600">
                    <div class="flex items-start gap-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-1"></i>
                        <span>Practice your café phrases daily for one week to build muscle memory</span>
                    </div>
                    <div class="flex items-start gap-2">
                        <i class="fas fa-users text-blue-500 mt-1"></i>
                        <span>Find a Spanish conversation partner to practice ordering scenarios</span>
                    </div>
                    <div class="flex items-start gap-2">
                        <i class="fas fa-video text-purple-500 mt-1"></i>
                        <span>Watch Spanish café/restaurant scenes in movies or YouTube videos</span>
                    </div>
                    <div class="flex items-start gap-2">
                        <i class="fas fa-book text-green-500 mt-1"></i>
                        <span>Keep a vocabulary journal with new words you encounter</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <button id="completeLessonBtn" class="px-8 py-3 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-lg hover:from-amber-600 hover:to-orange-600 transition-all duration-200 shadow-lg hover:shadow-xl">
                <i class="fas fa-trophy mr-2"></i>
                Complete Lesson & Continue
            </button>
        </div>
    </div>
</div>

<script>
// Extend phase interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Story writing functionality
    const storyPrompt = document.getElementById('storyPrompt');
    const userStory = document.getElementById('userStory');
    const wordCount = document.getElementById('wordCount');
    const spanishWordCount = document.getElementById('spanishWordCount');
    const checkStoryBtn = document.getElementById('checkStory');
    const storyFeedback = document.getElementById('storyFeedback');
    
    // Spanish vocabulary from the lesson
    const spanishWords = ['café', 'con', 'leche', 'cortado', 'croissant', 'cuenta', 'favor', 'gracias', 'buenos', 'días', 'gustaría', 'puede', 'traerme'];
    
    function updateWordCount() {
        const text = userStory.value.trim();
        const words = text ? text.split(/\s+/) : [];
        const totalWords = words.length;
        
        // Count Spanish words
        const foundSpanishWords = words.filter(word => {
            const cleanWord = word.toLowerCase().replace(/[.,!?]/g, '');
            return spanishWords.includes(cleanWord);
        });
        
        wordCount.textContent = totalWords;
        spanishWordCount.textContent = foundSpanishWords.length;
        
        // Enable check button if story has content and Spanish words
        checkStoryBtn.disabled = totalWords < 10 || foundSpanishWords.length < 3;
    }
    
    storyPrompt.addEventListener('change', function() {
        const prompts = {
            mystery: "The small café on Calle Mayor was usually quiet at this hour, but today María noticed something unusual. A customer had left a handwritten note next to his empty café con leche cup...",
            romance: "Elena was rushing to work when she accidentally bumped into someone outside the café. \"¡Perdón!\" she exclaimed as coffee spilled everywhere...",
            adventure: "While cleaning the espresso machine, barista Carlos discovered an old map hidden behind it. The map showed locations of cafés throughout Madrid, with mysterious symbols...",
            comedy: "It was 8 AM on Monday morning, and everything that could go wrong at Café Central was going wrong. First, the coffee machine broke down, then..."
        };
        
        if (prompts[this.value]) {
            userStory.value = prompts[this.value];
            updateWordCount();
        }
    });
    
    userStory.addEventListener('input', updateWordCount);
    
    checkStoryBtn.addEventListener('click', function() {
        const text = userStory.value.trim();
        const words = text.split(/\s+/);
        const foundSpanishWords = words.filter(word => {
            const cleanWord = word.toLowerCase().replace(/[.,!?]/g, '');
            return spanishWords.includes(cleanWord);
        });
        
        storyFeedback.classList.remove('hidden');
        
        if (foundSpanishWords.length >= 5) {
            storyFeedback.className = 'p-4 rounded-lg bg-green-50 border border-green-200';
            storyFeedback.innerHTML = `
                <div class="flex items-center gap-2 text-green-800 mb-2">
                    <i class="fas fa-trophy"></i>
                    <span class="font-medium">Excellent Story!</span>
                </div>
                <p class="text-green-700 text-sm mb-2">You used ${foundSpanishWords.length} Spanish words from your lesson: ${foundSpanishWords.join(', ')}</p>
                <p class="text-green-600 text-sm">Your creative application of the vocabulary shows real understanding!</p>
            `;
        } else {
            storyFeedback.className = 'p-4 rounded-lg bg-amber-50 border border-amber-200';
            storyFeedback.innerHTML = `
                <div class="flex items-center gap-2 text-amber-800 mb-2">
                    <i class="fas fa-lightbulb"></i>
                    <span class="font-medium">Good start!</span>
                </div>
                <p class="text-amber-700 text-sm">Try adding more Spanish words from today's lesson: café, con leche, por favor, gracias, etc.</p>
            `;
        }
    });
    
    // Mission checklist
    const missionCheckboxes = document.querySelectorAll('.mission-checkbox');
    const missionNotes = document.getElementById('missionNotes');
    
    function updateMissionProgress() {
        const completed = Array.from(missionCheckboxes).filter(cb => cb.checked).length;
        const total = missionCheckboxes.length;
        
        if (completed === total && missionNotes.value.trim().length > 10) {
            // Mission complete!
            const completeLessonBtn = document.getElementById('completeLessonBtn');
            completeLessonBtn.classList.add('animate-pulse');
        }
    }
    
    missionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateMissionProgress);
    });
    
    missionNotes.addEventListener('input', updateMissionProgress);
    
    // Confidence rating
    const confidenceButtons = document.querySelectorAll('.confidence-rating');
    
    confidenceButtons.forEach(button => {
        button.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            
            // Update visual feedback
            confidenceButtons.forEach((btn, index) => {
                if (index < rating) {
                    btn.classList.add('bg-amber-500', 'text-white', 'border-amber-500');
                    btn.classList.remove('border-gray-300');
                } else {
                    btn.classList.remove('bg-amber-500', 'text-white', 'border-amber-500');
                    btn.classList.add('border-gray-300');
                }
            });
            
            // Show encouragement based on rating
            let message = '';
            if (rating >= 4) {
                message = '🌟 Excellent confidence! You\'re ready for real conversations!';
            } else if (rating >= 3) {
                message = '👍 Good confidence! Keep practicing and you\'ll improve quickly.';
            } else {
                message = '💪 Building confidence takes time. Practice more and you\'ll get there!';
            }
            
            // Show message temporarily
            const messageDiv = document.createElement('div');
            messageDiv.className = 'mt-2 p-2 bg-amber-50 text-amber-700 rounded text-sm';
            messageDiv.textContent = message;
            
            const existingMessage = this.parentElement.nextElementSibling;
            if (existingMessage && existingMessage.classList.contains('mt-2')) {
                existingMessage.remove();
            }
            
            this.parentElement.insertAdjacentElement('afterend', messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        });
    });
    
    // Complete lesson button
    document.getElementById('completeLessonBtn').addEventListener('click', function() {
        // Show completion celebration
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm';
        modal.innerHTML = `
            <div class="bg-white rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="text-6xl mb-4">🎉</div>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Lesson Complete!</h3>
                <p class="text-gray-600 mb-6">You've successfully completed the C.A.R.E. learning journey for café vocabulary. You're ready for real Spanish conversations!</p>
                <div class="space-y-2 mb-6">
                    <div class="flex items-center justify-center gap-2 text-green-600">
                        <i class="fas fa-check"></i>
                        <span>Contextualized learning</span>
                    </div>
                    <div class="flex items-center justify-center gap-2 text-blue-600">
                        <i class="fas fa-check"></i>
                        <span>Acquired new vocabulary</span>
                    </div>
                    <div class="flex items-center justify-center gap-2 text-purple-600">
                        <i class="fas fa-check"></i>
                        <span>Reinforced through practice</span>
                    </div>
                    <div class="flex items-center justify-center gap-2 text-amber-600">
                        <i class="fas fa-check"></i>
                        <span>Extended to new contexts</span>
                    </div>
                </div>
                <button id="closeCompletionModal" class="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg hover:from-green-600 hover:to-blue-600 transition-all">
                    Continue Learning Journey
                </button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        document.getElementById('closeCompletionModal').addEventListener('click', function() {
            modal.remove();
            // Redirect to dashboard or next lesson
            window.location.href = '/dashboard/';
        });
    });
});
</script>
