# Generated by Django 5.2.3 on 2025-07-07 18:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0008_add_cefr_levels'),
    ]

    operations = [
        migrations.AddField(
            model_name='userlearningprofile',
            name='current_cefr_level',
            field=models.CharField(choices=[('A1', 'A1 - Breakthrough (Beginner)'), ('A2', 'A2 - Waystage (Elementary)'), ('B1', 'B1 - Threshold (Intermediate)'), ('B2', 'B2 - Vantage (Upper Intermediate)'), ('C1', 'C1 - Proficiency (Advanced)'), ('C2', 'C2 - Mastery (Near Native)')], db_index=True, default='A1', help_text="User's current CEFR level based on performance", max_length=2),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='current_level',
            field=models.PositiveIntegerField(default=1),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='skill_level',
            field=models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='beginner', max_length=20),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='target_cefr_level',
            field=models.CharField(choices=[('A1', 'A1 - Breakthrough (Beginner)'), ('A2', 'A2 - Waystage (Elementary)'), ('B1', 'B1 - Threshold (Intermediate)'), ('B2', 'B2 - Vantage (Upper Intermediate)'), ('C1', 'C1 - Proficiency (Advanced)'), ('C2', 'C2 - Mastery (Near Native)')], default='B2', help_text="User's target CEFR level goal", max_length=2),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='target_language',
            field=models.CharField(default='spanish', help_text='Primary language being learned', max_length=20),
        ),
    ]
