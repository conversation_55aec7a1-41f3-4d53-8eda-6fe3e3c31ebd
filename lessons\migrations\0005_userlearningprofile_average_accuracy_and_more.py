# Generated by Django 5.2.3 on 2025-07-04 12:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0004_contentitem_usercontentperformance_userlessonqueue_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='userlearningprofile',
            name='average_accuracy',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='learning_pace',
            field=models.CharField(default='moderate', max_length=20),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='learning_style',
            field=models.Char<PERSON>ield(default='visual', max_length=20),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='preferred_content_types',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='strengths',
            field=models.J<PERSON><PERSON>ield(default=list),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='total_sessions',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='weaknesses',
            field=models.JSONField(default=list),
        ),
    ]
