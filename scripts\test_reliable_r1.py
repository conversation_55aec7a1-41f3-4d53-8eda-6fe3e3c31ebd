#!/usr/bin/env python
"""
Test Reliable R1 Generation
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talontalk.settings')
django.setup()

from lessons.reliable_r1_generator import ReliableR1Generator
from lessons.models import ContentItem

def main():
    print("Reliable R1 Generator Test")
    print("=" * 30)
    
    generator = ReliableR1Generator()
    
    if not generator.client:
        print("ERROR: R1 not available")
        return
    
    print("Testing ultra-simple generation...")
    
    # Test single flashcard
    print("\nTest 1: Single Vocabulary Card")
    card = generator.create_flashcard("spanish", "family", "vocabulary")
    
    if card:
        print("SUCCESS!")
        print(f"  Question: {card.question_text}")
        print(f"  Answer: {card.answer_text}")
        print(f"  Explanation: {card.explanation_text[:60]}...")
    else:
        print("FAILED")
    
    # Test batch
    print("\nTest 2: Batch Generation")
    result = generator.generate_basic_set("spanish", 5)
    
    print(f"Generated: {result['generated_count']}/5")
    print(f"Success rate: {result['success_rate']:.1f}%")
    
    # Show samples
    for i, card in enumerate(result['flashcards'][:2], 1):
        print(f"  Sample {i}: {card.question_text[:40]}...")
    
    total = ContentItem.objects.count()
    print(f"\nTotal in database: {total}")
    
    if total > 0:
        print("SUCCESS: Reliable generation working!")
    else:
        print("FAILED: No content generated")

if __name__ == "__main__":
    main()
