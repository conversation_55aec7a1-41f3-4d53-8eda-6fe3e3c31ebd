"""
Django management command to run background content generation tasks
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from lessons.services.background_tasks import background_tasks


class Command(BaseCommand):
    help = "Queue background content generation for all active users"

    def add_arguments(self, parser):
        parser.add_argument(
            "--user-id",
            type=int,
            help="Generate content for specific user ID only",
        )
        parser.add_argument(
            "--language",
            type=str,
            default="spanish",
            help="Target language for content generation",
        )
        parser.add_argument(
            "--priority",
            type=str,
            choices=["high", "normal", "low"],
            default="normal",
            help="Task priority",
        )

    def handle(self, *args, **options):
        user_id = options["user_id"]
        language = options["language"]
        priority = options["priority"]

        self.stdout.write(
            self.style.SUCCESS(f"🎯 Starting background content generation...")
        )

        if user_id:
            # Generate content for specific user
            job_id = background_tasks.queue_content_generation(
                user_id=user_id, language=language, priority=priority
            )

            if job_id:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"✅ Queued content generation for user {user_id} (Job: {job_id})"
                    )
                )
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f"❌ Failed to queue content generation for user {user_id}"
                    )
                )
        else:
            # Queue daily content preparation for all users
            job_ids = background_tasks.queue_daily_content_prep()

            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ Queued daily content preparation for {len(job_ids)} users"
                )
            )

            # Show first few job IDs
            if job_ids:
                self.stdout.write(f"📝 Sample job IDs: {job_ids[:3]}...")

        self.stdout.write(
            self.style.SUCCESS(f"🚀 Background content generation tasks queued!")
        )
