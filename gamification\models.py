from django.db import models
from django.conf import settings
from django.utils import timezone
from datetime import date, timedelta

# Create your models here.


class Badge(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.ImageField(upload_to="badges/", blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class Achievement(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    badge = models.ForeignKey(Badge, on_delete=models.CASCADE)
    achieved_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.badge.name}"


class Streak(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    current_streak = models.PositiveIntegerField(default=0)
    longest_streak = models.PositiveIntegerField(default=0)
    last_active = models.DateField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.current_streak}"


class Level(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    level = models.PositiveIntegerField(default=1)
    xp = models.PositiveIntegerField(default=0)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - Level {self.level}"


class DailyGoal(models.Model):
    """Daily learning goals for users"""

    GOAL_TYPES = [
        ("xp", "XP Points"),
        ("lessons", "Lessons Completed"),
        ("flashcards", "Flashcards Practiced"),
        ("minutes", "Minutes Studied"),
        ("streak", "Maintain Streak"),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    goal_type = models.CharField(max_length=20, choices=GOAL_TYPES)
    target_value = models.PositiveIntegerField()
    current_progress = models.PositiveIntegerField(default=0)
    date = models.DateField(default=date.today)
    completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["user", "goal_type", "date"]

    def __str__(self):
        return f"{self.user.username} - {self.get_goal_type_display()}: {self.current_progress}/{self.target_value}"

    @property
    def progress_percentage(self):
        if self.target_value == 0:
            return 0
        return min(100, (self.current_progress / self.target_value) * 100)

    def update_progress(self, amount):
        """Update progress and check if goal is completed"""
        self.current_progress += amount
        if self.current_progress >= self.target_value and not self.completed:
            self.completed = True
            self.completed_at = timezone.now()
        self.save()
        return self.completed


class DailyGoalTemplate(models.Model):
    """Template for creating daily goals for users"""

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    goal_type = models.CharField(max_length=20, choices=DailyGoal.GOAL_TYPES)
    target_value = models.PositiveIntegerField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["user", "goal_type"]

    def __str__(self):
        return f"{self.user.username} - {self.get_goal_type_display()}: {self.target_value}"


class XPTransaction(models.Model):
    """Track all XP transactions for detailed analytics"""

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    amount = models.IntegerField()  # Can be negative for penalties
    source = models.CharField(
        max_length=50
    )  # 'lesson', 'flashcard', 'achievement', etc.
    source_id = models.PositiveIntegerField(
        null=True, blank=True
    )  # ID of the source object
    description = models.CharField(max_length=200)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-timestamp"]

    def __str__(self):
        return f"{self.user.username}: {self.amount:+d} XP - {self.description}"


class UserEngagementMetrics(models.Model):
    """Track user engagement metrics for analytics"""

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    date = models.DateField(default=date.today)

    # Daily metrics
    lessons_completed = models.PositiveIntegerField(default=0)
    flashcards_practiced = models.PositiveIntegerField(default=0)
    minutes_studied = models.PositiveIntegerField(default=0)
    xp_earned = models.PositiveIntegerField(default=0)
    goals_completed = models.PositiveIntegerField(default=0)

    # Session metrics
    sessions_count = models.PositiveIntegerField(default=0)
    average_session_length = models.FloatField(default=0.0)  # in minutes

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ["user", "date"]

    def __str__(self):
        return f"{self.user.username} - {self.date}"
