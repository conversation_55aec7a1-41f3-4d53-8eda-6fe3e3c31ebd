"""
Spaced Repetition Algorithm for TalonTalk Language Learning
Implements SuperMemo 2 algorithm with C.A.R.E. framework integration
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from django.utils import timezone
from django.db.models import Q, Avg, Count

from lessons.models import ContentItem, UserContentPerformance, UserLearningProfile

logger = logging.getLogger(__name__)


class SpacedRepetitionService:
    """
    Advanced Spaced Repetition System using SuperMemo 2 algorithm
    with adaptive modifications for language learning
    """

    # Base intervals in days for different difficulty levels
    BASE_INTERVALS = {
        1: [1, 6],  # Beginner: 1 day, 6 days
        2: [1, 4, 8],  # Elementary: 1, 4, 8 days
        3: [1, 3, 7, 15],  # Intermediate: 1, 3, 7, 15 days
        4: [1, 2, 5, 12, 30],  # Upper-intermediate: 1, 2, 5, 12, 30 days
        5: [1, 2, 4, 10, 25, 60],  # Advanced: 1, 2, 4, 10, 25, 60 days
    }

    # Quality factor mappings (0-5 scale)
    QUALITY_FACTORS = {
        0: 0.1,  # Complete blackout
        1: 0.3,  # Incorrect response, but correct one remembered upon seeing it
        2: 0.5,  # Incorrect response; correct one easy to recall
        3: 0.6,  # Correct response recalled with serious difficulty
        4: 0.8,  # Correct response after a hesitation
        5: 1.0,  # Perfect response
    }

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def calculate_next_review(
        self,
        user_id: int,
        content_item: ContentItem,
        quality: int,
        response_time_ms: Optional[int] = None,
    ) -> Tuple[datetime, float]:
        """
        Calculate next review date and new easiness factor

        Args:
            user_id: User ID
            content_item: ContentItem to review
            quality: Quality of response (0-5 scale)
            response_time_ms: Response time in milliseconds

        Returns:
            Tuple of (next_review_date, new_easiness_factor)
        """
        try:
            # Get or create user performance record
            performance, created = UserContentPerformance.objects.get_or_create(
                user_id=user_id,
                content_item=content_item,
                defaults={
                    "times_seen": 0,
                    "times_correct": 0,
                    "proficiency_score": 0.0,
                    "repetition_interval": 1,
                },
            )

            # Update performance stats
            performance.times_seen += 1
            if quality >= 3:  # Consider 3+ as correct
                performance.times_correct += 1

            # Calculate success rate
            success_rate = performance.times_correct / performance.times_seen

            # SuperMemo 2 algorithm adapted for the actual model fields
            # Use proficiency_score as a proxy for easiness factor
            current_proficiency = performance.proficiency_score
            current_interval = performance.repetition_interval

            # Update proficiency score based on quality
            if quality >= 3:
                # Correct response - increase proficiency
                new_proficiency = min(1.0, current_proficiency + 0.1)
                # Increase interval for spaced repetition
                new_interval = min(30, current_interval * 2)
            else:
                # Incorrect response - decrease proficiency
                new_proficiency = max(0.0, current_proficiency - 0.05)
                # Reset interval
                new_interval = 1

            # Apply content difficulty modifier
            difficulty_modifier = self._get_difficulty_modifier(content_item.difficulty)
            new_interval = max(1, int(new_interval * difficulty_modifier))

            # Apply response time modifier (faster response = longer interval)
            if response_time_ms:
                time_modifier = self._get_response_time_modifier(response_time_ms)
                new_interval = max(1, int(new_interval * time_modifier))

            # Apply user performance modifier
            user_modifier = self._get_user_performance_modifier(
                user_id, content_item.language
            )
            new_interval = max(1, int(new_interval * user_modifier))

            # Cap maximum interval based on content difficulty
            max_intervals = {1: 30, 2: 45, 3: 60, 4: 90, 5: 120}
            new_interval = min(
                new_interval, max_intervals.get(content_item.difficulty, 60)
            )

            # Calculate next review date
            next_review = timezone.now() + timedelta(days=new_interval)

            # Update performance record with actual model fields
            performance.proficiency_score = new_proficiency
            performance.repetition_interval = new_interval
            performance.next_review_date = next_review

            if response_time_ms:
                # Update average response time if previous value exists
                if performance.average_response_time:
                    performance.average_response_time = (
                        performance.average_response_time * (performance.times_seen - 1)
                        + response_time_ms
                    ) / performance.times_seen
                else:
                    performance.average_response_time = response_time_ms

            performance.save()

            # Update content item's aggregated stats
            content_item.add_performance_data(quality >= 3)

            self.logger.info(
                f"Spaced repetition: User {user_id}, Content {content_item.id}, "
                f"Quality {quality}, Next review in {new_interval} days"
            )

            return next_review, new_proficiency

        except Exception as e:
            self.logger.error(f"Error calculating next review: {e}")
            # Fallback to simple 1-day interval with default proficiency
            return timezone.now() + timedelta(days=1), 0.5

    def _get_difficulty_modifier(self, difficulty: int) -> float:
        """Get interval modifier based on content difficulty"""
        modifiers = {
            1: 1.2,  # Easier content - longer intervals
            2: 1.1,
            3: 1.0,  # Standard intervals
            4: 0.9,
            5: 0.8,  # Harder content - shorter intervals
        }
        return modifiers.get(difficulty, 1.0)

    def _get_response_time_modifier(self, response_time_ms: int) -> float:
        """Get interval modifier based on response time"""
        if response_time_ms < 3000:  # Very fast (< 3 seconds)
            return 1.3
        elif response_time_ms < 8000:  # Fast (3-8 seconds)
            return 1.1
        elif response_time_ms < 15000:  # Normal (8-15 seconds)
            return 1.0
        elif response_time_ms < 30000:  # Slow (15-30 seconds)
            return 0.9
        else:  # Very slow (> 30 seconds)
            return 0.8

    def _get_user_performance_modifier(self, user_id: int, language: str) -> float:
        """Get interval modifier based on overall user performance"""
        try:
            # Get user's recent performance in this language
            recent_performances = UserContentPerformance.objects.filter(
                user_id=user_id,
                content_item__language=language,
                last_seen__gte=timezone.now() - timedelta(days=30),
            )

            if not recent_performances.exists():
                return 1.0

            # Calculate average success rate
            avg_success = (
                recent_performances.aggregate(
                    avg_success=Avg("times_correct") / Avg("times_seen")
                )["avg_success"]
                or 0.5
            )

            # Strong performers get longer intervals
            if avg_success > 0.85:
                return 1.2
            elif avg_success > 0.7:
                return 1.1
            elif avg_success > 0.5:
                return 1.0
            elif avg_success > 0.3:
                return 0.9
            else:
                return 0.8

        except Exception as e:
            self.logger.error(f"Error calculating user performance modifier: {e}")
            return 1.0

    def get_due_content(
        self, user_id: int, language: str = "spanish", limit: int = 10
    ) -> List[ContentItem]:
        """
        Get content items due for review

        Args:
            user_id: User ID
            language: Target language
            limit: Maximum number of items to return

        Returns:
            List of ContentItem objects due for review
        """
        try:
            # Get content due for review
            due_performances = (
                UserContentPerformance.objects.filter(
                    user_id=user_id,
                    content_item__language=language,
                    next_review_date__lte=timezone.now(),
                )
                .select_related("content_item")
                .order_by("next_review_date")[:limit]
            )

            due_content = [perf.content_item for perf in due_performances]

            # If we don't have enough due content, add new content
            if len(due_content) < limit:
                # Get content the user hasn't seen yet
                seen_content_ids = UserContentPerformance.objects.filter(
                    user_id=user_id
                ).values_list("content_item_id", flat=True)

                new_content = (
                    ContentItem.objects.filter(language=language, is_active=True)
                    .exclude(id__in=seen_content_ids)
                    .order_by("difficulty", "?")[: limit - len(due_content)]
                )

                due_content.extend(new_content)

            return due_content

        except Exception as e:
            self.logger.error(f"Error getting due content: {e}")
            return []

    def get_learning_progress(self, user_id: int) -> Dict:
        """
        Get comprehensive learning progress for a user

        Args:
            user_id: User ID

        Returns:
            Dictionary with progress statistics
        """
        try:
            performances = UserContentPerformance.objects.filter(user_id=user_id)

            if not performances.exists():
                return {
                    "total_items_studied": 0,
                    "mastery_level": 0.0,
                    "average_success_rate": 0.0,
                    "items_due_today": 0,
                    "streak_days": 0,
                    "next_review_count": 0,
                }

            # Calculate statistics
            total_items = performances.count()
            avg_success = (
                performances.aggregate(avg=Avg("times_correct") / Avg("times_seen"))[
                    "avg"
                ]
                or 0.0
            )

            # Items with high success rate and long intervals = mastered
            mastered_items = performances.filter(
                times_correct__gte=3,
                repetition_count__gte=3,
                easiness_factor__gte=2.0,
            ).count()

            mastery_level = (
                (mastered_items / total_items) * 100 if total_items > 0 else 0
            )

            # Items due today
            due_today = performances.filter(
                next_review_date__date=timezone.now().date()
            ).count()

            # Items due in next 7 days
            next_week = performances.filter(
                next_review_date__range=[
                    timezone.now(),
                    timezone.now() + timedelta(days=7),
                ]
            ).count()

            # Calculate streak (simplified)
            recent_activity = performances.filter(
                last_seen__gte=timezone.now() - timedelta(days=7)
            ).exists()

            return {
                "total_items_studied": total_items,
                "mastery_level": round(mastery_level, 1),
                "average_success_rate": round(avg_success * 100, 1),
                "items_due_today": due_today,
                "streak_days": 1 if recent_activity else 0,  # Simplified
                "next_review_count": next_week,
                "mastered_items": mastered_items,
            }

        except Exception as e:
            self.logger.error(f"Error getting learning progress: {e}")
            return {
                "total_items_studied": 0,
                "mastery_level": 0.0,
                "average_success_rate": 0.0,
                "items_due_today": 0,
                "streak_days": 0,
                "next_review_count": 0,
            }

    def optimize_learning_schedule(self, user_id: int) -> Dict:
        """
        Optimize learning schedule based on user performance patterns

        Args:
            user_id: User ID

        Returns:
            Dictionary with optimization recommendations
        """
        try:
            performances = UserContentPerformance.objects.filter(
                user_id=user_id, last_seen__gte=timezone.now() - timedelta(days=30)
            )

            if not performances.exists():
                return {"message": "Not enough data for optimization"}

            # Analyze performance patterns
            weak_areas = (
                performances.filter(times_correct__lt=2, times_seen__gte=3)
                .values("content_item__type", "content_item__difficulty")
                .annotate(count=Count("id"))
                .order_by("-count")
            )

            strong_areas = (
                performances.filter(easiness_factor__gte=2.5, repetition_count__gte=3)
                .values("content_item__type")
                .annotate(count=Count("id"))
                .order_by("-count")
            )

            # Generate recommendations
            recommendations = []

            if weak_areas:
                weak_type = weak_areas[0]["content_item__type"]
                recommendations.append(f"Focus more on {weak_type} exercises")

            avg_response_time = performances.aggregate(
                avg_time=Avg("average_response_time")
            )["avg_time"]

            if avg_response_time and avg_response_time > 15000:  # > 15 seconds
                recommendations.append(
                    "Practice more frequently to improve response time"
                )

            return {
                "weak_areas": list(weak_areas[:3]),
                "strong_areas": list(strong_areas[:3]),
                "recommendations": recommendations,
                "optimal_daily_reviews": min(20, max(5, performances.count() // 7)),
            }

        except Exception as e:
            self.logger.error(f"Error optimizing learning schedule: {e}")
            return {"message": "Error generating optimization recommendations"}


# Initialize the service
spaced_repetition = SpacedRepetitionService()
