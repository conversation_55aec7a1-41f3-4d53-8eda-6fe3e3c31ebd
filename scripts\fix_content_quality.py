#!/usr/bin/env python
"""
Quick script to fix content quality issues
Run this to purge poor content and regenerate quality content
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()

from lessons.models import ContentItem
from lessons.advanced_quality_engine import AdvancedQualityEngine, QualityLevel
from lessons.services.content_generator import ContentGeneratorService
from ai_services.llm_config import get_recommended_config


def main():
    print("🔧 TalonTalk Content Quality Fix")
    print("=" * 50)

    # Initialize quality engine
    quality_engine = AdvancedQualityEngine()

    # Analyze existing content
    print("📊 Analyzing existing content...")
    all_content = ContentItem.objects.all()
    poor_content = []
    good_content = []

    for item in all_content:
        content_dict = {
            "id": str(item.id),
            "question": item.question_text,
            "content_type": item.type,
            "language": item.language,
        }

        try:
            report = quality_engine.validate_content(content_dict, item.language)
            if (
                report.overall_score < 60
                or report.quality_level == QualityLevel.REJECTED
            ):
                poor_content.append(item)
            else:
                good_content.append(item)
        except Exception as e:
            print(f"❌ Error analyzing item {item.id}: {e}")
            poor_content.append(item)

    print(f"📈 Quality Analysis Results:")
    print(f"   ✅ Good quality: {len(good_content)} items")
    print(f"   ❌ Poor quality: {len(poor_content)} items")

    if poor_content:
        print(f"\n🗑️  Removing {len(poor_content)} poor quality items...")
        for item in poor_content:
            print(f"   Deleting: {item.question_text[:50]}...")
            item.delete()
        print("✅ Poor content removed")

    # Generate new quality content
    print(f"\n🎯 Generating new quality content...")
    generator = ContentGeneratorService()

    languages = ["spanish", "french", "german", "italian", "portuguese"]
    total_generated = 0

    for language in languages:
        print(f"   Generating content for {language}...")
        try:
            result = generator.generate_care_content_batch(
                language=language,
                difficulty_level=2,
                content_types=["flashcard", "mcq", "translation"],
                batch_size=20,
            )

            if result.get("success"):
                generated = len(result.get("content_items", []))
                rejected = result.get("rejected_count", 0)
                total_generated += generated
                print(
                    f"      ✅ Generated {generated} items (rejected {rejected} poor quality)"
                )
            else:
                print(f"      ❌ Failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            print(f"      ❌ Error generating {language} content: {e}")

    print(f"\n🎉 Content Quality Fix Complete!")
    print(f"   📊 Total content now: {ContentItem.objects.count()} items")
    print(f"   ✨ New quality items: {total_generated}")
    print(f"   🗑️  Removed poor items: {len(poor_content)}")
    print("\n💡 All new content has been quality-validated!")


if __name__ == "__main__":
    main()
