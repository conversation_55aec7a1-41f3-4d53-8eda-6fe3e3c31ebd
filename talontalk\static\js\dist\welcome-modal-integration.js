/**
 * Welcome Modal Integration - TypeScript Edition
 * Handles automatic display of welcome modal on login and daily visits
 */
class WelcomeModalIntegration {
    constructor(config = {}) {
        this.currentStep = 0;
        this.modal = null;
        this.config = {
            showOnMount: true,
            autoShow: true,
            checkDaily: true,
            steps: [],
            ...config
        };
        this.userState = this.loadUserState();
        this.init();
    }
    async init() {
        await this.loadModal();
        if (this.config.autoShow && this.config.showOnMount) {
            const shouldShow = await this.checkShouldShowModal();
            if (shouldShow) {
                this.showModal();
            }
        }
        this.setupEventListeners();
    }
    async loadModal() {
        // Try to find existing modal first
        this.modal = document.getElementById('welcome-modal');
        if (!this.modal) {
            // Create modal dynamically if not found
            await this.createModal();
        }
    }
    async createModal() {
        const modalHTML = this.generateModalHTML();
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = modalHTML;
        this.modal = tempDiv.firstElementChild;
        document.body.appendChild(this.modal);
    }
    generateModalHTML() {
        return `
      <div id="welcome-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm hidden">
        <div id="modal-content" class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 overflow-hidden transform transition-all">
          <!-- Header -->
          <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <i class="fas fa-rocket text-xl"></i>
                </div>
                <div>
                  <h2 class="text-2xl font-bold">Welcome to TalonTalk!</h2>
                  <p class="text-blue-100">Let's get you started on your language learning journey</p>
                </div>
              </div>
              <button id="close-welcome" class="text-white/80 hover:text-white transition-colors">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
          </div>

          <!-- Content -->
          <div id="modal-body" class="p-6">
            <div id="welcome-steps" class="space-y-6">
              <!-- Steps will be inserted here -->
            </div>
          </div>

          <!-- Footer -->
          <div class="bg-gray-50 p-6 flex justify-between items-center">
            <button id="skip-welcome" class="text-gray-600 hover:text-gray-800 font-medium">
              Skip Tour
            </button>
            <div class="flex gap-3">
              <button id="prev-step" class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium hidden">
                Previous
              </button>
              <button id="next-step" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium">
                Next
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    }
    setupEventListeners() {
        if (!this.modal)
            return;
        const closeBtn = this.modal.querySelector('#close-welcome');
        const skipBtn = this.modal.querySelector('#skip-welcome');
        const nextBtn = this.modal.querySelector('#next-step');
        const prevBtn = this.modal.querySelector('#prev-step');
        closeBtn?.addEventListener('click', () => this.hideModal());
        skipBtn?.addEventListener('click', () => this.skipWelcome());
        nextBtn?.addEventListener('click', () => this.nextStep());
        prevBtn?.addEventListener('click', () => this.prevStep());
        // Close on backdrop click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hideModal();
            }
        });
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal?.classList.contains('hidden')) {
                this.hideModal();
            }
        });
    }
    async checkShouldShowModal() {
        // Check if user has disabled welcome modal
        if (this.userState.preferences.skipWelcome) {
            return false;
        }
        // Check if user is new (hasn't seen welcome)
        if (!this.userState.hasSeenWelcome) {
            return true;
        }
        // Check daily visit logic
        if (this.config.checkDaily && this.userState.preferences.showDaily) {
            const lastVisit = this.userState.lastVisit;
            const today = new Date().toDateString();
            if (!lastVisit || new Date(lastVisit).toDateString() !== today) {
                return true;
            }
        }
        return false;
    }
    showModal() {
        if (!this.modal)
            return;
        this.modal.classList.remove('hidden');
        this.renderCurrentStep();
        // Update last visit
        this.userState.lastVisit = new Date().toISOString();
        this.saveUserState();
        // Trigger animation
        requestAnimationFrame(() => {
            const content = this.modal?.querySelector('#modal-content');
            if (content) {
                content.style.transform = 'scale(1)';
                content.style.opacity = '1';
            }
        });
    }
    hideModal() {
        if (!this.modal)
            return;
        const content = this.modal.querySelector('#modal-content');
        if (content) {
            content.style.transform = 'scale(0.95)';
            content.style.opacity = '0';
        }
        setTimeout(() => {
            this.modal?.classList.add('hidden');
            this.completeWelcome();
        }, 200);
    }
    renderCurrentStep() {
        const steps = this.config.steps || this.getDefaultSteps();
        const currentStepData = steps[this.currentStep];
        if (!currentStepData)
            return;
        const stepsContainer = this.modal?.querySelector('#welcome-steps');
        const nextBtn = this.modal?.querySelector('#next-step');
        const prevBtn = this.modal?.querySelector('#prev-step');
        if (stepsContainer) {
            stepsContainer.innerHTML = `
        <div class="text-center">
          <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-user-graduate text-blue-600 text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">${currentStepData.title}</h3>
          <p class="text-gray-600 leading-relaxed">${currentStepData.content}</p>
        </div>
        
        <!-- Progress indicator -->
        <div class="flex justify-center gap-2 mt-6">
          ${steps.map((_, index) => `
            <div class="w-2 h-2 rounded-full ${index === this.currentStep ? 'bg-blue-600' : index < this.currentStep ? 'bg-green-500' : 'bg-gray-300'}"></div>
          `).join('')}
        </div>
      `;
        }
        // Update button states
        if (prevBtn) {
            prevBtn.classList.toggle('hidden', this.currentStep === 0);
        }
        if (nextBtn) {
            const isLastStep = this.currentStep === steps.length - 1;
            nextBtn.textContent = isLastStep ? 'Get Started!' : 'Next';
        }
    }
    getDefaultSteps() {
        return [
            {
                id: 'welcome',
                title: 'Welcome to TalonTalk!',
                content: 'Your personalized Spanish learning journey starts here. We use the C.A.R.E. method to make learning effective and fun.',
                skippable: true
            },
            {
                id: 'care-method',
                title: 'The C.A.R.E. Method',
                content: 'Contextualize, Acquire, Reinforce, and Extend - our proven 4-step approach that mirrors how you naturally learn languages.',
                skippable: true
            },
            {
                id: 'personalized',
                title: 'Personalized Learning',
                content: 'Our AI adapts to your learning style, pace, and goals to create a unique experience just for you.',
                skippable: true
            },
            {
                id: 'ready',
                title: 'Ready to Begin?',
                content: 'Let\'s start with your first lesson and begin building your Spanish fluency today!',
                skippable: false
            }
        ];
    }
    nextStep() {
        const steps = this.config.steps || this.getDefaultSteps();
        if (this.currentStep < steps.length - 1) {
            this.currentStep++;
            this.renderCurrentStep();
            // Execute step action if exists
            const currentStepData = steps[this.currentStep];
            if (currentStepData.action) {
                currentStepData.action().catch(console.error);
            }
        }
        else {
            this.completeWelcome();
            this.hideModal();
        }
    }
    prevStep() {
        if (this.currentStep > 0) {
            this.currentStep--;
            this.renderCurrentStep();
        }
    }
    skipWelcome() {
        if (this.config.onSkip) {
            this.config.onSkip();
        }
        this.userState.preferences.skipWelcome = true;
        this.saveUserState();
        this.hideModal();
    }
    completeWelcome() {
        this.userState.hasSeenWelcome = true;
        this.saveUserState();
        if (this.config.onComplete) {
            this.config.onComplete();
        }
    }
    loadUserState() {
        const defaultState = {
            hasSeenWelcome: false,
            lastVisit: null,
            completedSteps: [],
            preferences: {
                skipWelcome: false,
                showDaily: false
            }
        };
        try {
            const stored = localStorage.getItem('talontalk_welcome_state');
            if (stored) {
                return { ...defaultState, ...JSON.parse(stored) };
            }
        }
        catch (error) {
            console.warn('Failed to load welcome state:', error);
        }
        return defaultState;
    }
    saveUserState() {
        try {
            localStorage.setItem('talontalk_welcome_state', JSON.stringify(this.userState));
        }
        catch (error) {
            console.warn('Failed to save welcome state:', error);
        }
    }
    // Public API
    show() {
        this.showModal();
    }
    hide() {
        this.hideModal();
    }
    reset() {
        this.userState = {
            hasSeenWelcome: false,
            lastVisit: null,
            completedSteps: [],
            preferences: {
                skipWelcome: false,
                showDaily: false
            }
        };
        this.currentStep = 0;
        this.saveUserState();
    }
    getUserState() {
        return { ...this.userState };
    }
}
// Make WelcomeModalIntegration globally available
window.WelcomeModalIntegration = WelcomeModalIntegration;
// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎉 Initializing Welcome Modal (TypeScript)...');
    window.welcomeModal = new WelcomeModalIntegration({
        showOnMount: true,
        autoShow: true,
        checkDaily: false, // Set to true if you want daily welcome
        onComplete: () => console.log('✅ Welcome tour completed!'),
        onSkip: () => console.log('⏭️ Welcome tour skipped')
    });
});
export default WelcomeModalIntegration;
//# sourceMappingURL=welcome-modal-integration.js.map