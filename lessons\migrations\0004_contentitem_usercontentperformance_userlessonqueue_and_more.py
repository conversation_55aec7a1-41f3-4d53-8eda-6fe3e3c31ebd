# Generated by Django 5.2.3 on 2025-07-04 10:58

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0003_contentgenerationqueue_userlearningprofile_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('flashcard', 'Flashcard'), ('mcq', 'Multiple Choice Question'), ('translation', 'Translation Exercise'), ('listening', 'Listening Exercise'), ('grammar', 'Grammar Exercise'), ('vocabulary', 'Vocabulary Exercise'), ('conversation', 'Conversation Practice')], db_index=True, max_length=20)),
                ('question_text', models.TextField(help_text='The main question or prompt')),
                ('answer_text', models.TextField(help_text='The correct answer')),
                ('choices_json', models.JSONField(blank=True, default=list, help_text="List of choices for MCQ questions: ['Option A', 'Option B', ...]")),
                ('hint_text', models.TextField(blank=True, help_text='Optional hint for the learner')),
                ('explanation_text', models.TextField(blank=True, help_text='Explanation shown after answering')),
                ('audio_url', models.URLField(blank=True, help_text='Audio file URL for listening exercises')),
                ('image_url', models.URLField(blank=True, help_text='Image URL for visual exercises')),
                ('difficulty', models.IntegerField(choices=[(1, 'Beginner'), (2, 'Elementary'), (3, 'Intermediate'), (4, 'Upper Intermediate'), (5, 'Advanced')], db_index=True, default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('language', models.CharField(db_index=True, default='spanish', help_text='Target language being taught', max_length=10)),
                ('tags', models.JSONField(default=list, help_text="Tags for categorization: ['grammar', 'verbs', 'present-tense']")),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total_attempts', models.PositiveIntegerField(default=0)),
                ('total_correct', models.PositiveIntegerField(default=0)),
                ('average_difficulty_score', models.FloatField(default=0.0)),
            ],
            options={
                'ordering': ['difficulty', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserContentPerformance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('times_seen', models.PositiveIntegerField(default=0)),
                ('times_correct', models.PositiveIntegerField(default=0)),
                ('last_seen', models.DateTimeField(auto_now=True)),
                ('proficiency_score', models.FloatField(db_index=True, default=0.0, help_text="User's mastery level for this content (0.0 to 1.0)", validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('average_response_time', models.FloatField(blank=True, help_text='Average time to answer in seconds', null=True)),
                ('consecutive_correct', models.IntegerField(default=0, help_text='Current streak of correct answers')),
                ('last_incorrect_date', models.DateTimeField(blank=True, help_text='When user last got this item wrong', null=True)),
                ('times_hint_used', models.PositiveIntegerField(default=0)),
                ('next_review_date', models.DateTimeField(blank=True, help_text='When this item should be reviewed again', null=True)),
                ('repetition_interval', models.PositiveIntegerField(default=1, help_text='Days until next review (spaced repetition)')),
                ('first_seen', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-last_seen'],
            },
        ),
        migrations.CreateModel(
            name='UserLessonQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ordered_content_ids', models.JSONField(default=list, help_text='Ordered list of ContentItem IDs: [1, 5, 23, 8, ...]')),
                ('queue_type', models.CharField(choices=[('daily', 'Daily Learning'), ('review', 'Spaced Repetition Review'), ('weak_areas', 'Struggling Topics'), ('new_content', 'New Material')], default='daily', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('paused', 'Paused'), ('completed', 'Completed')], default='active', max_length=10)),
                ('current_position', models.PositiveIntegerField(default=0)),
                ('total_items', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(help_text='When this queue should be refreshed')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.RemoveField(
            model_name='contentgenerationqueue',
            name='user',
        ),
        migrations.RemoveField(
            model_name='preloadedcontent',
            name='user',
        ),
        migrations.RemoveField(
            model_name='userperformancemetrics',
            name='user',
        ),
        migrations.RenameField(
            model_name='userlearningprofile',
            old_name='total_sessions',
            new_name='current_streak',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='average_accuracy',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='engagement_score',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='learning_pace',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='learning_patterns',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='learning_style',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='motivation_level',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='preferred_content_types',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='strengths',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='total_practice_time',
        ),
        migrations.RemoveField(
            model_name='userlearningprofile',
            name='weaknesses',
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='adaptive_difficulty',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='daily_goal_minutes',
            field=models.PositiveIntegerField(default=15),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='last_active_date',
            field=models.DateField(auto_now=True),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='longest_streak',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='spaced_repetition_enabled',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='total_study_time',
            field=models.PositiveIntegerField(default=0, help_text='Total minutes studied'),
        ),
        migrations.AddField(
            model_name='userlearningprofile',
            name='total_xp',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='userlearningprofile',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AlterField(
            model_name='userlearningprofile',
            name='preferred_difficulty',
            field=models.IntegerField(choices=[(1, 'Beginner'), (2, 'Elementary'), (3, 'Intermediate'), (4, 'Upper Intermediate'), (5, 'Advanced')], default=1),
        ),
        migrations.AlterField(
            model_name='userlearningprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='adaptive_learning_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='userlearningprofile',
            index=models.Index(fields=['last_active_date'], name='lessons_use_last_ac_b00159_idx'),
        ),
        migrations.AddIndex(
            model_name='userlearningprofile',
            index=models.Index(fields=['total_xp'], name='lessons_use_total_x_903532_idx'),
        ),
        migrations.AddIndex(
            model_name='contentitem',
            index=models.Index(fields=['type', 'difficulty', 'language'], name='lessons_con_type_eacb55_idx'),
        ),
        migrations.AddIndex(
            model_name='contentitem',
            index=models.Index(fields=['language', 'is_active'], name='lessons_con_languag_032206_idx'),
        ),
        migrations.AddIndex(
            model_name='contentitem',
            index=models.Index(fields=['difficulty', 'average_difficulty_score'], name='lessons_con_difficu_02f101_idx'),
        ),
        migrations.AddField(
            model_name='usercontentperformance',
            name='content_item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_performances', to='lessons.contentitem'),
        ),
        migrations.AddField(
            model_name='usercontentperformance',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_performances', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userlessonqueue',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lesson_queues', to=settings.AUTH_USER_MODEL),
        ),
        migrations.DeleteModel(
            name='ContentGenerationQueue',
        ),
        migrations.DeleteModel(
            name='PreloadedContent',
        ),
        migrations.DeleteModel(
            name='UserPerformanceMetrics',
        ),
        migrations.AddIndex(
            model_name='usercontentperformance',
            index=models.Index(fields=['user', 'proficiency_score'], name='lessons_use_user_id_131c70_idx'),
        ),
        migrations.AddIndex(
            model_name='usercontentperformance',
            index=models.Index(fields=['content_item', 'proficiency_score'], name='lessons_use_content_99106d_idx'),
        ),
        migrations.AddIndex(
            model_name='usercontentperformance',
            index=models.Index(fields=['user', 'last_seen'], name='lessons_use_user_id_f2adcc_idx'),
        ),
        migrations.AddIndex(
            model_name='usercontentperformance',
            index=models.Index(fields=['user', 'next_review_date'], name='lessons_use_user_id_0de9eb_idx'),
        ),
        migrations.AddIndex(
            model_name='usercontentperformance',
            index=models.Index(fields=['proficiency_score', 'last_seen'], name='lessons_use_profici_b1cb55_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='usercontentperformance',
            unique_together={('user', 'content_item')},
        ),
        migrations.AddIndex(
            model_name='userlessonqueue',
            index=models.Index(fields=['user', 'status', 'expires_at'], name='lessons_use_user_id_69b8ff_idx'),
        ),
        migrations.AddIndex(
            model_name='userlessonqueue',
            index=models.Index(fields=['user', 'queue_type'], name='lessons_use_user_id_028d83_idx'),
        ),
    ]
