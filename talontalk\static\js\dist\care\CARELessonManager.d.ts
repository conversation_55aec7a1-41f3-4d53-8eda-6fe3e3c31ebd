/**
 * C.A.R.E. Framework Interactive Lesson System - TypeScript Implementation
 * Handles phase navigation, progress tracking, and AI tutor interaction
 */
import type { PhaseType, CAREConfig, CAREEvent, EventHandler } from '../types/care.types.js';
/**
 * Main C.A.R.E. Lesson Manager Class
 */
export declare class CARELessonManager {
    private state;
    private config;
    private eventBus;
    private logger;
    private animator;
    private aiTutorState;
    constructor(config?: Partial<CAREConfig>);
    /**
     * Initialize the lesson manager
     */
    private init;
    /**
     * Set up event listeners for navigation and interactions
     */
    private setupEventListeners;
    /**
     * Handle phase navigation events
     */
    private handlePhaseNavigation;
    /**
     * Load a specific phase
     */
    loadPhase(phaseName: PhaseType): Promise<void>;
    /**
     * Load phase-specific content from API
     */
    private loadPhaseContent;
    /**
     * Render phase content in the UI
     */
    private renderPhaseContent;
    /**
     * Generate HTML for specific phase content
     */
    private generatePhaseHTML;
    /**
     * Generate HTML for Contextualize phase
     */
    private generateContextualizeHTML;
    /**
     * Generate HTML for Acquire phase
     */
    private generateAcquireHTML;
    /**
     * Generate HTML for Reinforce phase
     */
    private generateReinforceHTML;
    /**
     * Generate HTML for Extend phase
     */
    private generateExtendHTML;
    /**
     * Generate HTML for individual exercises
     */
    private generateExerciseHTML;
    /**
     * Set up phase-specific event listeners
     */
    private setupPhaseSpecificListeners;
    /**
     * Set up listeners for Reinforce phase exercises
     */
    private setupReinforceListeners;
    /**
     * Handle practice option clicks
     */
    private handlePracticeOption;
    /**
     * Handle translation check
     */
    private handleTranslationCheck;
    /**
     * Handle pronunciation practice
     */
    private handlePronunciation;
    /**
     * Set up AI Tutor event listeners
     */
    private setupAITutorListeners;
    /**
     * Open AI Tutor modal
     */
    private openAITutor;
    /**
     * Close AI Tutor modal
     */
    private closeAITutor;
    /**
     * Send message to AI Tutor
     */
    private sendTutorMessage;
    /**
     * Add message to AI Tutor chat
     */
    private addTutorMessage;
    /**
     * Generate AI tutor response (placeholder)
     */
    private generateTutorResponse;
    /**
     * Move to next phase
     */
    nextPhase(): void;
    /**
     * Complete the lesson
     */
    private completeLesson;
    /**
     * Update progress bar
     */
    private updateProgressBar;
    /**
     * Update phase indicators
     */
    private updatePhaseIndicators;
    /**
     * Hide all phases with animation
     */
    private hideAllPhases;
    /**
     * Show specific phase with animation
     */
    private showPhase;
    /**
     * Show fallback content for a phase
     */
    private showFallbackContent;
    /**
     * Update lesson state
     */
    private updateState;
    /**
     * Update partial state
     */
    private setState;
    /**
     * Make API request
     */
    private apiRequest;
    /**
     * Get CSRF token
     */
    private getCSRFToken;
    /**
     * Emit events through event bus
     */
    private emitEvent;
    /**
     * Utility functions
     */
    private isValidPhase;
    private escapeHtml;
    private generateId;
    /**
     * Initialize default configuration
     */
    private mergeConfig;
    /**
     * Initialize default state
     */
    private initializeState;
    /**
     * Initialize AI Tutor state
     */
    private initializeAITutorState;
    /**
     * Create event bus implementation
     */
    private createEventBus;
    /**
     * Create logger implementation
     */
    private createLogger;
    /**
     * Create animator implementation
     */
    private createAnimator;
    /**
     * Public API for external usage
     */
    getCurrentPhase(): PhaseType;
    getProgress(): number;
    addEventListener<T extends CAREEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    removeEventListener<T extends CAREEvent>(eventType: T['type'], handler: EventHandler<T>): void;
}
//# sourceMappingURL=CARELessonManager.d.ts.map