#!/usr/bin/env python
"""
R1 Content Generation Pipeline
Generates comprehensive, high-quality flashcard content using DeepSeek R1
"""

import os
import sys
import django
import time
from typing import Dict, List

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()

from lessons.r1_flashcard_generator import R1FlashcardGenerator
from lessons.models import ContentItem

# Comprehensive content plan for each language
CONTENT_PLAN = {
    "spanish": {
        "vocabulary_contextual": {
            1: [
                "family",
                "food",
                "colors",
                "numbers",
                "greetings",
                "home",
                "body_parts",
                "animals",
            ],
            2: [
                "shopping",
                "travel",
                "weather",
                "clothing",
                "time",
                "directions",
                "emotions",
                "hobbies",
            ],
            3: [
                "work",
                "education",
                "health",
                "technology",
                "environment",
                "culture",
                "relationships",
                "media",
            ],
            4: [
                "politics",
                "economics",
                "science",
                "philosophy",
                "literature",
                "art",
                "history",
                "society",
            ],
            5: [
                "abstract_concepts",
                "specialized_fields",
                "cultural_nuances",
                "idiomatic_expressions",
            ],
        },
        "grammar_situational": {
            1: [
                "present_tense",
                "articles",
                "gender",
                "plurals",
                "basic_questions",
                "ser_vs_estar",
            ],
            2: [
                "past_tense",
                "future_tense",
                "comparatives",
                "possessives",
                "reflexive_verbs",
            ],
            3: [
                "subjunctive_present",
                "conditional",
                "perfect_tenses",
                "por_vs_para",
                "relative_pronouns",
            ],
            4: [
                "subjunctive_past",
                "passive_voice",
                "complex_clauses",
                "indirect_speech",
                "advanced_prepositions",
            ],
            5: [
                "subjunctive_nuances",
                "stylistic_variations",
                "formal_registers",
                "literary_language",
            ],
        },
        "cultural_immersion": {
            1: [
                "greetings_customs",
                "family_dynamics",
                "meal_times",
                "basic_politeness",
            ],
            2: [
                "shopping_etiquette",
                "social_interactions",
                "celebration_customs",
                "workplace_basics",
            ],
            3: [
                "business_culture",
                "friendship_norms",
                "educational_system",
                "social_expectations",
            ],
            4: [
                "professional_etiquette",
                "cultural_values",
                "historical_context",
                "regional_differences",
            ],
            5: [
                "cultural_subtleties",
                "literary_references",
                "philosophical_concepts",
                "social_commentary",
            ],
        },
    },
    "french": {
        "vocabulary_contextual": {
            1: [
                "famille",
                "nourriture",
                "couleurs",
                "nombres",
                "salutations",
                "maison",
                "corps",
                "animaux",
            ],
            2: [
                "achats",
                "voyage",
                "météo",
                "vêtements",
                "temps",
                "directions",
                "émotions",
                "loisirs",
            ],
            3: [
                "travail",
                "éducation",
                "santé",
                "technologie",
                "environnement",
                "culture",
                "relations",
                "médias",
            ],
            4: [
                "politique",
                "économie",
                "science",
                "philosophie",
                "littérature",
                "art",
                "histoire",
                "société",
            ],
            5: [
                "concepts_abstraits",
                "domaines_spécialisés",
                "nuances_culturelles",
                "expressions_idiomatiques",
            ],
        },
        "grammar_situational": {
            1: [
                "present_tense",
                "articles",
                "gender",
                "plurals",
                "basic_questions",
                "être_vs_avoir",
            ],
            2: [
                "past_tenses",
                "future_tense",
                "comparatives",
                "possessives",
                "reflexive_verbs",
            ],
            3: [
                "subjunctive",
                "conditional",
                "perfect_tenses",
                "relative_pronouns",
                "partitive_articles",
            ],
            4: [
                "advanced_subjunctive",
                "passive_voice",
                "complex_clauses",
                "literary_tenses",
            ],
            5: [
                "stylistic_nuances",
                "formal_registers",
                "literary_language",
                "archaic_forms",
            ],
        },
        "cultural_immersion": {
            1: ["salutations_françaises", "politesse", "repas_français", "famille"],
            2: ["savoir_vivre", "interactions_sociales", "fêtes", "travail"],
            3: [
                "culture_d_entreprise",
                "amitié",
                "système_éducatif",
                "attentes_sociales",
            ],
            4: [
                "étiquette_professionnelle",
                "valeurs_françaises",
                "contexte_historique",
                "régions",
            ],
            5: [
                "subtilités_culturelles",
                "références_littéraires",
                "concepts_philosophiques",
            ],
        },
    },
    "german": {
        "vocabulary_contextual": {
            1: [
                "Familie",
                "Essen",
                "Farben",
                "Zahlen",
                "Begrüßungen",
                "Haus",
                "Körper",
                "Tiere",
            ],
            2: [
                "Einkaufen",
                "Reisen",
                "Wetter",
                "Kleidung",
                "Zeit",
                "Richtungen",
                "Gefühle",
                "Hobbys",
            ],
            3: [
                "Arbeit",
                "Bildung",
                "Gesundheit",
                "Technologie",
                "Umwelt",
                "Kultur",
                "Beziehungen",
                "Medien",
            ],
            4: [
                "Politik",
                "Wirtschaft",
                "Wissenschaft",
                "Philosophie",
                "Literatur",
                "Kunst",
                "Geschichte",
            ],
            5: [
                "abstrakte_Konzepte",
                "Fachbereiche",
                "kulturelle_Nuancen",
                "Redewendungen",
            ],
        },
        "grammar_situational": {
            1: [
                "present_tense",
                "articles",
                "cases",
                "word_order",
                "basic_questions",
                "sein_vs_haben",
            ],
            2: [
                "past_tenses",
                "modal_verbs",
                "comparatives",
                "possessives",
                "separable_verbs",
            ],
            3: [
                "subjunctive",
                "passive_voice",
                "relative_clauses",
                "advanced_cases",
                "conjunctions",
            ],
            4: [
                "advanced_subjunctive",
                "complex_clauses",
                "literary_language",
                "formal_registers",
            ],
            5: [
                "stylistic_variations",
                "archaic_forms",
                "dialectal_differences",
                "technical_language",
            ],
        },
        "cultural_immersion": {
            1: [
                "deutsche_Begrüßungen",
                "Höflichkeit",
                "deutsche_Mahlzeiten",
                "Familie",
            ],
            2: ["Einkaufskultur", "soziale_Interaktionen", "Feste", "Arbeitsplatz"],
            3: [
                "Unternehmenskultur",
                "Freundschaft",
                "Bildungssystem",
                "soziale_Erwartungen",
            ],
            4: [
                "Geschäftsetikette",
                "deutsche_Werte",
                "historischer_Kontext",
                "Regionen",
            ],
            5: [
                "kulturelle_Feinheiten",
                "literarische_Bezüge",
                "philosophische_Konzepte",
            ],
        },
    },
}


def main():
    print("R1 Content Generation Pipeline")
    print("=" * 50)
    print("Generating comprehensive flashcard content using DeepSeek R1")
    print()

    # Initialize generator
    generator = R1FlashcardGenerator()

    # Check if R1 is available
    if not generator.client:
        print("DeepSeek R1 not available. Please check your Ollama setup.")
        return

    total_generated = 0
    total_attempted = 0
    language_stats = {}

    # Generate content for each language
    for language, templates in CONTENT_PLAN.items():
        print(f"\n🌍 Generating content for {language.title()}")
        print("-" * 30)

        language_generated = 0
        language_attempted = 0

        for template_type, difficulty_topics in templates.items():
            print(f"  📚 {template_type.replace('_', ' ').title()}")

            for difficulty, topics in difficulty_topics.items():
                print(f"    Level {difficulty}: {len(topics)} topics")

                for topic in topics:
                    try:
                        # Generate batch for this topic
                        result = generator.generate_flashcard_batch(
                            language=language,
                            difficulty=difficulty,
                            template_type=template_type,
                            batch_size=3,  # 3 flashcards per topic
                            topic_focus=topic,
                        )

                        if result["success"]:
                            generated = result["generated_count"]
                            language_generated += generated
                            total_generated += generated

                            print(
                                f"      ✅ {topic}: {generated}/3 flashcards (Success: {result['success_rate']:.1f}%)"
                            )
                        else:
                            print(
                                f"      ❌ {topic}: Failed - {result.get('error', 'Unknown error')}"
                            )

                        language_attempted += 3
                        total_attempted += 3

                        # Small delay to avoid overwhelming the model
                        time.sleep(0.5)

                    except Exception as e:
                        print(f"      ❌ {topic}: Error - {e}")
                        language_attempted += 3
                        total_attempted += 3

        # Language summary
        success_rate = (
            (language_generated / language_attempted * 100)
            if language_attempted > 0
            else 0
        )
        language_stats[language] = {
            "generated": language_generated,
            "attempted": language_attempted,
            "success_rate": success_rate,
        }

        print(
            f"  📊 {language.title()} Summary: {language_generated}/{language_attempted} ({success_rate:.1f}%)"
        )

    # Final summary
    print(f"\n🎉 Content Generation Complete!")
    print("=" * 50)
    print(f"📊 Overall Statistics:")
    print(f"   Total Generated: {total_generated}")
    print(f"   Total Attempted: {total_attempted}")
    print(f"   Overall Success Rate: {(total_generated/total_attempted*100):.1f}%")
    print()

    print(f"📈 Language Breakdown:")
    for language, stats in language_stats.items():
        print(
            f"   {language.title()}: {stats['generated']} flashcards ({stats['success_rate']:.1f}%)"
        )

    # Database verification
    total_in_db = ContentItem.objects.count()
    print(f"\n💾 Database Status:")
    print(f"   Total Content Items: {total_in_db}")
    print(
        f"   R1 Generated: {ContentItem.objects.filter(tags__contains=['r1_generated']).count()}"
    )

    print(f"\n✨ High-quality flashcard content ready for learning!")


if __name__ == "__main__":
    main()
