# TalonTalk

TalonTalk is a modern, AI-powered language learning platform designed to help you master new languages quickly and confidently. Built with Django and Tailwind CSS, it features a high-converting SaaS landing page, robust backend, and a gamified learning experience.

## Features
- AI-personalized lessons and practice
- Three-tier pricing: Free, AI Pro, Live (tutor marketplace)
- 30-day money-back guarantee, free trial, and instant access
- Mobile responsive, production-ready design
- FAQ accordion, testimonials, and social proof
- PostgreSQL database, Django REST Framework API

## Getting Started
1. Clone the repo: `git clone https://github.com/finessed/talontalk.git`
2. Install dependencies: `pip install -r requirements.txt`
3. Set up your database and environment variables (see `django_workflow_templates/DATABASE_SETUP.md`)
4. Run migrations: `python manage.py migrate`
5. Start the server: `python manage.py runserver`

## Project Structure
- `talontalk/` – Django project and core apps
- `talontalk/templates/landing.html` – Main landing page
- `django_workflow_templates/` – DevOps, branching, and workflow docs
- `app_workflow_notes/` – Additional notes and planning

## Development
- Uses Tailwind CDN for rapid prototyping; switch to static build for production
- See `django_workflow_templates/` for branching, backup, and migration strategies

## License
MIT
