# Generated by Django 5.2.3 on 2025-07-07 15:25

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gamification', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='XPTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.IntegerField()),
                ('source', models.CharField(max_length=50)),
                ('source_id', models.PositiveIntegerField(blank=True, null=True)),
                ('description', models.CharField(max_length=200)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='DailyGoal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('goal_type', models.CharField(choices=[('xp', 'XP Points'), ('lessons', 'Lessons Completed'), ('flashcards', 'Flashcards Practiced'), ('minutes', 'Minutes Studied'), ('streak', 'Maintain Streak')], max_length=20)),
                ('target_value', models.PositiveIntegerField()),
                ('current_progress', models.PositiveIntegerField(default=0)),
                ('date', models.DateField(default=datetime.date.today)),
                ('completed', models.BooleanField(default=False)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'goal_type', 'date')},
            },
        ),
        migrations.CreateModel(
            name='DailyGoalTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('goal_type', models.CharField(choices=[('xp', 'XP Points'), ('lessons', 'Lessons Completed'), ('flashcards', 'Flashcards Practiced'), ('minutes', 'Minutes Studied'), ('streak', 'Maintain Streak')], max_length=20)),
                ('target_value', models.PositiveIntegerField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'goal_type')},
            },
        ),
        migrations.CreateModel(
            name='UserEngagementMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=datetime.date.today)),
                ('lessons_completed', models.PositiveIntegerField(default=0)),
                ('flashcards_practiced', models.PositiveIntegerField(default=0)),
                ('minutes_studied', models.PositiveIntegerField(default=0)),
                ('xp_earned', models.PositiveIntegerField(default=0)),
                ('goals_completed', models.PositiveIntegerField(default=0)),
                ('sessions_count', models.PositiveIntegerField(default=0)),
                ('average_session_length', models.FloatField(default=0.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'date')},
            },
        ),
    ]
