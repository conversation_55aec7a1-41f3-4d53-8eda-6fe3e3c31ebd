{% load socialaccount %}
{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}TalonTalk{% endblock %}</title>
  
  <!-- Tailwind CSS CDN for complete coverage -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Backup: Load local CSS if available -->
  <link href="{% static 'css/output.css' %}" rel="stylesheet">
  
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'talon-blue': '#1e40af',
            'falcon-yellow': '#fbbf24',
          }
        }
      }
    }
  </script>
  
  <style>
    .animate-fade-in { animation: fadeIn 0.3s ease; }
    @keyframes fadeIn { from { opacity: 0; transform: translateY(20px);} to { opacity: 1; transform: none; } }
    
    /* Glassmorphism effect */
    .backdrop-blur-sm {
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }
    
    /* Custom gradient text */
    .bg-clip-text {
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  {% block navigation %}
  <!-- Default navigation for non-dashboard pages -->
  <nav class="bg-white shadow-sm border-b-2 border-falcon-yellow mb-0">
    <div class="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
      <a href="{% url 'landing' %}" class="text-2xl font-bold text-indigo-600 flex items-center">
        <span class="text-2xl mr-2">🦅</span> TalonTalk
      </a>
      <div>
        {% if user.is_authenticated %}
          <a href="{% url 'dashboard' %}" class="text-indigo-600 hover:text-indigo-800 px-3">Dashboard</a>
          <a href="{% url 'account_logout' %}" class="text-indigo-600 hover:text-indigo-800 px-3">Logout</a>
          <span class="text-gray-600 px-3">Welcome, {{ user.first_name|default:user.username }}!</span>
        {% else %}
          <a href="#" onclick="document.getElementById('auth-modal').classList.remove('hidden');showTab('login');return false;" class="text-indigo-600 hover:text-indigo-800 px-3">Sign In</a>
          <a href="#" onclick="document.getElementById('auth-modal').classList.remove('hidden');showTab('signup');return false;" class="text-indigo-600 hover:text-indigo-800 px-3">Sign Up</a>
        {% endif %}
      </div>
    </div>
  </nav>
  {% endblock %}
  <main class="mt-0">
    {% if messages %}
      {% for message in messages %}
        <div class="max-w-7xl mx-auto px-4 mb-4">
          <div class="bg-{% if message.tags == 'error' %}red{% else %}green{% endif %}-100 border border-{% if message.tags == 'error' %}red{% else %}green{% endif %}-400 text-{% if message.tags == 'error' %}red{% else %}green{% endif %}-700 px-4 py-3 rounded mb-4">
            {{ message }}
          </div>
        </div>
      {% endfor %}
    {% endif %}
    {% block content %}{% endblock %}
  </main>
  {% if not user.is_authenticated %}
    {% include 'account/auth_modal.html' %}
  {% endif %}
</body>
</html>
