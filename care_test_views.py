"""
C.A.R.E. Framework Test Views and Data
Provides sample data and handlers for each C.A.R.E. phase
"""

from django.http import JsonResponse
import json


def care_contextualize(request):
    """
    Contextualize phase: Set the learning scene with real-world scenarios
    """
    lesson_id = request.GET.get("lesson", "restaurant_ordering")

    contextualize_data = {
        "restaurant_ordering": {
            "scenario": {
                "title": "Ordering at a Spanish Restaurant",
                "description": "You're visiting Barcelona and want to try authentic Spanish cuisine. You've found a cozy local restaurant, but the menu is entirely in Spanish.",
                "image_url": "/static/images/spanish_restaurant.jpg",
                "location": "Barcelona, Spain",
                "situation": "Evening dinner at a traditional tapas restaurant",
            },
            "cultural_context": {
                "title": "Spanish Dining Culture",
                "facts": [
                    "Spanish people typically eat dinner very late, often after 9 PM",
                    "Tapas are small plates meant to be shared",
                    "It's common to stay at the table for hours, talking and enjoying food",
                    "Tipping is appreciated but not mandatory - 5-10% is typical",
                ],
            },
            "key_phrases": [
                {
                    "spanish": "¿Tienen mesa para dos?",
                    "english": "Do you have a table for two?",
                    "pronunciation": "tee-EH-nen MEH-sah PAH-rah dohs",
                },
                {
                    "spanish": "¿Qué me recomienda?",
                    "english": "What do you recommend?",
                    "pronunciation": "keh meh reh-koh-mee-EHN-dah",
                },
                {
                    "spanish": "La cuenta, por favor",
                    "english": "The bill, please",
                    "pronunciation": "lah KWEN-tah, pohr fah-VOHR",
                },
            ],
        }
    }

    return JsonResponse(
        contextualize_data.get(lesson_id, contextualize_data["restaurant_ordering"])
    )


def care_acquire(request):
    """
    Acquire phase: Learn new vocabulary and grammar structures
    """
    lesson_id = request.GET.get("lesson", "restaurant_ordering")

    acquire_data = {
        "restaurant_ordering": {
            "vocabulary": [
                {
                    "word": "restaurante",
                    "translation": "restaurant",
                    "pronunciation": "reh-stah-oo-RAHN-teh",
                    "example": "Vamos al restaurante",
                    "example_translation": "Let's go to the restaurant",
                },
                {
                    "word": "menú",
                    "translation": "menu",
                    "pronunciation": "meh-NOO",
                    "example": "¿Puedo ver el menú?",
                    "example_translation": "Can I see the menu?",
                },
                {
                    "word": "mesero/camarero",
                    "translation": "waiter",
                    "pronunciation": "meh-SEH-roh/kah-mah-REH-roh",
                    "example": "El mesero es muy amable",
                    "example_translation": "The waiter is very kind",
                },
                {
                    "word": "plato",
                    "translation": "dish/plate",
                    "pronunciation": "PLAH-toh",
                    "example": "Este plato está delicioso",
                    "example_translation": "This dish is delicious",
                },
                {
                    "word": "bebida",
                    "translation": "drink",
                    "pronunciation": "beh-BEE-dah",
                    "example": "¿Qué bebida quiere?",
                    "example_translation": "What drink do you want?",
                },
            ],
            "grammar": {
                "topic": "Polite Requests and Questions",
                "structures": [
                    {
                        "pattern": "¿Puedo + infinitive?",
                        "meaning": "Can I + verb?",
                        "examples": [
                            "¿Puedo ver el menú? (Can I see the menu?)",
                            "¿Puedo pagar con tarjeta? (Can I pay with card?)",
                        ],
                    },
                    {
                        "pattern": "Quisiera + noun/infinitive",
                        "meaning": "I would like + noun/to verb",
                        "examples": [
                            "Quisiera una mesa (I would like a table)",
                            "Quisiera ordenar (I would like to order)",
                        ],
                    },
                ],
            },
        }
    }

    return JsonResponse(
        acquire_data.get(lesson_id, acquire_data["restaurant_ordering"])
    )


def care_reinforce(request):
    """
    Reinforce phase: Practice through interactive exercises
    """
    lesson_id = request.GET.get("lesson", "restaurant_ordering")

    reinforce_data = {
        "restaurant_ordering": {
            "exercises": [
                {
                    "type": "multiple_choice",
                    "question": "How do you ask 'What do you recommend?' in Spanish?",
                    "options": [
                        "¿Qué me recomienda?",
                        "¿Cuánto cuesta?",
                        "¿Dónde está el baño?",
                        "¿Tienen mesa?",
                    ],
                    "correct": 0,
                    "explanation": "¿Qué me recomienda? is the polite way to ask for recommendations.",
                },
                {
                    "type": "translation",
                    "question": "Translate: 'I would like a table for two'",
                    "answer": "Quisiera una mesa para dos",
                    "alternatives": ["Quiero una mesa para dos"],
                    "explanation": "Quisiera is more polite than quiero (I want).",
                },
                {
                    "type": "pronunciation",
                    "phrase": "¿Tienen mesa para dos?",
                    "phonetic": "tee-EH-nen MEH-sah PAH-rah dohs",
                    "translation": "Do you have a table for two?",
                },
                {
                    "type": "dialogue_completion",
                    "scenario": "You're ordering food",
                    "dialogue": [
                        {"speaker": "waiter", "text": "¿Qué van a ordenar?"},
                        {
                            "speaker": "you",
                            "text": "_______",
                            "options": [
                                "Quisiera la paella, por favor",
                                "No me gusta la comida",
                                "¿Dónde está el baño?",
                            ],
                        },
                        {"speaker": "waiter", "text": "Excelente elección"},
                    ],
                    "correct": 0,
                },
            ]
        }
    }

    return JsonResponse(
        reinforce_data.get(lesson_id, reinforce_data["restaurant_ordering"])
    )


def care_extend(request):
    """
    Extend phase: Apply learning in broader contexts
    """
    lesson_id = request.GET.get("lesson", "restaurant_ordering")

    extend_data = {
        "restaurant_ordering": {
            "real_world_applications": [
                {
                    "title": "Role-Play: Dinner with Friends",
                    "description": "Practice ordering for a group at a Spanish restaurant",
                    "scenario": "You're the translator for your English-speaking friends visiting Madrid",
                    "tasks": [
                        "Ask for a table for 4 people",
                        "Order different dishes for each person",
                        "Ask about ingredients for someone with allergies",
                        "Request the bill and pay",
                    ],
                },
                {
                    "title": "Cultural Challenge",
                    "description": "Navigate Spanish dining customs",
                    "challenges": [
                        "Order appropriate tapas for sharing",
                        "Understand regional specialties",
                        "Practice dinner timing (after 9 PM)",
                        "Learn about Spanish wine culture",
                    ],
                },
            ],
            "expansion_topics": [
                {
                    "topic": "Food Types and Preparation",
                    "vocabulary": [
                        "asado",
                        "frito",
                        "al vapor",
                        "crudo",
                        "picante",
                        "dulce",
                    ],
                    "phrases": ["¿Cómo está preparado?", "¿Está muy picante?"],
                },
                {
                    "topic": "Dietary Restrictions",
                    "vocabulary": ["vegetariano", "vegano", "sin gluten", "alérgico"],
                    "phrases": ["Soy alérgico a...", "¿Tienen opciones vegetarianas?"],
                },
            ],
            "homework": {
                "title": "Practice Assignment",
                "description": "Find a Spanish restaurant menu online and practice ordering",
                "steps": [
                    "Choose 3 dishes you'd like to try",
                    "Practice pronouncing their names",
                    "Write out how you would order them politely",
                    "Research what drinks pair well with your choices",
                ],
            },
        }
    }

    return JsonResponse(extend_data.get(lesson_id, extend_data["restaurant_ordering"]))
