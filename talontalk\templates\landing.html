<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TalonTalk - Learn Languages Effectively with AI | AI-Powered Language Learning</title>
    <meta name="description" content="Learn languages more effectively with AI-powered personalized lessons. Join thousands of students improving their Spanish, French, German & more. Start your free trial today!">
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        },
                        accent: {
                            500: '#10b981',
                            600: '#059669'
                        }
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'bounce-gentle': 'bounceGentle 2s infinite',
                        'pulse-slow': 'pulse 3s infinite'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        @keyframes bounceGentle {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #f0f9ff 0%, #ecfdf5 100%);
        }
    </style>
</head>
<body class="font-sans antialiased">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-200 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="text-2xl font-bold gradient-text">TalonTalk</span>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#features" class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">Features</a>
                        <a href="#how-it-works" class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">How It Works</a>
                        <a href="#pricing" class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">Pricing</a>
                        <a href="#testimonials" class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors">Reviews</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="sign-in-btn" class="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium">Sign In</button>
                    <button id="start-trial-btn" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Start Free Trial
                    </button>
                </div>
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-600 hover:text-primary-600 p-2">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden md:hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
                    <a href="#features" class="block px-3 py-2 text-gray-600 hover:text-primary-600">Features</a>
                    <a href="#how-it-works" class="block px-3 py-2 text-gray-600 hover:text-primary-600">How It Works</a>
                    <a href="#pricing" class="block px-3 py-2 text-gray-600 hover:text-primary-600">Pricing</a>
                    <a href="#testimonials" class="block px-3 py-2 text-gray-600 hover:text-primary-600">Reviews</a>
                    <div class="px-3 py-2 space-y-2">
                        <button id="mobile-sign-in-btn" class="block w-full text-left text-gray-600 hover:text-primary-600">Sign In</button>
                        <button id="mobile-start-trial-btn" class="block w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Start Free Trial
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient pt-20 pb-16 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="animate-fade-in-up">
                    <!-- Urgency Badge -->
                    <div class="inline-flex items-center bg-accent-500/10 text-accent-600 px-4 py-2 rounded-full text-sm font-medium mb-6">
                        <span class="w-2 h-2 bg-accent-500 rounded-full mr-2 animate-pulse"></span>
                        New: Enhanced AI Learning Paths Now Available!
                    </div>
                    
                    <!-- Compelling Headline -->
                    <h1 class="text-4xl md:text-6xl font-bold text-gray-900 leading-tight mb-6">
                        Learn Languages 
                        <span class="gradient-text">Effectively</span> 
                        with AI
                    </h1>
                    
                    <!-- Engaging Body Text -->
                    <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                        Stop struggling with boring textbooks and generic lessons. Our AI creates personalized learning paths that adapt to your pace, helping you build confidence and real conversational skills at your own speed.
                    </p>
                    
                    <!-- Value Propositions -->
                    <div class="flex flex-wrap gap-4 mb-8">
                        <div class="flex items-center text-gray-700">
                            <svg class="w-5 h-5 text-accent-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            AI-Powered Personalization
                        </div>
                        <div class="flex items-center text-gray-700">
                            <svg class="w-5 h-5 text-accent-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            15+ Languages Available
                        </div>
                        <div class="flex items-center text-gray-700">
                            <svg class="w-5 h-5 text-accent-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            30-Day Money-Back Guarantee
                        </div>
                    </div>
                    
                    <!-- Strong CTA -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button id="main-cta" class="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105 shadow-lg hover:shadow-xl">
                            Start Your Free 7-Day Trial
                        </button>
                        <button id="demo-btn" class="border-2 border-gray-300 hover:border-primary-600 text-gray-700 hover:text-primary-600 px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
                            Watch Demo (2 min)
                        </button>
                    </div>
                    
                    <!-- Social Proof Numbers -->
                    <div class="flex items-center gap-8 mt-8 pt-8 border-t border-gray-200">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900">2,500+</div>
                            <div class="text-sm text-gray-600">Active Learners</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900">4.7/5</div>
                            <div class="text-sm text-gray-600">User Rating</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900">85%</div>
                            <div class="text-sm text-gray-600">Completion Rate</div>
                        </div>
                    </div>
                </div>
                
                <!-- Visual Appeal -->
                <div class="relative">
                    <div class="bg-white rounded-2xl shadow-2xl p-8 animate-bounce-gentle">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">Today's Lesson</h3>
                                <span class="bg-accent-500 text-white px-3 py-1 rounded-full text-sm">Spanish</span>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">Vocabulary: Food & Dining</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">Grammar: Present Tense</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <div class="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                                    </div>
                                    <span class="text-gray-700">Conversation Practice</span>
                                </div>
                            </div>
                            <div class="bg-gray-100 rounded-lg p-4 mt-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm text-gray-600">Progress</span>
                                    <span class="text-sm font-semibold text-primary-600">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-primary-600 h-2 rounded-full transition-all duration-1000" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Floating elements for visual appeal -->
                    <div class="absolute -top-4 -right-4 w-16 h-16 bg-accent-500 rounded-full opacity-20 animate-pulse-slow"></div>
                    <div class="absolute -bottom-4 -left-4 w-12 h-12 bg-primary-500 rounded-full opacity-20 animate-pulse-slow"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    How It Works - Simple as 1, 2, 3
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our AI analyzes your learning style and creates a personalized path to fluency
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center group">
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary-200 transition-colors">
                        <span class="text-2xl font-bold text-primary-600">1</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Take Assessment</h3>
                    <p class="text-gray-600">
                        Complete a 5-minute assessment to determine your current level and learning preferences
                    </p>
                </div>
                
                <div class="text-center group">
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary-200 transition-colors">
                        <span class="text-2xl font-bold text-primary-600">2</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Get Your Plan</h3>
                    <p class="text-gray-600">
                        Our AI creates a personalized curriculum tailored to your goals and schedule
                    </p>
                </div>
                
                <div class="text-center group">
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary-200 transition-colors">
                        <span class="text-2xl font-bold text-primary-600">3</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Start Learning</h3>
                    <p class="text-gray-600">
                        Practice daily with interactive lessons that adapt to your progress in real-time
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Why Choose TalonTalk?
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Advanced AI technology meets proven language learning methods
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">AI-Powered Personalization</h3>
                    <p class="text-gray-600">
                        Our advanced AI adapts to your learning style, pace, and preferences to maximize retention and engagement.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Interactive Conversations</h3>
                    <p class="text-gray-600">
                        Practice real conversations with AI tutors that provide instant feedback and pronunciation coaching.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Spaced Repetition</h3>
                    <p class="text-gray-600">
                        Scientifically-proven spaced repetition algorithm ensures you remember what you learn long-term.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Flexible Schedule</h3>
                    <p class="text-gray-600">
                        Learn at your own pace with lessons that fit your schedule. Just 15 minutes a day is enough to see progress.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Progress Tracking</h3>
                    <p class="text-gray-600">
                        Detailed analytics show your progress, strengths, and areas for improvement with actionable insights.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Community Support</h3>
                    <p class="text-gray-600">
                        Join a community of learners, participate in challenges, and get support from native speakers.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Proof / Testimonials -->
    <section id="testimonials" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    What Our Students Say
                </h2>
                <p class="text-xl text-gray-600">
                    Join thousands of successful language learners
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-gray-50 p-8 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="flex text-yellow-400">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-6">
                        "I went from zero Spanish to having conversations with locals in Barcelona in just 6 weeks. The AI really understands how I learn best!"
                    </p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                            <span class="text-primary-600 font-semibold">SM</span>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900">Sarah Martinez</div>
                            <div class="text-sm text-gray-600">Marketing Manager</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-8 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="flex text-yellow-400">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-6">
                        "As a busy professional, I needed something flexible. TalonTalk fits perfectly into my schedule and I can already hold basic conversations in French!"
                    </p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center mr-4">
                            <span class="text-accent-600 font-semibold">JD</span>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900">James Davis</div>
                            <div class="text-sm text-gray-600">Software Engineer</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-8 rounded-xl">
                    <div class="flex items-center mb-4">
                        <div class="flex text-yellow-400">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-6">
                        "I tried other apps but nothing worked like TalonTalk. The personalized approach made all the difference. Highly recommend!"
                    </p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                            <span class="text-purple-600 font-semibold">LW</span>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900">Lisa Wang</div>
                            <div class="text-sm text-gray-600">Teacher</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
          <p class="text-xl text-gray-600 mb-8">Flexible options for every learner</p>
          
          <!-- Monthly/Yearly Toggle -->
          <div class="flex items-center justify-center mb-8">
            <span class="text-gray-600 mr-3">Monthly</span>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="billing-toggle" class="sr-only peer" onchange="togglePricing()">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
            <span class="text-gray-600 ml-3">Yearly</span>
            <span class="ml-2 bg-accent-500 text-white px-2 py-1 rounded-full text-xs font-medium">Save 25%</span>
          </div>
        </div>
        <div class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <!-- Free Tier -->
          <div class="bg-white p-8 rounded-xl shadow-sm border border-gray-200 flex flex-col">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">TalonTalk (Free)</h3>
            <div class="text-3xl font-bold text-gray-900 mb-1">$0</div>
            <ul class="space-y-2 mb-4 text-left mt-4">
              <li>Full access to foundational lessons</li>
              <li>XP, Levels, Badges, Streak Tracking</li>
              <li>Interactive flashcards & quizzes</li>
            </ul>
            <div class="text-sm text-gray-500 mb-4">Limitations:</div>
            <ul class="space-y-2 mb-6 text-left">
              <li>Limited "Hearts" system</li>
              <li>Ad-supported</li>
            </ul>
            <button class="w-full border-2 border-gray-300 hover:border-primary-600 text-gray-700 hover:text-primary-600 py-3 rounded-lg font-semibold transition-colors mt-auto">Continue with Free</button>
          </div>
          <!-- AI Pro Tier -->
          <div class="bg-primary-600 p-8 rounded-xl shadow-lg border-2 border-primary-500 flex flex-col text-white relative">
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-accent-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
            </div>
            <h3 class="text-xl font-semibold mb-2 text-white">TalonTalk AI Pro</h3>
            <div class="text-3xl font-bold mb-1 text-white">
              <span id="pro-price">$12.99</span> 
              <span class="text-lg font-normal" id="pro-period">/mo</span>
            </div>
            <div class="text-gray-200" id="pro-yearly-note">or $99.99/year</div>
            <ul class="space-y-2 mb-4 text-left mt-4 text-white">
              <li>Everything in Free, plus:</li>
              <li>Unlimited Hearts</li>
              <li>Ad-free experience</li>
              <li>AI Practice Agent for conversational practice</li>
              <li>Pronunciation Assessment Reports powered by Deepgram</li>
              <li>Advanced personalized review</li>
            </ul>
            <div class="text-sm text-yellow-200 mb-4">Includes a 7-Day Free Trial – No credit card required.</div>
            <button class="w-full bg-accent-500 hover:bg-accent-600 text-white py-3 rounded-lg font-semibold transition-colors mt-auto">Start 7-Day Free Trial</button>
          </div>
          <!-- Plus Tier -->
          <div class="bg-white p-8 rounded-xl shadow-sm border border-gray-200 flex flex-col">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">TalonTalk Plus</h3>
            <div class="text-3xl font-bold text-gray-900 mb-1">
              <span id="plus-price">$29.99</span> 
              <span class="text-lg font-normal" id="plus-period">/mo</span>
            </div>
            <ul class="space-y-2 mb-4 text-left mt-4">
              <li>Everything in AI Pro, plus:</li>
              <li>Live AI Tutor (LLM-Powered) for open-ended conversation and grammar explanations</li>
              <li>Monthly token allowance for Live AI Tutor</li>
              <li>Ability to purchase more tokens</li>
            </ul>
            <button class="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-semibold transition-colors mt-auto">Upgrade to Plus</button>
          </div>
        </div>
      </div>
    </section>
    
    <!-- Marketplace Container -->
    <section class="py-16 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center border border-dashed border-primary-500 rounded-2xl shadow-md py-12">
        <div class="flex items-center justify-center gap-4 mb-4">
          <h2 class="text-2xl md:text-3xl font-bold text-gray-900">TalonTalk Live Marketplace</h2>
          <span class="inline-block bg-yellow-400 text-white px-3 py-1 rounded-full text-sm font-semibold">Coming Soon</span>
        </div>
        <p class="text-lg text-gray-700 mb-8">Practice one-on-one with vetted, native-speaking tutors. Pay per lesson, on your schedule.</p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button class="bg-accent-500 hover:bg-accent-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors">Notify Me When It's Live</button>
          <a href="#" class="inline-block border-2 border-primary-600 text-primary-600 hover:bg-primary-50 px-8 py-3 rounded-lg font-semibold transition-colors">Become a Tutor</a>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Frequently Asked Questions
                </h2>
                <p class="text-xl text-gray-600">
                    Everything you need to know about TalonTalk
                </p>
            </div>
            
            <div class="space-y-6" id="faq-accordion">
                <div class="bg-gray-50 rounded-lg p-6">
                    <button type="button" class="flex justify-between items-center w-full faq-toggle" aria-expanded="false">
                        <span class="text-lg font-semibold text-gray-900">Is TalonTalk really free?</span>
                        <svg class="w-5 h-5 text-primary-600 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div class="faq-content mt-2 text-gray-600 hidden">
                        Yes! You can start with a free plan and upgrade anytime. No credit card required for the trial.
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-6">
                    <button type="button" class="flex justify-between items-center w-full faq-toggle" aria-expanded="false">
                        <span class="text-lg font-semibold text-gray-900">What languages can I learn?</span>
                        <svg class="w-5 h-5 text-primary-600 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div class="faq-content mt-2 text-gray-600 hidden">
                        We support Spanish, French, German, and 12+ more languages. More are added regularly.
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-6">
                    <button type="button" class="flex justify-between items-center w-full faq-toggle" aria-expanded="false">
                        <span class="text-lg font-semibold text-gray-900">How is TalonTalk different from other apps?</span>
                        <svg class="w-5 h-5 text-primary-600 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div class="faq-content mt-2 text-gray-600 hidden">
                        TalonTalk uses advanced AI for personalized feedback, real conversation practice, and a gamified experience to keep you motivated.
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-6">
                    <button type="button" class="flex justify-between items-center w-full faq-toggle" aria-expanded="false">
                        <span class="text-lg font-semibold text-gray-900">Can I use TalonTalk on my phone?</span>
                        <svg class="w-5 h-5 text-primary-600 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div class="faq-content mt-2 text-gray-600 hidden">
                        Absolutely! Our platform is fully responsive and works on any device.
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-6">
                    <button type="button" class="flex justify-between items-center w-full faq-toggle" aria-expanded="false">
                        <span class="text-lg font-semibold text-gray-900">What if I don’t like it?</span>
                        <svg class="w-5 h-5 text-primary-600 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div class="faq-content mt-2 text-gray-600 hidden">
                        We offer a 30-day money-back guarantee on all paid plans. No questions asked.
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-6">
                    <button type="button" class="flex justify-between items-center w-full faq-toggle" aria-expanded="false">
                        <span class="text-lg font-semibold text-gray-900">How do I get started?</span>
                        <svg class="w-5 h-5 text-primary-600 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                    </button>
                    <div class="faq-content mt-2 text-gray-600 hidden">
                        Just click “Start Free Trial” and follow the simple sign-up process. You’ll be learning in minutes!
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-20 bg-primary-600">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Start Your Language Learning Journey?
            </h2>
            <p class="text-xl text-white mb-8 leading-relaxed">
                Join thousands of students who are building real language skills and confidence. Start your free trial today - no credit card required.
            </p>
            
            <!-- Email Signup Form -->
            <div class="max-w-md mx-auto mb-8">
                <form id="signup-form" class="flex flex-col sm:flex-row gap-3">
                    <input 
                        type="email" 
                        id="email-input"
                        placeholder="Enter your email address" 
                        required
                        class="flex-1 px-4 py-3 text-base rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-transparent"
                    >
                    <button 
                        type="submit"
                        class="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 text-base rounded-lg font-semibold transition-colors whitespace-nowrap"
                    >
                        Start Free Trial
                    </button>
                </form>
            </div>

            <div class="mt-4 flex flex-wrap gap-2 justify-center">
                <span class="inline-block bg-white/20 text-white px-3 py-1 rounded-full text-sm font-semibold">Free 7-day trial</span>
                <span class="inline-block bg-white/20 text-white px-3 py-1 rounded-full text-sm font-semibold">No credit card required</span>
                <span class="inline-block bg-white/20 text-white px-3 py-1 rounded-full text-sm font-semibold">Cancel anytime</span>
            </div>
            <div class="mt-4 flex flex-wrap gap-2 justify-center">
                <span class="inline-block bg-white/20 text-white px-3 py-1 rounded-full text-sm font-semibold">30-Day Money-Back Guarantee</span>
                <span class="inline-block bg-white/20 text-white px-3 py-1 rounded-full text-sm font-semibold">Cancel Anytime</span>
                <span class="inline-block bg-white/20 text-white px-3 py-1 rounded-full text-sm font-semibold">Instant Access</span>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-1">
                    <div class="flex items-center mb-4">
                        <span class="text-2xl font-bold gradient-text">TalonTalk</span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        AI-powered language learning that adapts to you. Master any language faster than ever before.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 7.392 0 9.142-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Product</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="#pricing" class="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Mobile App</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Languages</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">For Business</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Community</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">System Status</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Bug Reports</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Company</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Press</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    © 2024 TalonTalk. All rights reserved.
                </p>
                <div class="flex items-center space-x-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Terms</a>
                    <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- AUTH MODALS -->
    <div id="auth-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
      <div class="bg-white rounded-xl shadow-2xl w-full max-w-md p-8 relative">
        <button id="close-auth-modal" class="absolute top-4 right-4 text-gray-400 hover:text-gray-700 text-2xl">&times;</button>
        <div class="flex mb-6 border-b">
          <button id="tab-signup" class="flex-1 py-2 text-center font-semibold text-primary-600 border-b-2 border-primary-600">Sign Up</button>
          <button id="tab-login" class="flex-1 py-2 text-center font-semibold text-gray-500 border-b-2 border-transparent">Log In</button>
        </div>
        <form id="signup-form-modal" class="space-y-4">
          <input type="text" id="signup-username" placeholder="Username" required class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-accent-500">
          <input type="email" id="signup-email" placeholder="Email" required class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-accent-500">
          <input type="password" id="signup-password" placeholder="Password" required class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-accent-500">
          <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-semibold transition-colors">Create Account</button>
        </form>
        <form id="login-form-modal" class="space-y-4 hidden">
          <input type="text" id="login-username" placeholder="Username" required class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-accent-500">
          <input type="password" id="login-password" placeholder="Password" required class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-accent-500">
          <div class="mt-2 text-center">
            <a href="#" id="forgot-password-link" class="text-blue-600 hover:underline text-sm">Forgot Password?</a>
          </div>
          <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-semibold transition-colors">Log In</button>
        </form>
        <!-- Social Auth Buttons (no form wrappers) -->
        <div class="flex flex-col gap-3 mt-6">
          <button id="google-auth-btn" type="button" class="w-full flex items-center justify-center gap-2 border border-gray-300 rounded-lg py-2 hover:bg-gray-50">
            <img src="https://www.svgrepo.com/show/475656/google-color.svg" alt="Google" class="w-5 h-5" onerror="this.style.display='none'"> Continue with Google
          </button>
        </div>
        <div id="auth-modal-message" class="mt-4 text-center text-sm"></div>
      </div>
    </div>

    <!-- Password Reset Modal -->
    <div id="password-reset-modal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
      <div class="bg-white rounded-xl shadow-2xl w-full max-w-md p-8 relative">
        <button id="close-password-reset" class="absolute top-4 right-4 text-gray-400 hover:text-gray-700 text-2xl">&times;</button>
        <h2 class="text-xl font-bold mb-4">Reset Your Password</h2>
        <form id="password-reset-form">
          <label for="reset-email" class="block mb-2">Email Address</label>
          <input type="email" id="reset-email" name="email" required class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-accent-500 mb-4" placeholder="Enter your email">
          <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-lg font-semibold transition-colors">Send Reset Link</button>
          <div id="password-reset-message" class="mt-4 text-sm"></div>
        </form>
      </div>
    </div>

    <!-- TypeScript/JavaScript -->
    <script>
    // --- Setup Main Application Logic ---
    document.addEventListener('DOMContentLoaded', function() {

        // 1. Mobile Menu Toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenuButton?.addEventListener('click', () => {
            mobileMenu?.classList.toggle('hidden');
        });

        // 2. Smooth Scrolling for Anchor Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
        
        // 3. FAQ Accordion
        document.querySelectorAll('.faq-toggle').forEach(button => {
            button.addEventListener('click', () => {
                const content = button.nextElementSibling;
                const isExpanded = button.getAttribute('aria-expanded') === 'true';
                
                // Close all others
                document.querySelectorAll('.faq-content').forEach(item => item.classList.add('hidden'));
                document.querySelectorAll('.faq-toggle').forEach(item => item.setAttribute('aria-expanded', 'false'));
                
                if (!isExpanded) {
                    content.classList.remove('hidden');
                    button.setAttribute('aria-expanded', 'true');
                }
            });
        });

        // --- Authentication Modal Logic (robust, deduped) ---
    const authModal = document.getElementById('auth-modal');
    const closeAuthModalBtn = document.getElementById('close-auth-modal');
    const tabSignup = document.getElementById('tab-signup');
    const tabLogin = document.getElementById('tab-login');
    const signupFormModal = document.getElementById('signup-form-modal');
    const loginFormModal = document.getElementById('login-form-modal');
    const authModalMessage = document.getElementById('auth-modal-message');

    function openAuthModal(isSignup) {
        if (!authModal) return;
        authModal.classList.remove('hidden');
        if (isSignup) {
            tabSignup.classList.add('text-primary-600', 'border-primary-600');
            tabSignup.classList.remove('text-gray-500', 'border-transparent');
            tabLogin.classList.add('text-gray-500', 'border-transparent');
            tabLogin.classList.remove('text-primary-600', 'border-primary-600');
            signupFormModal.classList.remove('hidden');
            loginFormModal.classList.add('hidden');
        } else {
            tabLogin.classList.add('text-primary-600', 'border-primary-600');
            tabLogin.classList.remove('text-gray-500', 'border-transparent');
            tabSignup.classList.add('text-gray-500', 'border-transparent');
            tabSignup.classList.remove('text-primary-600', 'border-primary-600');
            loginFormModal.classList.remove('hidden');
            signupFormModal.classList.add('hidden');
        }
        authModalMessage.textContent = '';
    }
    // Attach modal openers
    document.getElementById('start-trial-btn')?.addEventListener('click', () => openAuthModal(true));
    document.getElementById('main-cta')?.addEventListener('click', () => openAuthModal(true));
    document.getElementById('sign-in-btn')?.addEventListener('click', () => openAuthModal(false));
    document.getElementById('mobile-start-trial-btn')?.addEventListener('click', () => openAuthModal(true));
    document.getElementById('mobile-sign-in-btn')?.addEventListener('click', () => openAuthModal(false));
    // Modal close and tab switching
    closeAuthModalBtn?.addEventListener('click', () => authModal.classList.add('hidden'));
    tabSignup?.addEventListener('click', () => openAuthModal(true));
    tabLogin?.addEventListener('click', () => openAuthModal(false));

    // --- Login Form AJAX Handler (deduped, robust) ---
    if (loginFormModal) {
      loginFormModal.addEventListener('submit', async function(e) {
        e.preventDefault();
        authModalMessage.textContent = '';
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;
        try {
          const res = await fetch('/accounts/login/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'X-Requested-With': 'XMLHttpRequest',
            },
            body: `login=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&csrfmiddlewaretoken=${getCSRFToken()}`
          });
          if (res.ok) {
            localStorage.setItem('talontalk_onboarded', '1');
            window.location.href = '/onboarding/';
            return;
          } else {
            const text = await res.text();
            if (text.includes('Please enter a correct')) {
              authModalMessage.textContent = 'Invalid username or password.';
            } else {
              authModalMessage.textContent = 'Login failed.';
            }
            authModalMessage.className = 'mt-4 text-center text-red-600';
          }
        } catch (err) {
          authModalMessage.textContent = 'Network error.';
          authModalMessage.className = 'mt-4 text-center text-red-600';
        }
      });
    }
    // Helper to get CSRF token from cookie
    function getCSRFToken() {
      const name = 'csrftoken';
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const c = cookies[i].trim();
        if (c.startsWith(name + '=')) {
          return c.substring(name.length + 1);
        }
      }
      return '';
    }

        // --- Social Auth Button Handler (popup, center on screen) ---
        document.getElementById('google-auth-btn').onclick = function(e) {
          e.preventDefault();
          const w = 500, h = 600;
          const left = window.screenX + (window.outerWidth - w) / 2;
          const top = window.screenY + (window.outerHeight - h) / 2;
          window.open(
            'https://accounts.google.com/o/oauth2/v2/auth/oauthchooseaccount?client_id=************-e4nbe8e9g0vvau54k21ct9vnil03gqi5.apps.googleusercontent.com&redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Faccounts%2Fgoogle%2Flogin%2Fcallback%2F&scope=email%20profile&response_type=code&state=GO3cSOACGIzGg6JQ&access_type=online&service=lso&o2v=2&flowName=GeneralOAuthFlow',
            'GoogleLogin',
            `width=${w},height=${h},top=${top},left=${left},resizable,scrollbars=yes,status=1`
          );
        };

        // --- Automatic Google OAuth Nudge (top right) ---
        setTimeout(function() {
          // Only nudge if not already authenticated (simple check, can be improved)
          if (!localStorage.getItem('talontalk_token') && !sessionStorage.getItem('talontalk_google_nudge')) {
            const w = 500, h = 600;
            const left = window.screenX + window.outerWidth - w - 20;
            const top = window.screenY + 20;
            window.open(
              'https://accounts.google.com/o/oauth2/v2/auth/oauthchooseaccount?client_id=************-e4nbe8e9g0vvau54k21ct9vnil03gqi5.apps.googleusercontent.com&redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Faccounts%2Fgoogle%2Flogin%2Fcallback%2F&scope=email%20profile&response_type=code&state=GO3cSOACGIzGg6JQ&access_type=online&service=lso&o2v=2&flowName=GeneralOAuthFlow',
              'GoogleLoginNudge',
              `width=${w},height=${h},top=${top},left=${left},resizable,scrollbars=yes,status=1`
            );
            sessionStorage.setItem('talontalk_google_nudge', '1');
          }
        }, 8000); // 8 seconds after page load

        // --- Other Page Logic (add back countdown, form logic, etc. as needed) ---
    });

    // --- Pricing Toggle Function ---
    function togglePricing() {
        const toggle = document.getElementById('billing-toggle');
        const isYearly = toggle.checked;
        
        const proPrice = document.getElementById('pro-price');
        const proPeriod = document.getElementById('pro-period');
        const proYearlyNote = document.getElementById('pro-yearly-note');
        
        const plusPrice = document.getElementById('plus-price');
        const plusPeriod = document.getElementById('plus-period');
        
        if (isYearly) {
            // Show yearly prices
            proPrice.textContent = '$99.99';
            proPeriod.textContent = '/year';
            proYearlyNote.textContent = 'Save $55.89 vs monthly';
            
            plusPrice.textContent = '$269.99';
            plusPeriod.textContent = '/year';
        } else {
            // Show monthly prices
            proPrice.textContent = '$12.99';
            proPeriod.textContent = '/mo';
            proYearlyNote.textContent = 'or $99.99/year';
            
            plusPrice.textContent = '$29.99';
            plusPeriod.textContent = '/mo';
        }
    }
    
    // Make function globally available
    window.togglePricing = togglePricing;
    </script>
</body>
</html>