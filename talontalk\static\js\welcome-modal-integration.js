/**
 * Welcome Modal Integration Script
 * Handles automatic display of welcome modal on login and daily visits
 */

class WelcomeModalIntegration {
    constructor() {
        this.modalShown = false;
        this.init();
    }

    async init() {
        // Check if we should show the welcome modal
        const shouldShow = await this.checkShouldShowModal();

        if (shouldShow) {
            // Wait for the modal component to be ready
            this.waitForModal(() => {
                this.showWelcomeModal();
            });
        }

        // Set up event listeners for the modal
        this.setupModalEventListeners();
    }

    async checkShouldShowModal() {
        try {
            const response = await fetch('/api/check-welcome-modal/', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            });

            if (response.ok) {
                const data = await response.json();

                // Store user context for the modal
                if (data.user_context) {
                    window.userModalContext = data.user_context;
                }

                return data.show_modal;
            }
        } catch (error) {
            console.warn('Could not check welcome modal status:', error);

            // Fallback: check if user just landed on dashboard
            const referrer = document.referrer;
            const currentUrl = window.location.href;

            // Show modal if coming from login/signup pages
            if (referrer.includes('/accounts/login/') ||
                referrer.includes('/accounts/signup/') ||
                sessionStorage.getItem('justLoggedIn') === 'true') {

                sessionStorage.removeItem('justLoggedIn');
                return true;
            }
        }

        return false;
    }

    waitForModal(callback, maxAttempts = 50) {
        let attempts = 0;

        const checkForModal = () => {
            if (window.enhancedWelcomeModal || document.getElementById('welcome-modal')) {
                callback();
                return;
            }

            attempts++;
            if (attempts < maxAttempts) {
                setTimeout(checkForModal, 100);
            } else {
                console.warn('Welcome modal component not found');
            }
        };

        checkForModal();
    }

    showWelcomeModal() {
        if (this.modalShown) return;

        this.modalShown = true;

        // Try to use the enhanced modal first
        if (window.enhancedWelcomeModal) {
            window.enhancedWelcomeModal.show();
        } else if (window.welcomeModal) {
            // Fallback to basic modal
            window.welcomeModal.show();
        } else {
            // Manual trigger
            const modal = document.getElementById('welcome-modal');
            if (modal) {
                modal.classList.remove('hidden');
                modal.classList.add('show');
            }
        }

        // Track modal display
        this.trackModalDisplay();
    }

    setupModalEventListeners() {
        // Listen for successful content preloading
        window.addEventListener('startLearningWithContent', (event) => {
            this.handleLearningStart(event.detail);
        });

        // Listen for practice start events
        window.addEventListener('startLearning', () => {
            this.navigateToPractice();
        });

        // Listen for modal completion
        window.addEventListener('welcomeModalCompleted', (event) => {
            this.handleModalCompletion(event.detail);
        });
    }

    handleLearningStart(preloadedContent) {
        console.log('Starting learning with preloaded content:', preloadedContent);

        // Store content for practice page
        if (preloadedContent) {
            sessionStorage.setItem('preloadedLessons', JSON.stringify({
                content: preloadedContent,
                timestamp: Date.now(),
                date: new Date().toDateString()
            }));
        }

        this.navigateToPractice();
    }

    navigateToPractice() {
        // Navigate to the C.A.R.E. learning system
        // You can customize this URL based on your app structure
        const practiceUrl = '/care/lesson/';

        // Add a flag to indicate we have preloaded content
        const url = new URL(practiceUrl, window.location.origin);
        if (sessionStorage.getItem('preloadedLessons')) {
            url.searchParams.set('preloaded', 'true');
        }

        window.location.href = url.toString();
    }

    handleModalCompletion(result) {
        console.log('Welcome modal completed:', result);

        // Track user choice
        if (result && result.action) {
            this.trackUserChoice(result.action);
        }

        // Update user preferences if provided
        if (result && result.preferences) {
            this.updateUserPreferences(result.preferences);
        }
    }

    trackModalDisplay() {
        // Track that the modal was shown (for analytics)
        try {
            // You can integrate with your analytics service here
            console.log('Welcome modal displayed for user');

            // Update local storage to prevent showing again today
            localStorage.setItem('lastWelcomeShown', new Date().toDateString());
        } catch (error) {
            console.warn('Could not track modal display:', error);
        }
    }

    trackUserChoice(action) {
        // Track user interaction with modal
        try {
            console.log('User chose:', action);

            // Store preference for future sessions
            if (action === 'skip') {
                localStorage.setItem('welcomeModalSkipped', 'true');
            } else if (action === 'start_learning') {
                localStorage.setItem('welcomeModalUsed', 'true');
            }
        } catch (error) {
            console.warn('Could not track user choice:', error);
        }
    }

    async updateUserPreferences(preferences) {
        // Update user preferences on the server
        try {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            const response = await fetch('/api/profiles/preferences/', {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                },
                credentials: 'same-origin',
                body: JSON.stringify(preferences)
            });

            if (response.ok) {
                console.log('User preferences updated');
            }
        } catch (error) {
            console.warn('Could not update user preferences:', error);
        }
    }

    // Public method to manually trigger the modal
    static show() {
        const integration = new WelcomeModalIntegration();
        integration.showWelcomeModal();
    }

    // Public method to check if content is preloaded
    static getPreloadedContent() {
        try {
            const stored = sessionStorage.getItem('preloadedLessons');
            if (!stored) return null;

            const data = JSON.parse(stored);
            const today = new Date().toDateString();

            // Check if content is from today
            if (data.date === today) {
                return data.content;
            } else {
                // Clean up old content
                sessionStorage.removeItem('preloadedLessons');
                return null;
            }
        } catch (error) {
            console.warn('Error reading preloaded content:', error);
            return null;
        }
    }
}

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', () => {
    new WelcomeModalIntegration();
});

// Export for global access
window.WelcomeModalIntegration = WelcomeModalIntegration;
