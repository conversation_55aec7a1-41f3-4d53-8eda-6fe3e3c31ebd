/**
 * Type definitions for the TalonTalk C.A.R.E. Framework
 */
export type PhaseType = 'contextualize' | 'acquire' | 'reinforce' | 'extend';
export type ExerciseType = 'multiple_choice' | 'translation' | 'pronunciation' | 'conversation';
export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';
export interface APIResponse<T = unknown> {
    success: boolean;
    content?: T;
    error?: string;
    message?: string;
}
export interface Scenario {
    title: string;
    description: string;
    location?: string;
    setting?: string;
}
export interface CulturalContext {
    title: string;
    facts: string[];
    tips?: string[];
}
export interface KeyPhrase {
    spanish: string;
    english: string;
    pronunciation: string;
    usage?: string;
}
export interface VocabularyItem {
    word: string;
    translation: string;
    pronunciation: string;
    example: string;
    example_translation: string;
    part_of_speech?: string;
    difficulty?: DifficultyLevel;
}
export interface GrammarStructure {
    pattern: string;
    meaning: string;
    examples: string[];
    usage_notes?: string[];
}
export interface Grammar {
    topic: string;
    structures: GrammarStructure[];
    key_points?: string[];
}
export interface MultipleChoiceExercise {
    type: 'multiple_choice';
    question: string;
    options: string[];
    correct: number;
    explanation: string;
    hint?: string;
}
export interface TranslationExercise {
    type: 'translation';
    question: string;
    answer: string;
    explanation: string;
    hint?: string;
    alternative_answers?: string[];
}
export interface PronunciationExercise {
    type: 'pronunciation';
    phrase: string;
    translation: string;
    phonetic: string;
    audio_url?: string;
}
export interface ConversationExercise {
    type: 'conversation';
    scenario: string;
    participants: string[];
    exchanges: Array<{
        speaker: string;
        text: string;
        translation?: string;
    }>;
    user_responses: string[];
}
export type Exercise = MultipleChoiceExercise | TranslationExercise | PronunciationExercise | ConversationExercise;
export interface ContextualizeContent {
    scenario: Scenario;
    cultural_context: CulturalContext;
    key_phrases: KeyPhrase[];
    learning_objectives?: string[];
}
export interface AcquireContent {
    vocabulary: VocabularyItem[];
    grammar: Grammar;
    focus_areas?: string[];
}
export interface ReinforceContent {
    exercises: Exercise[];
    practice_goals?: string[];
    difficulty_progression?: DifficultyLevel[];
}
export interface RealWorldApplication {
    title: string;
    description: string;
    scenario: string;
    tasks?: string[];
}
export interface ExpansionTopic {
    topic: string;
    vocabulary: string[];
    phrases: string[];
    cultural_notes?: string[];
}
export interface Homework {
    title: string;
    description: string;
    steps: string[];
    estimated_time?: string;
    resources?: string[];
}
export interface ExtendContent {
    real_world_applications: RealWorldApplication[];
    expansion_topics: ExpansionTopic[];
    homework?: Homework;
    next_lesson_preview?: string;
}
export interface PhaseNavigationEvent {
    type: 'phase_navigation';
    from_phase: PhaseType;
    to_phase: PhaseType;
    timestamp: Date;
}
export interface ExerciseCompletionEvent {
    type: 'exercise_completion';
    exercise_type: ExerciseType;
    correct: boolean;
    time_taken: number;
    timestamp: Date;
}
export interface ProgressUpdateEvent {
    type: 'progress_update';
    phase: PhaseType;
    progress_percentage: number;
    timestamp: Date;
}
export type CAREEvent = PhaseNavigationEvent | ExerciseCompletionEvent | ProgressUpdateEvent;
export interface CARELessonState {
    currentPhase: PhaseType;
    phaseProgress: number;
    totalPhases: number;
    phases: PhaseType[];
    lessonId?: string;
    userId?: string;
    isLoading: boolean;
    error?: string;
}
export interface AITutorState {
    isOpen: boolean;
    messages: Array<{
        id: string;
        sender: 'user' | 'ai';
        content: string;
        timestamp: Date;
    }>;
    isTyping: boolean;
}
export interface CAREConfig {
    apiBaseUrl: string;
    enableAnalytics: boolean;
    enableAITutor: boolean;
    autoSaveProgress: boolean;
    transitionDuration: number;
    maxRetries: number;
}
export declare class CAREError extends Error {
    readonly code: string;
    readonly phase?: PhaseType | undefined;
    readonly cause?: unknown | undefined;
    constructor(message: string, code: string, phase?: PhaseType | undefined, cause?: unknown | undefined);
}
export declare class APIError extends CAREError {
    readonly statusCode: number;
    readonly endpoint: string;
    constructor(message: string, statusCode: number, endpoint: string, cause?: unknown);
}
export declare class ValidationError extends CAREError {
    readonly field: string;
    constructor(message: string, field: string, cause?: unknown);
}
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type EventHandler<T extends CAREEvent> = (event: T) => void | Promise<void>;
export type PhaseContent = ContextualizeContent | AcquireContent | ReinforceContent | ExtendContent;
export interface CAREElements {
    progressBar: HTMLElement;
    phaseContainer: HTMLElement;
    aiTutorModal: HTMLElement;
    navigationItems: NodeListOf<HTMLElement>;
    phaseIndicators: NodeListOf<HTMLElement>;
}
export interface AnimationConfig {
    duration: number;
    easing: string;
    delay?: number;
}
export type AnimationType = 'fadeIn' | 'fadeOut' | 'slideIn' | 'slideOut' | 'bounce';
//# sourceMappingURL=care.types.d.ts.map