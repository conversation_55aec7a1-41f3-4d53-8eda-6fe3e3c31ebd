from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import SessionAuthentication, TokenAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.utils import timezone
from django.contrib.auth.decorators import login_required
from django.core.cache import cache
import json
import uuid
import logging
import random
from datetime import timedelta
from .models import Badge, Achievement, Streak, Level
from .serializers import (
    BadgeSerializer,
    AchievementSerializer,
    StreakSerializer,
    LevelSerializer,
)

# Import new services
from .services import GamificationService
from lessons.content_pipeline import content_pipeline
from lessons.adaptive_engine import adaptive_engine

# Try to import AI services
try:
    from ai_services.llm_flashcards import (
        LLMFlashcardService,
        FlashcardRequest,
        DifficultyLevel,
        ExerciseType,
    )

    AI_SERVICE_AVAILABLE = True
except ImportError as e:
    logging.warning(f"AI services not available: {e}")
    AI_SERVICE_AVAILABLE = False

# Try to import advanced preloading services
try:
    from lessons.preloading_service import content_preloader
    from lessons.models import (
        UserLearningProfile,
        UserLessonQueue,
        ContentItem,
        UserContentPerformance,
    )

    PRELOADING_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Preloading services not available: {e}")
    PRELOADING_AVAILABLE = False

logger = logging.getLogger(__name__)


class BadgeViewSet(viewsets.ModelViewSet):
    queryset = Badge.objects.all()
    serializer_class = BadgeSerializer
    permission_classes = [AllowAny]


class AchievementViewSet(viewsets.ModelViewSet):
    queryset = Achievement.objects.all()
    serializer_class = AchievementSerializer
    permission_classes = [AllowAny]


class StreakViewSet(viewsets.ModelViewSet):
    queryset = Streak.objects.all()
    serializer_class = StreakSerializer
    permission_classes = [AllowAny]


class LevelViewSet(viewsets.ModelViewSet):
    queryset = Level.objects.all()
    serializer_class = LevelSerializer
    permission_classes = [AllowAny]


@api_view(["GET"])
@permission_classes([AllowAny])  # Allow access without authentication for now
def generate_flashcard(request):
    """Serve preloaded/cached flashcards instantly if available, else generate via LLM."""
    logger.info(
        f"Lesson/flashcard request from user: {request.user if request.user.is_authenticated else 'anonymous'}"
    )

    # Get user preferences from query parameters or set defaults
    language = request.GET.get("language", "spanish")
    difficulty = request.GET.get("difficulty", "beginner")
    exercise_type = request.GET.get("type", "multiple_choice")
    topic = request.GET.get("topic", None)
    lesson_length = int(request.GET.get("lesson_length", 5))

    # 🚀 FAST CACHE: Check memory cache first for instant response
    cache_key = f"flashcard_{language}_{difficulty}_{exercise_type}_v4"  # Changed to v4 after fixing LLM validation
    cached_flashcard = cache.get(cache_key)

    if cached_flashcard:
        logger.info(f"🚀 Serving cached flashcard for {cache_key}")
        logger.info(f"🚀 Cached flashcard: {cached_flashcard}")  # Debug log
        return JsonResponse(
            {"success": True, "flashcard": cached_flashcard, "cached": True}
        )

    # Try to serve preloaded/cached flashcards for instant response
    if PRELOADING_AVAILABLE:
        preload_filter = {
            "content_type": "flashcard",
            "language": language,
            "difficulty": difficulty,
            "status": "ready",
            "expires_at__gt": timezone.now(),
        }
        if topic:
            preload_filter["topic"] = topic
        # Prefer user-specific cache if authenticated, else global
        # Try to get content from user's lesson queue
        if request.user.is_authenticated:
            active_queue = UserLessonQueue.objects.filter(
                user=request.user, status="active", expires_at__gt=timezone.now()
            ).first()

            if active_queue:
                content_ids = active_queue.get_next_content_ids(count=1)
                if content_ids:
                    content_item = ContentItem.objects.filter(id=content_ids[0]).first()
                    if content_item:
                        flashcard_data = {
                            "id": content_item.id,
                            "question": content_item.question_text,
                            "correct_answer": content_item.answer_text,
                            "question_type": content_item.type,
                            "options": (
                                content_item.choices_list
                                if content_item.type == "mcq"
                                else None
                            ),
                            "hint": content_item.hint_text,
                            "explanation": content_item.explanation_text,
                        }

                        # Validate database flashcard options for multiple choice
                        if content_item.type == "mcq" and flashcard_data["options"]:
                            correct_answer = (
                                flashcard_data["correct_answer"].strip().lower()
                            )
                            options_lower = [
                                opt.strip().lower() for opt in flashcard_data["options"]
                            ]

                            if correct_answer not in options_lower:
                                logger.warning(
                                    f"Database flashcard validation: correct answer '{flashcard_data['correct_answer']}' not in options {flashcard_data['options']}"
                                )
                                # Replace a random option with the correct answer
                                import random

                                random_index = random.randint(
                                    0, len(flashcard_data["options"]) - 1
                                )
                                flashcard_data["options"][random_index] = (
                                    flashcard_data["correct_answer"]
                                )
                                logger.info(
                                    f"Fixed database flashcard options: now includes correct answer"
                                )

                        # Advance queue position
                        active_queue.advance_position(1)

                        # Cache the flashcard for next time
                        cache.set(cache_key, flashcard_data, timeout=300)  # 5 minutes

                        return JsonResponse(
                            {"success": True, "flashcard": flashcard_data}
                        )

        # If no preloaded content available, fall back to AI generation

        # If no preloaded content, fall back to LLM generation
    try:
        if AI_SERVICE_AVAILABLE:
            llm_service = LLMFlashcardService()
            lesson_context = (
                f"Generate a lesson of {lesson_length} {difficulty} level {exercise_type} flashcards for learning {language}. "
                f"Each flashcard should be unique, cover different vocabulary or grammar points, and include question, options (if applicable), correct answer, hint, explanation, example sentence, and pronunciation guide. "
            )
            if topic:
                lesson_context += f"The lesson should focus on the topic: {topic}. "
            lesson_context += (
                "Return the lesson as a list of flashcards in JSON format."
            )
            flashcard_request = FlashcardRequest(
                language="english",  # User's native language
                target_language=language,
                difficulty=DifficultyLevel(difficulty.lower()),
                exercise_type=ExerciseType(exercise_type.lower()),
                grammar_topic=topic,
                context=lesson_context,
            )
            flashcard_responses = llm_service.generate_lesson(
                flashcard_request, lesson_length=lesson_length
            )
            flashcards = []
            for resp in flashcard_responses:
                flashcard_data = {
                    "id": str(uuid.uuid4()),
                    "question": resp.question,
                    "question_type": exercise_type,
                    "options": resp.options or [],
                    "correct_answer": resp.correct_answer,
                    "hint": resp.hint,
                    "explanation": resp.explanation,
                    "example_sentence": getattr(resp, "example_sentence", ""),
                    "pronunciation_guide": getattr(resp, "pronunciation_guide", ""),
                    "difficulty": difficulty,
                    "language": language,
                    "ai_generated": True,
                }

                # Validate multiple choice options
                if exercise_type == "multiple_choice" and flashcard_data["options"]:
                    correct_answer = flashcard_data["correct_answer"].strip().lower()
                    options_lower = [
                        opt.strip().lower() for opt in flashcard_data["options"]
                    ]

                    if correct_answer not in options_lower:
                        logger.warning(
                            f"Fixing flashcard: correct answer '{flashcard_data['correct_answer']}' not in options {flashcard_data['options']}"
                        )
                        # Replace a random option with the correct answer
                        import random

                        random_index = random.randint(
                            0, len(flashcard_data["options"]) - 1
                        )
                        flashcard_data["options"][random_index] = flashcard_data[
                            "correct_answer"
                        ]
                        logger.info(f"Fixed options: now includes correct answer")

                flashcards.append(flashcard_data)
            # Check if we got valid multiple choice options for multiple choice questions
            if exercise_type == "multiple_choice":
                valid_flashcards = [
                    fc
                    for fc in flashcards
                    if fc.get("options") and len(fc["options"]) > 0
                ]
                if not valid_flashcards:
                    logger.warning(
                        "AI generated flashcards but no valid multiple choice options, falling back to demo"
                    )
                else:
                    # 🚀 CACHE the AI-generated flashcard for instant future responses
                    cache.set(cache_key, valid_flashcards[0], timeout=600)  # 10 minutes
                    return Response(
                        {
                            "success": True,
                            "flashcard": valid_flashcards[0],
                            "flashcards": valid_flashcards,
                            "cached": False,
                        }
                    )
            else:
                if flashcards:
                    # 🚀 CACHE the AI-generated flashcard for instant future responses
                    cache.set(cache_key, flashcards[0], timeout=600)  # 10 minutes
                    return Response(
                        {
                            "success": True,
                            "flashcard": flashcards[0],
                            "flashcards": flashcards,
                            "cached": False,
                        }
                    )
                else:
                    return Response(
                        {"success": False, "error": "No flashcards generated"},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
    except Exception as ai_error:
        logger.warning(f"AI service failed, falling back to demo: {ai_error}")
    # Fallback demo lesson (sequence of flashcards with variety)
    demo_flashcards = [
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Hello" en español?',
            "question_type": "multiple_choice",
            "options": ["Hola", "Adiós", "Gracias", "Por favor"],
            "correct_answer": "Hola",
            "hint": "It's a common greeting.",
            "explanation": '"Hola" means "Hello" in Spanish.',
            "example_sentence": "Hola, ¿cómo estás?",
            "pronunciation_guide": "OH-lah",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Thank you" en español?',
            "question_type": "multiple_choice",
            "options": ["Hola", "Adiós", "Gracias", "Por favor"],
            "correct_answer": "Gracias",
            "hint": "It's used to show appreciation.",
            "explanation": '"Gracias" means "Thank you" in Spanish.',
            "example_sentence": "Gracias por tu ayuda.",
            "pronunciation_guide": "GRAH-see-ahs",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Goodbye" en español?',
            "question_type": "multiple_choice",
            "options": ["Hola", "Adiós", "Gracias", "Por favor"],
            "correct_answer": "Adiós",
            "hint": "It's used when leaving.",
            "explanation": '"Adiós" means "Goodbye" in Spanish.',
            "example_sentence": "Adiós, nos vemos mañana.",
            "pronunciation_guide": "ah-DYOHS",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Please" en español?',
            "question_type": "multiple_choice",
            "options": ["Hola", "Adiós", "Gracias", "Por favor"],
            "correct_answer": "Por favor",
            "hint": "It's used to make polite requests.",
            "explanation": '"Por favor" means "Please" in Spanish.',
            "example_sentence": "Por favor, ayúdame.",
            "pronunciation_guide": "por fah-VOHR",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
    ]

    # Add more variety to demo flashcards
    extra_demo_flashcards = [
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Good morning" en español?',
            "question_type": "multiple_choice",
            "options": ["Buenas noches", "Buenos días", "Buenas tardes", "Hasta luego"],
            "correct_answer": "Buenos días",
            "hint": "It's used in the morning.",
            "explanation": '"Buenos días" means "Good morning" in Spanish.',
            "example_sentence": "Buenos días, ¿cómo está usted?",
            "pronunciation_guide": "BWAY-nohs DEE-ahs",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Water" en español?',
            "question_type": "multiple_choice",
            "options": ["Leche", "Agua", "Jugo", "Café"],
            "correct_answer": "Agua",
            "hint": "You need it to survive.",
            "explanation": '"Agua" means "Water" in Spanish.',
            "example_sentence": "Necesito un vaso de agua.",
            "pronunciation_guide": "AH-gwah",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Dog" en español?',
            "question_type": "multiple_choice",
            "options": ["Gato", "Perro", "Pájaro", "Pez"],
            "correct_answer": "Perro",
            "hint": "Man's best friend.",
            "explanation": '"Perro" means "Dog" in Spanish.',
            "example_sentence": "Mi perro es muy amigable.",
            "pronunciation_guide": "PEH-rroh",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
    ]

    # Combine all demo flashcards
    all_demo_flashcards = demo_flashcards + extra_demo_flashcards

    # Randomly select a demo flashcard to provide variety
    import random
    import time

    # Use current time as seed for better randomization
    random.seed(int(time.time() * 1000) % 10000)
    selected_demo = random.choice(all_demo_flashcards)

    return Response(
        {
            "success": True,
            "flashcard": selected_demo,
            "flashcards": demo_flashcards,
            "cached": False,
            "demo": True,
        }
    )


@api_view(["POST"])
@permission_classes([AllowAny])  # Allow access without authentication for now
def submit_flashcard_answer(request):
    """Submit and grade flashcard answer using AI"""

    try:
        # Use DRF's request.data for automatic parsing
        data = request.data

        flashcard_id = data.get("flashcard_id")
        user_answer = data.get("user_answer")
        correct_answer = data.get("correct_answer")
        question = data.get("question")
        language = data.get("language", "spanish")

        logger.info(
            f"Grading answer for anonymous user: '{user_answer}' vs '{correct_answer}'"
        )

        if AI_SERVICE_AVAILABLE and question and correct_answer:
            try:
                # Use LLM service for intelligent grading
                llm_service = LLMFlashcardService()
                grading_result = llm_service.grade_answer(
                    question=question,
                    correct_answer=correct_answer,
                    user_answer=user_answer,
                    language=language,
                )

                response_data = {
                    "success": True,
                    "flashcard_id": flashcard_id,
                    "is_correct": grading_result.get("is_correct", False),
                    "score": grading_result.get("score", 0),
                    "feedback": grading_result.get("feedback", ""),
                    "suggestions": grading_result.get("suggestions", []),
                    "ai_graded": True,
                }

                logger.info(f"AI grading result: {grading_result}")
                return Response(response_data)

            except Exception as ai_error:
                logger.warning(f"AI grading failed, using simple grading: {ai_error}")
                # Fall through to simple grading

        # Simple fallback grading
        if correct_answer and user_answer:
            correct_clean = correct_answer.lower().strip()
            user_clean = user_answer.lower().strip()

            if correct_clean == user_clean:
                is_correct = True
                score = 100
                feedback = "Perfect! Correct answer."
                suggestions = []
            elif user_clean in correct_clean or correct_clean in user_clean:
                is_correct = True
                score = 75
                feedback = "Good! Very close to the correct answer."
                suggestions = ["Check for exact spelling and punctuation"]
            else:
                is_correct = False
                score = 0
                feedback = f"Not quite right. The correct answer is: {correct_answer}"
                suggestions = ["Review the vocabulary", "Practice more examples"]
        else:
            # For multiple choice, use the passed is_correct value
            is_correct = data.get("is_correct", False)
            score = 100 if is_correct else 0
            feedback = "Correct!" if is_correct else "Incorrect answer."
            suggestions = [] if is_correct else ["Try again!"]

        response_data = {
            "success": True,
            "flashcard_id": flashcard_id,
            "is_correct": is_correct,
            "score": score,
            "feedback": feedback,
            "suggestions": suggestions,
            "ai_graded": False,
        }

        logger.info(f"Simple grading result: {response_data}")
        return Response(response_data)

    except Exception as e:
        logger.error(f"Error in submit_flashcard_answer: {e}")
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


# generate_lesson function removed - functionality merged into generate_flashcard


@api_view(["POST"])
@permission_classes([AllowAny])
def preload_daily_content(request):
    """Pre-load and cache daily learning content for seamless UX"""

    logger.info(
        f"Pre-loading daily content for request from {request.META.get('REMOTE_ADDR', 'unknown')}"
    )

    try:
        # Use DRF's request.data for automatic parsing
        data = request.data

        user_id = data.get("user_id")
        if not user_id:
            return JsonResponse({"error": "user_id is required"}, status=400)

        # TODO: Implement content preloading logic here
        # This will be implemented with Django-RQ later

        response_data = {
            "success": True,
            "message": "Daily content preloading initiated",
            "user_id": user_id,
        }

        logger.info(f"Preload content initiated for user: {user_id}")
        return JsonResponse(response_data)

    except Exception as e:
        logger.error(f"Error in preload_daily_content: {e}")
        return JsonResponse(
            {"success": False, "error": str(e)},
            status=500,
        )


# generate_lesson function removed - functionality merged into generate_flashcard


@api_view(["GET"])
@permission_classes([AllowAny])
def get_adaptive_content(request):
    """Get adaptive content recommendations based on user performance"""

    if not PRELOADING_AVAILABLE:
        return Response(
            {"error": "Adaptive learning system not available"},
            status=status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    try:
        if request.user.is_authenticated:
            # Get adaptive content for authenticated user
            session_context = {
                "time_of_day": timezone.now().hour,
                "requested_language": request.GET.get("language", "spanish"),
                "session_type": request.GET.get("session_type", "practice"),
            }

            adaptive_content = content_preloader.get_adaptive_content(
                user=request.user, session_context=session_context
            )

            return Response(
                {
                    "success": True,
                    "adaptive_content": adaptive_content,
                    "personalized": True,
                    "timestamp": timezone.now().isoformat(),
                }
            )
        else:
            # Anonymous user - provide general recommendations
            language = request.GET.get("language", "spanish")
            difficulty = request.GET.get("difficulty", "beginner")

            content = content_preloader.preload_anonymous_content(language, difficulty)

            return Response(
                {
                    "success": True,
                    "content": content,
                    "personalized": False,
                    "message": "Sign in to get personalized adaptive learning!",
                    "timestamp": timezone.now().isoformat(),
                }
            )

    except Exception as e:
        logger.error(f"Error in get_adaptive_content: {e}")
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def track_performance(request):
    """Track user performance for adaptive learning"""

    if not PRELOADING_AVAILABLE:
        return Response(
            {"error": "Performance tracking not available"},
            status=status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    try:
        # Parse performance data
        if request.content_type == "application/json":
            data = json.loads(request.body)
        else:
            data = request.POST.dict()

        session_id = data.get("session_id", str(uuid.uuid4()))
        performance_data = {
            "question_count": int(data.get("question_count", 0)),
            "correct_answers": int(data.get("correct_answers", 0)),
            "time_spent_seconds": int(data.get("time_spent_seconds", 0)),
            "response_times": data.get("response_times", []),
            "difficulty_attempted": data.get("difficulty", "beginner"),
            "topics_covered": data.get("topics", []),
            "hints_used": int(data.get("hints_used", 0)),
            "completion_rate": float(data.get("completion_rate", 0.0)),
        }

        if request.user.is_authenticated:
            # Store performance metrics using new model structure
            from datetime import timedelta

            # Update user learning profile
            profile, created = UserLearningProfile.objects.get_or_create(
                user=request.user
            )
            profile.total_xp += performance_data.get("xp_earned", 0)
            profile.save()

            # Update user profile based on performance using adaptive engine
            if PRELOADING_AVAILABLE:
                updated_profile = adaptive_engine.update_user_profile(
                    request.user, performance_data
                )

            # Get recommendations for next session
            recommendations = adaptive_engine.generate_learning_insights(request.user)

            response_data = {
                "success": True,
                "performance_recorded": True,
                "session_id": session_id,
                "next_recommendations": recommendations.get("recommendations", []),
                "updated_difficulty": updated_profile.preferred_difficulty,
                "learning_insights": {
                    "accuracy_rate": performance_data["correct_answers"]
                    / max(performance_data["question_count"], 1),
                    "avg_response_time": (
                        sum(performance_data["response_times"])
                        / max(len(performance_data["response_times"]), 1)
                        if performance_data["response_times"]
                        else 0
                    ),
                    "performance_insights": recommendations.get(
                        "performance_insights", []
                    ),
                },
            }

            # Dynamic difficulty adjustment
            if (
                performance_data["question_count"] >= 5
            ):  # Minimum questions for adjustment
                new_difficulty = adaptive_engine.adjust_difficulty_dynamically(
                    request.user,
                    {
                        "accuracy": performance_data["correct_answers"]
                        / performance_data["question_count"],
                        "response_times": performance_data["response_times"],
                        "current_difficulty": performance_data["difficulty_attempted"],
                    },
                )
                response_data["recommended_difficulty"] = new_difficulty

        else:
            # Anonymous user - provide basic feedback
            accuracy = performance_data["correct_answers"] / max(
                performance_data["question_count"], 1
            )

            response_data = {
                "success": True,
                "performance_recorded": False,
                "session_summary": {
                    "accuracy": accuracy,
                    "questions_completed": performance_data["question_count"],
                    "time_spent": f"{performance_data['time_spent_seconds']} seconds",
                },
                "general_feedback": [
                    (
                        "Great practice session!"
                        if accuracy >= 0.8
                        else "Keep practicing to improve!"
                    ),
                    "Sign in to track your progress and get personalized recommendations!",
                ],
            }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Error in track_performance: {e}")
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_learning_analytics(request):
    """Get comprehensive learning analytics for authenticated users"""

    if not PRELOADING_AVAILABLE:
        return Response(
            {"error": "Learning analytics not available"},
            status=status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    try:
        # Get comprehensive performance analysis
        analysis = adaptive_engine.analyze_user_performance(request.user)

        # Get learning insights
        insights = adaptive_engine.generate_learning_insights(request.user)

        # Get user profile
        profile = UserLearningProfile.objects.filter(user=request.user).first()

        response_data = {
            "success": True,
            "analytics": {
                "performance_summary": analysis["summary"],
                "trends": analysis["trends"],
                "topic_analysis": analysis["topics"],
                "optimal_settings": {
                    "difficulty": analysis["optimal_difficulty"],
                    "recommended_content_types": (
                        getattr(profile, "preferred_content_types", ["flashcard"])
                        if profile
                        else ["flashcard"]
                    ),
                    "learning_pace": getattr(profile, "learning_pace", "moderate"),
                },
                "engagement_metrics": analysis["engagement"],
            },
            "insights": insights,
            "profile_summary": {
                "total_sessions": getattr(profile, "total_sessions", 0),
                "average_accuracy": getattr(profile, "average_accuracy", 0.0),
                "strengths": getattr(profile, "strengths", []),
                "areas_for_improvement": getattr(profile, "weaknesses", []),
                "learning_style": getattr(profile, "learning_style", "visual"),
            },
            "timestamp": timezone.now().isoformat(),
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Error in get_learning_analytics: {e}")
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([AllowAny])
def process_content_queue(request):
    """Process background content generation queue (admin/maintenance endpoint)"""

    if not PRELOADING_AVAILABLE:
        return Response(
            {"error": "Content processing not available"},
            status=status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    try:
        # Only allow this for admin users or in development
        if request.user.is_authenticated and not request.user.is_staff:
            return Response(
                {"error": "Admin access required"}, status=status.HTTP_403_FORBIDDEN
            )

        max_items = int(request.GET.get("max_items", 5))

        # Process the generation queue
        results = content_preloader.process_generation_queue(max_items)

        # Cleanup expired content
        cleanup_results = content_preloader.cleanup_expired_content()

        return Response(
            {
                "success": True,
                "queue_processing": results,
                "cleanup": cleanup_results,
                "timestamp": timezone.now().isoformat(),
            }
        )

    except Exception as e:
        logger.error(f"Error in process_content_queue: {e}")
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([AllowAny])
def get_content_recommendations(request):
    """Get intelligent content recommendations"""

    if not PRELOADING_AVAILABLE:
        return Response(
            {"error": "Content recommendations not available"},
            status=status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    try:
        if request.user.is_authenticated:
            # Get available content from user's lesson queue
            active_queue = UserLessonQueue.objects.filter(
                user=request.user, status="active", expires_at__gt=timezone.now()
            ).first()

            if active_queue:
                content_ids = active_queue.get_next_content_ids(count=10)
                available_content = ContentItem.objects.filter(
                    id__in=content_ids, is_active=True
                )
            else:
                available_content = ContentItem.objects.filter(
                    is_active=True, language="spanish"
                )[:10]

            # Get personalized recommendations using adaptive engine
            if PRELOADING_AVAILABLE:
                recommendations = adaptive_engine.predict_optimal_content(
                    request.user, list(available_content)
                )
            else:
                recommendations = []

            response_data = {
                "success": True,
                "recommendations": [
                    {
                        "content_id": str(rec["content"].id),
                        "content_type": rec["content"].content_type,
                        "difficulty": rec["content"].difficulty,
                        "language": rec["content"].language,
                        "suitability_score": rec["suitability_score"],
                        "reasons": rec["reasons"],
                        "estimated_duration": "10-15 minutes",  # Could be calculated
                    }
                    for rec in recommendations[:5]  # Top 5 recommendations
                ],
                "personalized": True,
            }
        else:
            # Anonymous user recommendations
            language = request.GET.get("language", "spanish")
            difficulty = request.GET.get("difficulty", "beginner")

            response_data = {
                "success": True,
                "recommendations": [
                    {
                        "content_type": "flashcard",
                        "difficulty": difficulty,
                        "language": language,
                        "title": f"{difficulty.title()} {language.title()} Flashcards",
                        "description": "Practice essential vocabulary and phrases",
                        "estimated_duration": "10 minutes",
                    },
                    {
                        "content_type": "lesson",
                        "difficulty": difficulty,
                        "language": language,
                        "title": f"{difficulty.title()} {language.title()} Lessons",
                        "description": "Structured learning with explanations",
                        "estimated_duration": "15 minutes",
                    },
                ],
                "personalized": False,
                "message": "Sign in to get AI-powered content recommendations!",
            }

        return Response(response_data)

    except Exception as e:
        logger.error(f"Error in get_content_recommendations: {e}")
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_care_analytics(request):
    """
    Get comprehensive C.A.R.E. framework analytics for authenticated users
    """
    try:
        user = request.user

        # Get C.A.R.E. performance analysis
        care_analysis = adaptive_engine.analyze_care_performance(user)

        # Get overall performance analysis
        performance_analysis = adaptive_engine.analyze_user_performance(user)

        # Get gamification stats
        level = Level.objects.filter(user=user).first()
        streak = Streak.objects.filter(user=user).first()
        achievements = Achievement.objects.filter(user=user).select_related("badge")

        # Combine all analytics
        analytics_data = {
            "success": True,
            "user_id": user.id,
            "care_framework": care_analysis,
            "performance": performance_analysis,
            "gamification": {
                "level": level.level if level else 1,
                "xp": level.xp if level else 0,
                "current_streak": streak.current_streak if streak else 0,
                "longest_streak": streak.longest_streak if streak else 0,
                "achievements_count": achievements.count(),
                "recent_achievements": [
                    {
                        "name": ach.badge.name,
                        "achieved_at": ach.achieved_at.isoformat(),
                        "description": getattr(ach.badge, "description", ""),
                    }
                    for ach in achievements.order_by("-achieved_at")[:5]
                ],
            },
            "recommendations": {
                "care_recommendations": care_analysis.get("recommendations", []),
                "performance_recommendations": performance_analysis.get(
                    "recommendations", []
                ),
                "optimal_care_sequence": care_analysis.get("optimal_sequence", []),
            },
            "timestamp": timezone.now().isoformat(),
        }

        logger.info(f"Generated C.A.R.E. analytics for user {user.id}")
        return Response(analytics_data)

    except Exception as e:
        logger.error(
            f"Error generating C.A.R.E. analytics for user {request.user.id}: {str(e)}"
        )
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def generate_care_content(request):
    """
    Generate C.A.R.E. framework content for authenticated users
    """
    try:
        user = request.user
        data = request.data

        # Get user preferences
        language = data.get("language", "spanish")
        difficulty = int(data.get("difficulty", 1))
        care_phase = data.get("care_phase", "acquire")
        content_type = data.get("content_type", "flashcard")

        # Validate inputs
        valid_phases = ["contextualize", "acquire", "reinforce", "extend"]
        valid_types = ["flashcard", "mcq", "translation", "grammar"]

        if care_phase not in valid_phases:
            return Response(
                {
                    "success": False,
                    "error": f"Invalid C.A.R.E. phase. Must be one of: {valid_phases}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if content_type not in valid_types:
            return Response(
                {
                    "success": False,
                    "error": f"Invalid content type. Must be one of: {valid_types}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Generate C.A.R.E. content using content pipeline
        result = content_pipeline.generate_care_content_batch(
            language=language,
            difficulty_level=difficulty,
            content_types=[content_type],
            batch_size=1,
        )

        if result["success"] and result["content_items"]:
            content_item = result["content_items"][0]

            # Format response
            content_data = {
                "success": True,
                "content": {
                    "id": content_item.id,
                    "type": content_item.type,
                    "care_phase": care_phase,
                    "question": content_item.question_text,
                    "answer": content_item.answer_text,
                    "hint": content_item.hint_text,
                    "explanation": content_item.explanation_text,
                    "choices": (
                        content_item.choices_list
                        if content_item.type == "mcq"
                        else None
                    ),
                    "difficulty": content_item.difficulty,
                    "language": content_item.language,
                    "tags": content_item.tags,
                },
                "care_context": {
                    "phase_focus": care_phase,
                    "learning_objective": content_pipeline._get_care_templates(
                        language, difficulty
                    )[care_phase]["focus"],
                },
                "timestamp": timezone.now().isoformat(),
            }

            logger.info(
                f"Generated {care_phase} {content_type} content for user {user.id}"
            )
            return Response(content_data)
        else:
            return Response(
                {
                    "success": False,
                    "error": "Failed to generate content",
                    "details": result.get("errors", []),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        logger.error(
            f"Error generating C.A.R.E. content for user {request.user.id}: {str(e)}"
        )
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def update_care_progress(request):
    """
    Update user progress in C.A.R.E. framework
    """
    try:
        user = request.user
        data = request.data

        content_id = data.get("content_id")
        is_correct = data.get("is_correct", False)
        response_time = data.get("response_time", 0)
        used_hint = data.get("used_hint", False)
        care_phase = data.get("care_phase", "acquire")

        if not content_id:
            return Response(
                {"success": False, "error": "content_id is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get or create content item
        try:
            content_item = ContentItem.objects.get(id=content_id)
        except ContentItem.DoesNotExist:
            return Response(
                {"success": False, "error": "Content item not found"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Get or create user performance record
        performance, created = UserContentPerformance.objects.get_or_create(
            user=user, content_item=content_item
        )

        # Store previous correct count for XP calculation
        performance._previous_correct = performance.times_correct

        # Record the attempt
        performance.record_attempt(
            is_correct=is_correct, response_time=response_time, used_hint=used_hint
        )

        # Award XP through gamification service (handled by signal)
        # The signal handler will automatically award XP based on performance

        # Calculate progress update
        progress_data = {
            "success": True,
            "content_id": content_id,
            "care_phase": care_phase,
            "performance": {
                "proficiency_score": performance.proficiency_score,
                "accuracy_rate": performance.accuracy_rate,
                "times_seen": performance.times_seen,
                "times_correct": performance.times_correct,
                "consecutive_correct": performance.consecutive_correct,
                "needs_review": performance.needs_review,
            },
            "session_stats": {
                "is_correct": is_correct,
                "response_time": response_time,
                "used_hint": used_hint,
            },
            "timestamp": timezone.now().isoformat(),
        }

        # Get updated C.A.R.E. analytics
        if (
            random.random() < 0.3
        ):  # 30% chance to include analytics (to avoid overloading)
            care_analysis = adaptive_engine.analyze_care_performance(user)
            progress_data["care_insights"] = {
                "overall_score": care_analysis.get("overall_score", 0.0),
                "phase_performance": care_analysis.get("phase_performance", {}),
                "recommendations": care_analysis.get("recommendations", [])[
                    :3
                ],  # Top 3 recommendations
            }

        logger.info(
            f"Updated C.A.R.E. progress for user {user.id}, content {content_id}"
        )
        return Response(progress_data)

    except Exception as e:
        logger.error(
            f"Error updating C.A.R.E. progress for user {request.user.id}: {str(e)}"
        )
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def populate_user_queue(request):
    """
    Populate user's learning queue with optimized C.A.R.E. content
    """
    try:
        user = request.user
        data = request.data

        queue_type = data.get("queue_type", "daily")
        force_refresh = data.get("force_refresh", False)

        # Check if user already has an active queue
        if not force_refresh:
            existing_queue = UserLessonQueue.objects.filter(
                user=user,
                queue_type=queue_type,
                status="active",
                expires_at__gt=timezone.now(),
            ).first()

            if existing_queue:
                return Response(
                    {
                        "success": True,
                        "message": "Queue already exists and is active",
                        "queue_id": existing_queue.id,
                        "remaining_items": existing_queue.get_remaining_count(),
                        "expires_at": existing_queue.expires_at.isoformat(),
                    }
                )

        # Populate queue using content pipeline
        result = content_pipeline.populate_user_queue(user.id, queue_type)

        if result["success"]:
            return Response(
                {
                    "success": True,
                    "message": f"{queue_type.title()} queue populated successfully",
                    "queue_id": result["queue_id"],
                    "content_count": result["content_count"],
                    "queue_type": queue_type,
                    "expires_at": result["expires_at"],
                }
            )
        else:
            return Response(
                {"success": False, "error": result.get("error", "Unknown error")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    except Exception as e:
        logger.error(f"Error populating queue for user {request.user.id}: {str(e)}")
        return Response(
            {"success": False, "error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
