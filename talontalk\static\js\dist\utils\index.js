/**
 * Utility functions for the TalonTalk C.A.R.E. system
 */
/**
 * Debounce function calls
 */
export function debounce(fn, delay) {
    let timeoutId;
    let lastArgs;
    const debounced = ((...args) => {
        lastArgs = args;
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => fn(...args), delay);
    });
    debounced.cancel = () => {
        clearTimeout(timeoutId);
    };
    debounced.flush = () => {
        clearTimeout(timeoutId);
        return fn(...lastArgs);
    };
    return debounced;
}
/**
 * Throttle function calls
 */
export function throttle(fn, limit) {
    let inThrottle;
    const throttled = ((...args) => {
        if (!inThrottle) {
            fn(...args);
            inThrottle = true;
            setTimeout(() => { inThrottle = false; }, limit);
        }
    });
    throttled.cancel = () => {
        inThrottle = false;
    };
    return throttled;
}
/**
 * Format date using a simple format string
 */
export function formatDate(date, format) {
    const map = {
        'YYYY': date.getFullYear().toString(),
        'MM': (date.getMonth() + 1).toString().padStart(2, '0'),
        'DD': date.getDate().toString().padStart(2, '0'),
        'HH': date.getHours().toString().padStart(2, '0'),
        'mm': date.getMinutes().toString().padStart(2, '0'),
        'ss': date.getSeconds().toString().padStart(2, '0')
    };
    return Object.keys(map).reduce((formatted, key) => {
        return formatted.replace(new RegExp(key, 'g'), map[key]);
    }, format);
}
/**
 * Format seconds into readable time string
 */
export function formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    else {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
}
/**
 * Generate unique ID with optional prefix
 */
export function generateId(prefix = 'id') {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}
/**
 * Convert string to URL-friendly slug
 */
export function slugify(text) {
    return text
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .trim()
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-'); // Remove duplicate hyphens
}
/**
 * Capitalize first letter of string
 */
export function capitalize(text) {
    return text.charAt(0).toUpperCase() + text.slice(1);
}
/**
 * Truncate text with optional suffix
 */
export function truncate(text, length, suffix = '...') {
    if (text.length <= length)
        return text;
    return text.substring(0, length - suffix.length) + suffix;
}
/**
 * Parse URL query string into object
 */
export function parseQuery(search) {
    const params = new URLSearchParams(search);
    const result = {};
    for (const [key, value] of params.entries()) {
        result[key] = value;
    }
    return result;
}
/**
 * Build query string from object
 */
export function buildQuery(params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
        searchParams.append(key, String(value));
    });
    return searchParams.toString();
}
/**
 * Escape HTML entities
 */
export function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
/**
 * Get browser information
 */
export function getBrowserInfo() {
    const userAgent = navigator.userAgent;
    const isOpera = Boolean(window.opr?.addons) || Boolean(window.opera) || userAgent.indexOf(' OPR/') >= 0;
    const isFirefox = typeof window.InstallTrigger !== 'undefined';
    const isSafari = /constructor/i.test(window.HTMLElement) || ((p) => p.toString() === '[object SafariRemoteNotification]')(!window['safari'] || (typeof window.safari !== 'undefined' && window.safari.pushNotification));
    const isIE = /*@cc_on!@*/ false || Boolean(window.document.documentMode);
    const isEdge = !isIE && Boolean(window.StyleMedia);
    const isChrome = Boolean(window.chrome) && Boolean(window.chrome.webstore) && !isOpera && !isEdge;
    let browserName = 'Unknown';
    if (isChrome)
        browserName = 'Chrome';
    else if (isFirefox)
        browserName = 'Firefox';
    else if (isSafari)
        browserName = 'Safari';
    else if (isOpera)
        browserName = 'Opera';
    else if (isEdge)
        browserName = 'Edge';
    else if (isIE)
        browserName = 'Internet Explorer';
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    return {
        name: browserName,
        version: 'Unknown', // Would need more complex detection
        os: navigator.platform,
        mobile: isMobile,
        webgl: Boolean(window.WebGLRenderingContext),
        localStorage: typeof Storage !== 'undefined',
        sessionStorage: typeof sessionStorage !== 'undefined',
        indexedDB: Boolean(window.indexedDB),
        webAudio: Boolean(window.AudioContext || window.webkitAudioContext),
        speechSynthesis: 'speechSynthesis' in window
    };
}
/**
 * Deep clone an object
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object')
        return obj;
    if (obj instanceof Date)
        return new Date(obj.getTime());
    if (obj instanceof Array)
        return obj.map(item => deepClone(item));
    const cloned = {};
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            cloned[key] = deepClone(obj[key]);
        }
    }
    return cloned;
}
/**
 * Check if device has touch capabilities
 */
export function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}
/**
 * Get viewport dimensions
 */
export function getViewportSize() {
    return {
        width: window.innerWidth || document.documentElement.clientWidth,
        height: window.innerHeight || document.documentElement.clientHeight
    };
}
/**
 * Smooth scroll to element
 */
export function scrollToElement(element, offset = 0) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;
    window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
    });
}
/**
 * Wait for specified milliseconds
 */
export function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * Get all utility functions as a single object
 */
export const utils = {
    debounce,
    throttle,
    formatDate,
    formatTime,
    generateId,
    slugify,
    capitalize,
    truncate,
    parseQuery,
    buildQuery
};
/**
 * Export all utilities as default
 */
export default {
    debounce,
    throttle,
    formatDate,
    formatTime,
    generateId,
    slugify,
    capitalize,
    truncate,
    parseQuery,
    buildQuery,
    escapeHtml,
    getBrowserInfo,
    deepClone,
    isTouchDevice,
    getViewportSize,
    scrollToElement,
    sleep,
    utils
};
//# sourceMappingURL=index.js.map