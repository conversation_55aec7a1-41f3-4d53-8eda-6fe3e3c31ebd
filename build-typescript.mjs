#!/usr/bin/env node

/**
 * Build script for TalonTalk TypeScript implementation
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { existsSync, mkdirSync } from 'fs';
import path from 'path';

const execAsync = promisify(exec);

async function build() {
    console.log('🚀 Building TalonTalk TypeScript implementation...');

    try {
        // Clean previous builds
        console.log('🧹 Cleaning previous builds...');
        try {
            await execAsync('npm run clean');
        } catch (error) {
            console.log('No previous builds to clean');
        }

        // Ensure output directories exist
        const outputDir = path.join(process.cwd(), 'talontalk', 'static', 'js', 'dist');
        if (!existsSync(outputDir)) {
            mkdirSync(outputDir, { recursive: true });
        }

        // Run TypeScript compilation
        console.log('🔨 Compiling TypeScript...');
        await execAsync('npx tsc');
        console.log('✅ TypeScript compilation completed');

        // Copy built files to Django static directory
        console.log('📁 Copying files to Django static directory...');
        await execAsync('npm run copy-static');
        console.log('✅ Files copied successfully');

        console.log('🎉 Build completed successfully!');
        console.log('');
        console.log('📍 Built files are available in:');
        console.log('   - dist/ (TypeScript output)');
        console.log('   - talontalk/static/js/dist/ (Django static files)');

    } catch (error) {
        console.error('❌ Build failed:', error);
        process.exit(1);
    }
}

// Run build if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    build();
}

export { build };
