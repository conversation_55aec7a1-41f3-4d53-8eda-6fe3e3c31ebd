<!-- Reinforce Phase: Practice and Solidify -->
<div class="care-phase-content reinforce-phase hidden" data-phase="reinforce">
    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl p-8 text-white mb-8">
        <div class="flex items-center gap-4 mb-4">
            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <i class="fas fa-dumbbell text-3xl"></i>
            </div>
            <div>
                <h2 class="text-3xl font-bold">Reinforce</h2>
                <p class="text-purple-100">Practice and solidify your new knowledge</p>
            </div>
        </div>
        <p class="text-lg text-purple-50">Let's strengthen your skills through focused practice</p>
    </div>

    <!-- Practice Activities Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Flashcard Practice -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-cards text-purple-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">Flashcard Review</h3>
            </div>
            <div id="flashcardContainer" class="min-h-[300px] flex flex-col items-center justify-center">
                <div class="flashcard w-full max-w-sm h-48 perspective-1000">
                    <div class="flashcard-inner relative w-full h-full transform-style-preserve-3d transition-transform duration-600">
                        <div class="flashcard-front absolute w-full h-full bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center backface-hidden">
                            <div class="text-center p-6">
                                <div class="text-2xl font-bold text-white mb-2" id="flashcard-question">café</div>
                                <div class="text-purple-100 text-sm">Click to reveal translation</div>
                            </div>
                        </div>
                        <div class="flashcard-back absolute w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center backface-hidden transform rotateY-180">
                            <div class="text-center p-6">
                                <div class="text-2xl font-bold text-white mb-2" id="flashcard-answer">coffee</div>
                                <div class="text-blue-100 text-sm">Did you get it right?</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex gap-3 mt-6">
                    <button id="wrongBtn" class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-times mr-2"></i>Incorrect
                    </button>
                    <button id="correctBtn" class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-check mr-2"></i>Correct
                    </button>
                </div>
                
                <div class="mt-4 text-center">
                    <div class="text-sm text-gray-500">Card <span id="cardNumber">1</span> of <span id="totalCards">8</span></div>
                    <div class="w-48 h-2 bg-gray-200 rounded-full mt-2">
                        <div id="flashcardProgress" class="h-full bg-purple-600 rounded-full transition-all duration-300" style="width: 12.5%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Audio Recognition -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center gap-3 mb-4">
                <i class="fas fa-headphones text-purple-600 text-xl"></i>
                <h3 class="text-xl font-semibold text-gray-900">Listen & Match</h3>
            </div>
            <div class="space-y-4">
                <div class="text-center">
                    <button id="playAudioBtn" class="w-20 h-20 bg-purple-600 text-white rounded-full hover:bg-purple-700 transition-colors flex items-center justify-center text-2xl">
                        <i class="fas fa-play"></i>
                    </button>
                    <p class="text-sm text-gray-600 mt-2">Click to hear the pronunciation</p>
                </div>
                
                <div class="grid grid-cols-2 gap-3">
                    <button class="audio-option p-4 border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors text-left" data-answer="café">
                        <div class="font-medium">café</div>
                        <div class="text-sm text-gray-500">coffee</div>
                    </button>
                    <button class="audio-option p-4 border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors text-left" data-answer="leche">
                        <div class="font-medium">leche</div>
                        <div class="text-sm text-gray-500">milk</div>
                    </button>
                    <button class="audio-option p-4 border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors text-left" data-answer="cortado">
                        <div class="font-medium">cortado</div>
                        <div class="text-sm text-gray-500">espresso with milk</div>
                    </button>
                    <button class="audio-option p-4 border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors text-left" data-answer="cuenta">
                        <div class="font-medium">cuenta</div>
                        <div class="text-sm text-gray-500">bill</div>
                    </button>
                </div>
                
                <div id="audioResult" class="hidden p-3 rounded-lg">
                    <!-- Audio result feedback -->
                </div>
            </div>
        </div>
    </div>

    <!-- Conversation Practice -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-comment-dots text-purple-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">Conversation Practice</h3>
        </div>
        <p class="text-gray-600 mb-6">Practice a real café conversation. You are the customer.</p>
        
        <div id="conversationArea" class="space-y-4 max-h-96 overflow-y-auto">
            <div class="flex gap-3">
                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                    <i class="fas fa-user-tie text-gray-600"></i>
                </div>
                <div class="flex-1">
                    <div class="bg-gray-100 p-3 rounded-lg">
                        <p class="text-gray-800">¡Buenos días! ¿Qué le gustaría tomar?</p>
                        <p class="text-sm text-gray-500 mt-1">Good morning! What would you like to have?</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 space-y-3">
            <p class="text-sm font-medium text-gray-700">Choose your response:</p>
            <div class="space-y-2">
                <button class="conversation-option w-full p-3 text-left border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors" data-response="Me gustaría un café con leche, por favor">
                    <div class="font-medium">Me gustaría un café con leche, por favor</div>
                    <div class="text-sm text-gray-500">I would like a coffee with milk, please</div>
                </button>
                <button class="conversation-option w-full p-3 text-left border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors" data-response="Un cortado, gracias">
                    <div class="font-medium">Un cortado, gracias</div>
                    <div class="text-sm text-gray-500">A cortado, thank you</div>
                </button>
                <button class="conversation-option w-full p-3 text-left border border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors" data-response="¿Qué me recomienda?">
                    <div class="font-medium">¿Qué me recomienda?</div>
                    <div class="text-sm text-gray-500">What do you recommend?</div>
                </button>
            </div>
        </div>
    </div>

    <!-- Progress Summary -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center gap-3 mb-4">
            <i class="fas fa-chart-line text-purple-600 text-xl"></i>
            <h3 class="text-xl font-semibold text-gray-900">Your Progress</h3>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600" id="vocabScore">85%</div>
                <div class="text-sm text-purple-700">Vocabulary Mastery</div>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600" id="listeningScore">92%</div>
                <div class="text-sm text-blue-700">Listening Skills</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600" id="conversationScore">78%</div>
                <div class="text-sm text-green-700">Conversation Flow</div>
            </div>
        </div>
        <div class="mt-6 text-center">
            <p class="text-gray-600 mb-4">Great progress! You're ready to apply your knowledge in real situations.</p>
            <div class="flex items-center justify-center gap-2">
                <i class="fas fa-star text-yellow-500"></i>
                <i class="fas fa-star text-yellow-500"></i>
                <i class="fas fa-star text-yellow-500"></i>
                <i class="fas fa-star text-yellow-500"></i>
                <i class="far fa-star text-gray-300"></i>
                <span class="ml-2 text-sm text-gray-600">4/5 stars earned</span>
            </div>
        </div>
    </div>
</div>

<style>
.perspective-1000 {
    perspective: 1000px;
}

.transform-style-preserve-3d {
    transform-style: preserve-3d;
}

.backface-hidden {
    backface-visibility: hidden;
}

.rotateY-180 {
    transform: rotateY(180deg);
}

.flashcard-inner.flipped {
    transform: rotateY(180deg);
}
</style>

<script>
// Reinforce phase interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Flashcard data
    const flashcards = [
        { question: 'café', answer: 'coffee' },
        { question: 'con leche', answer: 'with milk' },
        { question: 'cortado', answer: 'espresso with milk' },
        { question: 'croissant', answer: 'croissant' },
        { question: 'la cuenta', answer: 'the bill' },
        { question: 'por favor', answer: 'please' },
        { question: 'gracias', answer: 'thank you' },
        { question: 'buenos días', answer: 'good morning' }
    ];
    
    let currentCard = 0;
    let correctAnswers = 0;
    
    // Flashcard functionality
    const flashcardInner = document.querySelector('.flashcard-inner');
    const questionEl = document.getElementById('flashcard-question');
    const answerEl = document.getElementById('flashcard-answer');
    const cardNumberEl = document.getElementById('cardNumber');
    const totalCardsEl = document.getElementById('totalCards');
    const progressBar = document.getElementById('flashcardProgress');
    const correctBtn = document.getElementById('correctBtn');
    const wrongBtn = document.getElementById('wrongBtn');
    
    totalCardsEl.textContent = flashcards.length;
    
    function showCard(index) {
        const card = flashcards[index];
        questionEl.textContent = card.question;
        answerEl.textContent = card.answer;
        cardNumberEl.textContent = index + 1;
        progressBar.style.width = `${((index + 1) / flashcards.length) * 100}%`;
        
        // Reset card to front
        flashcardInner.classList.remove('flipped');
    }
    
    function flipCard() {
        flashcardInner.classList.toggle('flipped');
    }
    
    function nextCard() {
        if (currentCard < flashcards.length - 1) {
            currentCard++;
            showCard(currentCard);
        } else {
            // Finished all cards
            showFlashcardResults();
        }
    }
    
    function showFlashcardResults() {
        const container = document.getElementById('flashcardContainer');
        const accuracy = Math.round((correctAnswers / flashcards.length) * 100);
        container.innerHTML = `
            <div class="text-center">
                <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center text-white text-2xl mb-4 mx-auto">
                    <i class="fas fa-check"></i>
                </div>
                <h4 class="text-xl font-bold text-gray-900 mb-2">Flashcard Practice Complete!</h4>
                <p class="text-gray-600 mb-4">You got ${correctAnswers} out of ${flashcards.length} correct</p>
                <div class="text-3xl font-bold text-purple-600 mb-4">${accuracy}%</div>
                <button id="restartFlashcards" class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    Practice Again
                </button>
            </div>
        `;
        
        document.getElementById('restartFlashcards').addEventListener('click', function() {
            currentCard = 0;
            correctAnswers = 0;
            location.reload(); // Simple restart
        });
    }
    
    // Flashcard click to flip
    document.querySelector('.flashcard').addEventListener('click', flipCard);
    
    // Answer buttons
    correctBtn.addEventListener('click', function() {
        correctAnswers++;
        nextCard();
    });
    
    wrongBtn.addEventListener('click', function() {
        nextCard();
    });
    
    // Audio recognition game
    const audioWords = ['café', 'leche', 'cortado', 'cuenta'];
    let currentAudioWord = 0;
    
    const playAudioBtn = document.getElementById('playAudioBtn');
    const audioOptions = document.querySelectorAll('.audio-option');
    const audioResult = document.getElementById('audioResult');
    
    playAudioBtn.addEventListener('click', function() {
        // Simulate audio playback
        this.innerHTML = '<i class="fas fa-pause"></i>';
        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-play"></i>';
        }, 1500);
        
        // Show current word being "played"
        const word = audioWords[currentAudioWord];
        console.log('Playing audio for:', word);
    });
    
    audioOptions.forEach(option => {
        option.addEventListener('click', function() {
            const selectedAnswer = this.dataset.answer;
            const correctAnswer = audioWords[currentAudioWord];
            
            // Reset all options
            audioOptions.forEach(opt => {
                opt.classList.remove('border-green-500', 'bg-green-50', 'border-red-500', 'bg-red-50');
            });
            
            audioResult.classList.remove('hidden');
            
            if (selectedAnswer === correctAnswer) {
                this.classList.add('border-green-500', 'bg-green-50');
                audioResult.className = 'p-3 rounded-lg bg-green-50 border border-green-200';
                audioResult.innerHTML = `
                    <div class="flex items-center gap-2 text-green-800">
                        <i class="fas fa-check-circle"></i>
                        <span class="font-medium">Correct! That was "${correctAnswer}"</span>
                    </div>
                `;
                
                // Move to next word
                setTimeout(() => {
                    currentAudioWord = (currentAudioWord + 1) % audioWords.length;
                    audioResult.classList.add('hidden');
                    audioOptions.forEach(opt => {
                        opt.classList.remove('border-green-500', 'bg-green-50');
                    });
                }, 1500);
            } else {
                this.classList.add('border-red-500', 'bg-red-50');
                audioResult.className = 'p-3 rounded-lg bg-red-50 border border-red-200';
                audioResult.innerHTML = `
                    <div class="flex items-center gap-2 text-red-800">
                        <i class="fas fa-times-circle"></i>
                        <span class="font-medium">Try again! Listen carefully.</span>
                    </div>
                `;
            }
        });
    });
    
    // Conversation practice
    const conversationOptions = document.querySelectorAll('.conversation-option');
    const conversationArea = document.getElementById('conversationArea');
    
    const conversationFlow = [
        {
            server: "¡Perfecto! ¿Algo más?",
            serverEn: "Perfect! Anything else?",
            responses: [
                { text: "Un croissant también, por favor", en: "A croissant too, please" },
                { text: "No, gracias", en: "No, thank you" },
                { text: "¿Cuánto cuesta?", en: "How much does it cost?" }
            ]
        },
        {
            server: "Son 3,50 euros",
            serverEn: "That's 3.50 euros",
            responses: [
                { text: "Aquí tiene", en: "Here you go" },
                { text: "¿Puedo pagar con tarjeta?", en: "Can I pay with card?" },
                { text: "La cuenta, por favor", en: "The bill, please" }
            ]
        }
    ];
    
    let conversationStep = 0;
    
    conversationOptions.forEach(option => {
        option.addEventListener('click', function() {
            const response = this.dataset.response;
            const responseEn = this.querySelector('.text-sm').textContent;
            
            // Add customer response
            conversationArea.innerHTML += `
                <div class="flex gap-3 justify-end">
                    <div class="flex-1 max-w-sm">
                        <div class="bg-purple-500 text-white p-3 rounded-lg">
                            <p>${response}</p>
                            <p class="text-sm text-purple-100 mt-1">${responseEn}</p>
                        </div>
                    </div>
                    <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                </div>
            `;
            
            // Add server response if there's a next step
            if (conversationStep < conversationFlow.length) {
                setTimeout(() => {
                    const nextStep = conversationFlow[conversationStep];
                    conversationArea.innerHTML += `
                        <div class="flex gap-3">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-tie text-gray-600"></i>
                            </div>
                            <div class="flex-1">
                                <div class="bg-gray-100 p-3 rounded-lg">
                                    <p class="text-gray-800">${nextStep.server}</p>
                                    <p class="text-sm text-gray-500 mt-1">${nextStep.serverEn}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Update response options
                    conversationOptions.forEach((opt, index) => {
                        if (nextStep.responses[index]) {
                            opt.dataset.response = nextStep.responses[index].text;
                            opt.querySelector('.font-medium').textContent = nextStep.responses[index].text;
                            opt.querySelector('.text-sm').textContent = nextStep.responses[index].en;
                        }
                    });
                    
                    conversationStep++;
                    conversationArea.scrollTop = conversationArea.scrollHeight;
                }, 1000);
            } else {
                // Conversation complete
                setTimeout(() => {
                    conversationArea.innerHTML += `
                        <div class="text-center p-6">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-xl mb-4 mx-auto">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <h4 class="text-lg font-bold text-gray-900">Conversation Complete!</h4>
                            <p class="text-gray-600">You successfully navigated a café interaction in Spanish!</p>
                        </div>
                    `;
                    
                    // Enable next phase
                    const nextBtn = document.getElementById('nextPhaseBtn');
                    if (nextBtn) {
                        nextBtn.disabled = false;
                        nextBtn.classList.remove('opacity-50');
                    }
                }, 1000);
            }
            
            conversationArea.scrollTop = conversationArea.scrollHeight;
        });
    });
    
    // Initialize first card
    showCard(0);
});
</script>
