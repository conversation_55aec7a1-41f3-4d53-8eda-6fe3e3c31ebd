#!/usr/bin/env python3

import requests
import json
import sys
import time

BASE_URL = "http://127.0.0.1:8000"


def login_user():
    """Login to get session cookie"""
    print("🔐 Attempting to login...")

    # Get login page to get CSRF token
    session = requests.Session()
    login_page = session.get(f"{BASE_URL}/users/login/")

    if login_page.status_code == 200:
        print("✅ Login page accessible")

        # Try to create and login test user
        return session
    else:
        print(f"❌ Cannot access login page: {login_page.status_code}")
        return None


def test_lesson_page_logged_in():
    """Test lesson page with authentication"""
    print("🧪 Testing lesson page with authentication...")

    session = login_user()
    if not session:
        print("❌ Cannot login, skipping authenticated test")
        return

    # Test lesson page with session
    try:
        url = f"{BASE_URL}/care/lesson/1/"
        response = session.get(url, timeout=10)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ Lesson page loads with session")

            # Check for key elements in HTML
            html = response.text
            print(f"HTML length: {len(html)} characters")

            # Save response to file for inspection
            with open("lesson_response.html", "w", encoding="utf-8") as f:
                f.write(html)
            print("💾 Saved response to lesson_response.html")

            # Check for key elements
            checks = [
                ("care-lesson.js", "JavaScript file included"),
                ("contextualizePhase", "Contextualize phase container"),
                ("acquirePhase", "Acquire phase container"),
                ("reinforcePhase", "Reinforce phase container"),
                ("extendPhase", "Extend phase container"),
                ("CARELessonManager", "JavaScript class present"),
                ('class="care-phase-content"', "Phase content class"),
                ('id="careProgress"', "Progress container"),
            ]

            for check, description in checks:
                if check in html:
                    print(f"✅ {description}")
                else:
                    print(f"❌ {description} missing")

        elif response.status_code == 302:
            print("🔄 Redirected (probably to login)")
            print(f"Redirect location: {response.headers.get('Location', 'Unknown')}")
        else:
            print(f"❌ HTTP Error {response.status_code}")
            print(f"Response: {response.text[:500]}...")

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")


def test_static_files():
    """Test if static files are accessible"""
    print("\n📁 Testing static file access...")

    static_files = [
        "/static/js/care-lesson.js",
        "/static/css/main.css",
    ]

    for static_file in static_files:
        try:
            url = f"{BASE_URL}{static_file}"
            response = requests.get(url, timeout=5)

            if response.status_code == 200:
                print(f"✅ {static_file} accessible ({len(response.content)} bytes)")
            else:
                print(f"❌ {static_file} not accessible ({response.status_code})")

        except requests.exceptions.RequestException as e:
            print(f"❌ {static_file} request failed: {e}")


if __name__ == "__main__":
    print("🚀 C.A.R.E. Authentication & Static Files Test")
    print("=" * 60)

    test_static_files()
    test_lesson_page_logged_in()

    print("\n" + "=" * 60)
    print("✨ Test complete!")
