# Generated by Django 5.2.3 on 2025-07-05 10:59

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0006_userlearningprofile_learning_patterns'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='lesson',
            options={'ordering': ['order', 'difficulty_level']},
        ),
        migrations.AddField(
            model_name='contentitem',
            name='progressive_hints',
            field=models.JSONField(default=list, help_text="Progressive hints: ['Gentle nudge', 'More specific hint', 'Almost the answer']"),
        ),
        migrations.AddField(
            model_name='lesson',
            name='average_score',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='lesson',
            name='care_phase',
            field=models.CharField(choices=[('contextualize', 'Contextualize - Set the scene'), ('acquire', 'Acquire - Learn new concepts'), ('reinforce', 'Reinforce - Practice and solidify'), ('extend', 'Extend - Apply in new contexts')], default='contextualize', help_text='Current C.A.R.E. phase focus for this lesson', max_length=20),
        ),
        migrations.AddField(
            model_name='lesson',
            name='completion_rate',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='lesson',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lesson',
            name='difficulty_level',
            field=models.PositiveIntegerField(choices=[(1, 'Beginner'), (2, 'Elementary'), (3, 'Intermediate'), (4, 'Upper Intermediate'), (5, 'Advanced')], default=1, help_text='Lesson difficulty from 1 (Beginner) to 5 (Advanced)'),
        ),
        migrations.AddField(
            model_name='lesson',
            name='estimated_duration',
            field=models.PositiveIntegerField(default=15, help_text='Estimated completion time in minutes'),
        ),
        migrations.AddField(
            model_name='lesson',
            name='hint_levels',
            field=models.JSONField(default=list, help_text="Progressive hints: ['Level 1 hint', 'Level 2 hint', 'Final hint']"),
        ),
        migrations.AddField(
            model_name='lesson',
            name='learning_objectives',
            field=models.JSONField(default=list, help_text='List of learning objectives for this lesson'),
        ),
        migrations.AddField(
            model_name='lesson',
            name='prerequisite_lessons',
            field=models.ManyToManyField(blank=True, help_text='Lessons that should be completed before this one', to='lessons.lesson'),
        ),
        migrations.AddField(
            model_name='lesson',
            name='target_language',
            field=models.CharField(default='spanish', help_text='Target language being taught', max_length=20),
        ),
        migrations.AddField(
            model_name='lesson',
            name='total_attempts',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='lesson',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddIndex(
            model_name='lesson',
            index=models.Index(fields=['care_phase', 'difficulty_level'], name='lessons_les_care_ph_6671e9_idx'),
        ),
        migrations.AddIndex(
            model_name='lesson',
            index=models.Index(fields=['target_language', 'is_active'], name='lessons_les_target__2897cb_idx'),
        ),
        migrations.AddIndex(
            model_name='lesson',
            index=models.Index(fields=['difficulty_level', 'completion_rate'], name='lessons_les_difficu_2c80b7_idx'),
        ),
    ]
