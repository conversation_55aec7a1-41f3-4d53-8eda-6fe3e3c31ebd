"""
Management command to set up CEFR-structured lessons and content
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from lessons.models import Lesson, ContentItem


class Command(BaseCommand):
    help = "Set up CEFR-structured lessons and content for proper language learning progression"

    def add_arguments(self, parser):
        parser.add_argument(
            "--reset",
            action="store_true",
            help="Reset existing CEFR levels before setting up new ones",
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Setting up CEFR-structured lessons..."))

        if options["reset"]:
            self.reset_cefr_levels()

        with transaction.atomic():
            self.setup_cefr_lessons()
            self.setup_cefr_content()

        self.stdout.write(self.style.SUCCESS("CEFR setup completed successfully!"))

    def reset_cefr_levels(self):
        """Reset all CEFR levels to default"""
        self.stdout.write("Resetting CEFR levels...")

        Lesson.objects.all().update(cefr_level="A1")
        ContentItem.objects.all().update(cefr_level="A1")

        self.stdout.write(self.style.WARNING("CEFR levels reset to A1"))

    def setup_cefr_lessons(self):
        """Set up lessons with appropriate CEFR levels"""
        self.stdout.write("Setting up CEFR lesson structure...")

        # Define CEFR lesson mapping based on difficulty and content
        cefr_lesson_mapping = {
            "A1": {
                "difficulty_range": [1],
                "topics": ["greetings", "basic", "introductions", "numbers", "colors"],
                "max_lessons": 10,
            },
            "A2": {
                "difficulty_range": [1, 2],
                "topics": ["family", "food", "shopping", "directions", "time"],
                "max_lessons": 8,
            },
            "B1": {
                "difficulty_range": [2, 3],
                "topics": ["travel", "work", "hobbies", "past_tense", "future_tense"],
                "max_lessons": 6,
            },
            "B2": {
                "difficulty_range": [3, 4],
                "topics": ["opinions", "complex_grammar", "culture", "literature"],
                "max_lessons": 5,
            },
            "C1": {
                "difficulty_range": [4, 5],
                "topics": ["academic", "professional", "abstract_concepts"],
                "max_lessons": 4,
            },
            "C2": {
                "difficulty_range": [5],
                "topics": ["mastery", "nuanced_expression", "advanced_literature"],
                "max_lessons": 3,
            },
        }

        # Update existing lessons with CEFR levels
        for cefr_level, config in cefr_lesson_mapping.items():
            difficulty_range = config["difficulty_range"]
            max_lessons = config["max_lessons"]

            # Get lessons that match this CEFR level criteria
            lesson_ids = list(
                Lesson.objects.filter(
                    difficulty_level__in=difficulty_range,
                    cefr_level="A1",  # Only update lessons that haven't been assigned yet
                )
                .order_by("difficulty_level", "order")[:max_lessons]
                .values_list("id", flat=True)
            )

            updated_count = Lesson.objects.filter(id__in=lesson_ids).update(
                cefr_level=cefr_level
            )

            self.stdout.write(f"  {cefr_level}: Updated {updated_count} lessons")

        # Create additional lessons if needed
        self.create_missing_cefr_lessons(cefr_lesson_mapping)

    def create_missing_cefr_lessons(self, cefr_mapping):
        """Create lessons for CEFR levels that don't have enough content"""

        lesson_templates = {
            "A1": [
                {
                    "title": "Basic Greetings",
                    "description": "Learn essential Spanish greetings",
                },
                {
                    "title": "Numbers 1-20",
                    "description": "Master basic numbers in Spanish",
                },
                {
                    "title": "Colors and Objects",
                    "description": "Identify colors and common objects",
                },
                {"title": "Family Members", "description": "Talk about your family"},
                {
                    "title": "Days and Months",
                    "description": "Learn days of the week and months",
                },
            ],
            "A2": [
                {
                    "title": "Shopping and Prices",
                    "description": "Navigate shopping situations",
                },
                {
                    "title": "Food and Restaurants",
                    "description": "Order food and discuss preferences",
                },
                {
                    "title": "Directions and Transportation",
                    "description": "Ask for and give directions",
                },
                {"title": "Past Tense Basics", "description": "Talk about past events"},
            ],
            "B1": [
                {
                    "title": "Travel and Vacations",
                    "description": "Discuss travel plans and experiences",
                },
                {
                    "title": "Work and Professions",
                    "description": "Talk about jobs and career goals",
                },
                {
                    "title": "Expressing Opinions",
                    "description": "Share and discuss opinions",
                },
            ],
            "B2": [
                {
                    "title": "Current Events",
                    "description": "Discuss news and social issues",
                },
                {
                    "title": "Cultural Differences",
                    "description": "Compare cultures and traditions",
                },
            ],
            "C1": [
                {
                    "title": "Academic Writing",
                    "description": "Master formal writing skills",
                },
                {
                    "title": "Professional Communication",
                    "description": "Business and formal communication",
                },
            ],
            "C2": [
                {
                    "title": "Literary Analysis",
                    "description": "Analyze literature and complex texts",
                },
            ],
        }

        for cefr_level, templates in lesson_templates.items():
            existing_count = Lesson.objects.filter(cefr_level=cefr_level).count()
            target_count = cefr_mapping[cefr_level]["max_lessons"]

            if existing_count < target_count:
                needed = target_count - existing_count
                difficulty = cefr_mapping[cefr_level]["difficulty_range"][0]

                for i, template in enumerate(templates[:needed]):
                    lesson, created = Lesson.objects.get_or_create(
                        title=template["title"],
                        defaults={
                            "description": template["description"],
                            "difficulty_level": difficulty,
                            "cefr_level": cefr_level,
                            "order": existing_count + i + 1,
                            "target_language": "spanish",
                            "estimated_duration": 15,
                        },
                    )

                    if created:
                        self.stdout.write(
                            f"  Created lesson: {lesson.title} ({cefr_level})"
                        )

    def setup_cefr_content(self):
        """Set up content items with appropriate CEFR levels"""
        self.stdout.write("Setting up CEFR content structure...")

        # Map content difficulty to CEFR levels
        difficulty_to_cefr = {1: "A1", 2: "A2", 3: "B1", 4: "B2", 5: "C1"}

        # Update content items based on difficulty
        for difficulty, cefr_level in difficulty_to_cefr.items():
            updated_count = ContentItem.objects.filter(
                difficulty=difficulty,
                cefr_level="A1",  # Only update items that haven't been assigned
            ).update(cefr_level=cefr_level)

            self.stdout.write(f"  {cefr_level}: Updated {updated_count} content items")

        # Create sample content for each CEFR level if needed
        self.create_sample_cefr_content()

    def create_sample_cefr_content(self):
        """Create sample content for each CEFR level"""

        sample_content = {
            "A1": [
                {
                    "type": "flashcard",
                    "question_text": '¿Cómo se dice "hello" en español?',
                    "answer_text": "Hola",
                    "difficulty": 1,
                    "tags": ["greetings", "basic"],
                },
                {
                    "type": "mcq",
                    "question_text": 'What does "Gracias" mean?',
                    "answer_text": "Thank you",
                    "choices_json": [
                        "Thank you",
                        "Please",
                        "Excuse me",
                        "You're welcome",
                    ],
                    "difficulty": 1,
                    "tags": ["politeness", "basic"],
                },
            ],
            "A2": [
                {
                    "type": "translation",
                    "question_text": 'Translate: "I would like to buy bread"',
                    "answer_text": "Me gustaría comprar pan",
                    "difficulty": 2,
                    "tags": ["shopping", "conditional"],
                }
            ],
            "B1": [
                {
                    "type": "mcq",
                    "question_text": "Which sentence expresses a past habit?",
                    "answer_text": "Cuando era niño, jugaba fútbol todos los días",
                    "choices_json": [
                        "Ayer jugué fútbol",
                        "Cuando era niño, jugaba fútbol todos los días",
                        "Mañana jugaré fútbol",
                        "Estoy jugando fútbol",
                    ],
                    "difficulty": 3,
                    "tags": ["imperfect", "past_habits"],
                }
            ],
        }

        for cefr_level, content_list in sample_content.items():
            for content_data in content_list:
                content_data.update({"cefr_level": cefr_level, "language": "spanish"})

                item, created = ContentItem.objects.get_or_create(
                    question_text=content_data["question_text"], defaults=content_data
                )

                if created:
                    self.stdout.write(
                        f"  Created content: {item.question_text[:50]}... ({cefr_level})"
                    )

        self.stdout.write(self.style.SUCCESS("CEFR content structure completed!"))
