#!/usr/bin/env python
"""
Complete Content Purge Script for TalonTalk
Removes ALL existing content to start fresh with DeepSeek R1 generated content
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()

from lessons.models import (
    ContentItem,
    UserLearningProfile,
    UserContentPerformance,
    UserLessonQueue,
)
from django.db import transaction


def main():
    print("TalonTalk Complete Content Purge")
    print("=" * 50)
    print("WARNING: This will delete ALL existing content!")
    print("   - All flashcards and learning content")
    print("   - User performance data")
    print("   - Learning queues")
    print("   - User profiles will be RESET")
    print()

    # Get counts before deletion
    content_count = ContentItem.objects.count()
    performance_count = UserContentPerformance.objects.count()
    queue_count = UserLessonQueue.objects.count()
    profile_count = UserLearningProfile.objects.count()

    print(f"Current Database State:")
    print(f"   Content Items: {content_count}")
    print(f"   Performance Records: {performance_count}")
    print(f"   Learning Queues: {queue_count}")
    print(f"   User Profiles: {profile_count}")
    print()

    if content_count == 0 and performance_count == 0:
        print("Database is already clean!")
        return

    # Confirm deletion
    confirm = input("Type 'PURGE' to confirm complete deletion: ")
    if confirm != "PURGE":
        print("Operation cancelled")
        return

    print("\nStarting complete purge...")

    try:
        with transaction.atomic():
            # Delete in correct order to avoid foreign key issues

            # 1. Delete user performance data
            if performance_count > 0:
                print(f"   Deleting {performance_count} performance records...")
                UserContentPerformance.objects.all().delete()

            # 2. Delete learning queues
            if queue_count > 0:
                print(f"   Deleting {queue_count} learning queues...")
                UserLessonQueue.objects.all().delete()

            # 3. Reset user profiles (keep users but reset their learning data)
            if profile_count > 0:
                print(f"   Resetting {profile_count} user profiles...")
                for profile in UserLearningProfile.objects.all():
                    profile.total_xp = 0
                    profile.current_streak = 0
                    profile.longest_streak = 0
                    profile.current_level = 1
                    profile.total_sessions = 0
                    profile.average_accuracy = 0.0
                    profile.total_study_time = 0
                    profile.strengths = []
                    profile.weaknesses = []
                    profile.learning_patterns = {}
                    profile.save()

            # 4. Delete all content items
            if content_count > 0:
                print(f"   Deleting {content_count} content items...")
                ContentItem.objects.all().delete()

            print("Complete purge successful!")

    except Exception as e:
        print(f"Error during purge: {e}")
        return

    # Verify deletion
    remaining_content = ContentItem.objects.count()
    remaining_performance = UserContentPerformance.objects.count()
    remaining_queues = UserLessonQueue.objects.count()

    print(f"\nDatabase After Purge:")
    print(f"   Content Items: {remaining_content}")
    print(f"   Performance Records: {remaining_performance}")
    print(f"   Learning Queues: {remaining_queues}")
    print(f"   User Profiles: {UserLearningProfile.objects.count()} (reset)")

    if remaining_content == 0 and remaining_performance == 0 and remaining_queues == 0:
        print("\nDatabase successfully purged!")
        print("   Ready for fresh DeepSeek R1 generated content!")
    else:
        print("\nSome data may still remain. Check manually.")


if __name__ == "__main__":
    main()
