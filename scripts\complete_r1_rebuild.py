#!/usr/bin/env python
"""
Complete R1 Rebuild Script
Purges all content and rebuilds with high-quality DeepSeek R1 generated flashcards
"""

import os
import sys
import django
import subprocess
import time

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talontalk.settings')
django.setup()

def run_script(script_path, description):
    """Run a Python script and handle errors"""
    print(f"\n🔄 {description}")
    print("-" * 50)
    
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, cwd=os.path.dirname(script_path))
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"❌ {description} failed")
            if result.stderr:
                print("Error:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error running {description}: {e}")
        return False
    
    return True

def check_r1_availability():
    """Check if DeepSeek R1 is available"""
    print("🔍 Checking DeepSeek R1 availability...")
    
    try:
        from ai_services.llm_config import get_recommended_config
        config = get_recommended_config()
        
        if "deepseek" in config.model_config.name.lower():
            print("✅ DeepSeek R1 is available and selected")
            return True
        else:
            print(f"⚠️  Currently using: {config.model_config.name}")
            print("   DeepSeek R1 not selected. Check your Ollama setup.")
            return False
            
    except Exception as e:
        print(f"❌ Error checking R1: {e}")
        return False

def main():
    print("🚀 TalonTalk Complete R1 Rebuild")
    print("=" * 60)
    print("This will:")
    print("1. 🧹 Purge ALL existing content")
    print("2. 🔧 Verify DeepSeek R1 availability") 
    print("3. 🎯 Generate high-quality flashcards with R1")
    print("4. ✅ Verify content quality")
    print()
    
    # Safety confirmation
    confirm = input("⚠️  This will DELETE ALL existing content. Type 'REBUILD' to continue: ")
    if confirm != 'REBUILD':
        print("❌ Operation cancelled")
        return
    
    start_time = time.time()
    
    # Step 1: Check R1 availability
    if not check_r1_availability():
        print("\n❌ DeepSeek R1 not available. Please run:")
        print("   ollama pull deepseek-r1:8b")
        return
    
    # Step 2: Purge existing content
    script_dir = os.path.dirname(os.path.abspath(__file__))
    purge_script = os.path.join(script_dir, "complete_content_purge.py")
    
    if not run_script(purge_script, "Purging existing content"):
        print("❌ Purge failed. Aborting rebuild.")
        return
    
    # Step 3: Generate R1 content
    generate_script = os.path.join(script_dir, "generate_r1_content.py")
    
    if not run_script(generate_script, "Generating R1 flashcards"):
        print("❌ Content generation failed.")
        return
    
    # Step 4: Verify results
    print("\n📊 Verifying rebuild results...")
    
    try:
        from lessons.models import ContentItem
        from lessons.advanced_quality_engine import AdvancedQualityEngine
        
        total_content = ContentItem.objects.count()
        r1_content = ContentItem.objects.filter(tags__contains=['r1_generated']).count()
        
        print(f"   Total content items: {total_content}")
        print(f"   R1 generated items: {r1_content}")
        
        if total_content > 0:
            # Sample quality check
            quality_engine = AdvancedQualityEngine()
            sample_items = ContentItem.objects.all()[:10]
            
            quality_scores = []
            for item in sample_items:
                content_dict = {
                    "id": str(item.id),
                    "question": item.question_text,
                    "content_type": item.type,
                    "language": item.language,
                }
                
                try:
                    report = quality_engine.validate_content(content_dict, item.language)
                    quality_scores.append(report.overall_score)
                except:
                    pass
            
            if quality_scores:
                avg_quality = sum(quality_scores) / len(quality_scores)
                print(f"   Average quality score: {avg_quality:.1f}/100")
                
                if avg_quality >= 75:
                    print("   ✅ Quality check passed!")
                else:
                    print("   ⚠️  Quality below expected threshold")
        
        # Language breakdown
        print(f"\n📈 Content by language:")
        languages = ContentItem.objects.values_list('language', flat=True).distinct()
        for language in languages:
            count = ContentItem.objects.filter(language=language).count()
            print(f"   {language.title()}: {count} items")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
    
    # Final summary
    elapsed_time = time.time() - start_time
    print(f"\n🎉 R1 Rebuild Complete!")
    print("=" * 60)
    print(f"⏱️  Total time: {elapsed_time/60:.1f} minutes")
    print(f"🚀 Platform now powered by DeepSeek R1")
    print(f"📚 High-quality flashcards ready for learning")
    print()
    print("Next steps:")
    print("1. Start the server: python manage.py runserver")
    print("2. Test the enhanced flashcards")
    print("3. Check quality in Django admin")
    print()
    print("✨ Your language learning platform is now supercharged with R1!")

if __name__ == "__main__":
    main()
