{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if profile.target_language %}{{ profile.target_language|title }}{% else %}Language{% endif %} Practice - TalonTalk</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'talon-blue': '#2C3E50',
                        'falcon-yellow': '#FFC300',
                        'success-green': '#10B981',
                        'error-red': '#EF4444',
                        'accent-purple': '#8B5CF6'
                    }
                }
            }
        }
    </script>
    
    <!-- Prevent distractions -->
    <style>
        body { 
            overflow-x: hidden;
            user-select: none; /* Prevent text selection for focus */
        }
        
        /* Hide scrollbar but keep functionality */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: transparent;
        }
        ::-webkit-scrollbar-thumb {
            background: #CBD5E0;
            border-radius: 3px;
        }
        
        /* Focus ring for accessibility */
        .focus-ring:focus {
            outline: 2px solid #FFC300;
            outline-offset: 2px;
        }
        
        /* Smooth animations */
        .slide-in {
            animation: slideIn 0.4s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Pulse animation for engagement */
        .pulse-subtle {
            animation: pulseSubtle 2s ease-in-out infinite;
        }
        
        @keyframes pulseSubtle {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        /* Progress bar animation */
        .progress-animate {
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Stats animation for engagement */
        .stat-bump {
            animation: statBump 0.5s ease-out;
        }
        
        @keyframes statBump {
            0% { transform: scale(1); }
            50% { transform: scale(1.15); }
            100% { transform: scale(1); }
        }
    </style>
</head>

<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 h-screen overflow-hidden flex flex-col">
    {% csrf_token %}
    
    <!-- Immersive Header (10% of page) -->
    <header class="bg-gradient-to-r from-talon-blue to-indigo-700 text-white shadow-2xl">
        <div class="max-w-7xl mx-auto px-6 py-6">
            <div class="flex items-center justify-between">
                <!-- Back Button - Subtle -->
                <div class="w-1/4">
                    <a href="{% url 'dashboard' %}" class="inline-flex items-center space-x-2 text-blue-200 hover:text-white transition-colors p-3 rounded-lg hover:bg-white/10">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        <span class="text-sm font-medium">Dashboard</span>
                    </a>
                </div>
                
                <!-- Progress Section - Centered -->
                <div class="flex-1 max-w-3xl">
                    <div class="text-center mb-3">
                        <div class="flex items-center justify-center space-x-6 mb-2">
                            <span class="text-blue-200 text-sm font-medium">Question</span>
                            <span id="progress-text" class="text-white font-bold text-2xl">1 of 10</span>
                            <span class="text-blue-200 text-sm font-medium">{{ profile.target_language|title }} Practice</span>
                        </div>
                    </div>
                    
                    <!-- Animated Progress Bar -->
                    <div class="w-full max-w-md mx-auto bg-blue-800/30 rounded-full h-4 shadow-inner">
                        <div id="progress-bar" class="bg-gradient-to-r from-falcon-yellow to-yellow-400 h-4 rounded-full progress-animate shadow-lg" style="width: 10%"></div>
                    </div>
                </div>
                
                <!-- Session Stats - Better Layout -->
                <div class="w-1/4 text-center">
                    <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                        <div class="text-xs text-blue-200 font-medium uppercase tracking-wide mb-1">Session Score</div>
                        <div id="session-score" class="text-3xl font-bold text-falcon-yellow">0</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Learning Area (80% of page) -->
    <main class="flex-1 max-w-5xl mx-auto px-6 py-6 pb-24 flex flex-col justify-center overflow-y-auto">{# Flex-1 takes remaining space between header and footer #}
        
        <!-- Loading State -->
        <div id="loading-state" class="text-center py-20">
            <div class="animate-spin rounded-full h-20 w-20 border-4 border-talon-blue border-t-falcon-yellow mx-auto mb-8"></div>
            <h2 class="text-2xl font-bold text-talon-blue mb-4">Preparing Your Practice</h2>
            <p class="text-gray-600 text-lg">Creating the perfect question for your learning level...</p>
            <div class="mt-8 flex justify-center space-x-2">
                <div class="w-3 h-3 bg-talon-blue rounded-full animate-bounce"></div>
                <div class="w-3 h-3 bg-talon-blue rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-3 h-3 bg-talon-blue rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
        </div>

        <!-- Flashcard Container -->
        <div id="flashcard-container" class="hidden flex-1 flex flex-col justify-center">
            <!-- Question Card - Large and Prominent -->
            <div class="bg-white rounded-3xl shadow-2xl p-12 mb-8 border-4 border-blue-100 slide-in max-w-4xl mx-auto w-full">
                <div class="text-center">
                    <!-- Question Type Badge -->
                    <div class="inline-flex items-center px-6 py-3 bg-purple-100 text-purple-700 rounded-full text-base font-medium mb-6">
                        <span class="mr-3 text-lg">🧠</span>
                        <span id="question-type">Multiple Choice</span>
                    </div>
                    
                    <!-- Main Question -->
                    <h1 id="flashcard-question" class="text-4xl md:text-5xl font-bold text-talon-blue mb-8 leading-tight max-w-3xl mx-auto">
                        Loading question...
                    </h1>
                    
                    <!-- Hint Section (Hidden by default) -->
                    <div id="hint-section" class="hidden bg-yellow-50 border-2 border-yellow-200 rounded-2xl p-6 mb-8 max-w-2xl mx-auto">
                        <div class="flex items-center justify-center space-x-3 mb-3">
                            <span class="text-2xl">💡</span>
                            <h3 class="text-lg font-bold text-yellow-800">Hint</h3>
                        </div>
                        <p id="hint-text" class="text-yellow-700 text-lg leading-relaxed"></p>
                    </div>
                    
                    <!-- Show Hint Button -->
                    <button id="show-hint-btn" class="text-yellow-600 hover:text-yellow-700 font-medium underline mb-8 focus-ring rounded-lg px-4 py-2 text-base">
                        💡 Show Hint
                    </button>
                </div>
            </div>

            <!-- Answer Options - Large Touch Targets -->
            <div id="answer-options" class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 max-w-4xl mx-auto w-full">
                <!-- Options will be dynamically inserted -->
            </div>
            
            <!-- Text Input (for non-multiple choice) -->
            <div id="text-input-area" class="hidden mb-8 max-w-2xl mx-auto w-full">
                <div class="bg-white rounded-2xl shadow-xl p-8 border-2 border-gray-200">
                    <label for="text-answer" class="block text-lg font-medium text-gray-700 mb-4 text-center">
                        Type your answer:
                    </label>
                    <input type="text" id="text-answer" 
                           class="w-full px-6 py-4 text-2xl text-center border-3 border-gray-300 rounded-xl focus:border-talon-blue focus:ring-4 focus:ring-blue-100 transition-all"
                           placeholder="Enter your answer here..."
                           autocomplete="off">
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-center">
                <button id="submit-answer-btn" 
                        class="bg-gradient-to-r from-talon-blue to-indigo-600 hover:from-indigo-700 hover:to-blue-700 text-white px-12 py-4 rounded-2xl font-bold text-xl shadow-xl transform hover:scale-105 transition-all focus-ring disabled:opacity-50 disabled:cursor-not-allowed">
                    Submit Answer
                </button>
            </div>
        </div>

        <!-- Feedback Section -->
        <div id="feedback-section" class="hidden slide-in flex-1 flex flex-col justify-center">
            <div id="feedback-card" class="rounded-3xl shadow-2xl p-10 mb-8 border-4 max-w-4xl mx-auto w-full">
                <!-- Feedback content will be dynamically inserted -->
            </div>
            
            <!-- Continue Button -->
            <div class="text-center">
                <button id="continue-btn" 
                        class="bg-gradient-to-r from-success-green to-emerald-600 hover:from-emerald-600 hover:to-green-700 text-white px-12 py-4 rounded-2xl font-bold text-xl shadow-xl transform hover:scale-105 transition-all focus-ring">
                    Continue Learning
                </button>
            </div>
        </div>

        <!-- Session Complete -->
        <div id="session-complete" class="hidden text-center flex-1 flex flex-col justify-center">
            <div class="bg-white rounded-3xl shadow-2xl p-12 max-w-3xl mx-auto border-4 border-green-200">
                <div class="text-6xl mb-6">🎉</div>
                <h2 class="text-4xl font-bold text-talon-blue mb-4">Practice Complete!</h2>
                <p class="text-xl text-gray-600 mb-8">Excellent work! You've completed this learning session.</p>
                
                <!-- Session Statistics -->
                <div class="grid grid-cols-3 gap-6 mb-8">
                    <div class="bg-blue-50 rounded-2xl p-6 border-2 border-blue-200">
                        <div id="total-questions" class="text-3xl font-bold text-talon-blue mb-2">10</div>
                        <div class="text-sm font-medium text-blue-700">Questions</div>
                    </div>
                    <div class="bg-green-50 rounded-2xl p-6 border-2 border-green-200">
                        <div id="correct-answers" class="text-3xl font-bold text-success-green mb-2">8</div>
                        <div class="text-sm font-medium text-green-700">Correct</div>
                    </div>
                    <div class="bg-yellow-50 rounded-2xl p-6 border-2 border-yellow-200">
                        <div id="accuracy-rate" class="text-3xl font-bold text-yellow-600 mb-2">80%</div>
                        <div class="text-sm font-medium text-yellow-700">Accuracy</div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                    <button onclick="startNewSession()" 
                            class="bg-gradient-to-r from-talon-blue to-indigo-600 hover:from-indigo-700 hover:to-blue-700 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl transform hover:scale-105 transition-all">
                        Practice Again
                    </button>
                    <a href="{% url 'dashboard' %}" 
                       class="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl transform hover:scale-105 transition-all text-center">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Bottom Stats Bar (Sticky) - Always visible for motivation -->
    <footer class="fixed bottom-0 left-0 right-0 bg-white border-t-4 border-gray-100 shadow-2xl z-40">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between text-sm">
                <!-- Session Info -->
                <div class="flex items-center space-x-3 sm:space-x-6">
                    <div class="flex items-center space-x-2">
                        <span class="w-3 h-3 bg-green-400 rounded-full pulse-subtle"></span>
                        <span class="text-gray-600 text-xs sm:text-sm">Active</span>
                    </div>
                    <div class="text-gray-500 text-xs sm:text-sm hidden sm:block">Started: <span id="session-start-time"></span></div>
                </div>
                
                <!-- Real-time Stats -->
                <div class="flex items-center space-x-4 sm:space-x-8">
                    <div class="text-center">
                        <div id="streak-count" class="text-base sm:text-lg font-bold text-orange-500">0</div>
                        <div class="text-xs text-gray-500">Streak</div>
                    </div>
                    <div class="text-center">
                        <div id="xp-earned" class="text-base sm:text-lg font-bold text-purple-500">0 XP</div>
                        <div class="text-xs text-gray-500">Session</div>
                    </div>
                    <div class="text-center">
                        <div id="time-elapsed" class="text-base sm:text-lg font-bold text-gray-600">0:00</div>
                        <div class="text-xs text-gray-500">Time</div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Psychology-backed learning variables
        let currentQuestion = 0;
        let totalQuestions = 10;
        let sessionStats = {
            correct: 0,
            total: 0,
            streak: 0,
            maxStreak: 0,
            xpEarned: 0,
            startTime: new Date()
        };
        let currentFlashcard = null;
        let sessionTimer = null;
        let selectedAnswer = null;
        let flashcardSet = []; // 🚀 Preloaded flashcard set for instant transitions
        let isSessionInitialized = false;

        // Initialize session
        document.addEventListener('DOMContentLoaded', function() {
            initializeSession();
            startTimer();
            preloadFlashcardSet();
        });

        function initializeSession() {
            // Set session start time
            const startTime = new Date();
            sessionStats.startTime = startTime;
            document.getElementById('session-start-time').textContent = startTime.toLocaleTimeString();
            
            // Reset UI
            document.getElementById('loading-state').classList.remove('hidden');
            document.getElementById('flashcard-container').classList.add('hidden');
            document.getElementById('feedback-section').classList.add('hidden');
            document.getElementById('session-complete').classList.add('hidden');
            
            // Reset session variables
            currentQuestion = 0;
            flashcardSet = [];
            isSessionInitialized = false;
        }

        // 🚀 NEW: Preload full flashcard set for instant transitions
        function preloadFlashcardSet() {
            // Get URL parameters to check if this is lesson-specific practice
            const urlParams = new URLSearchParams(window.location.search);
            const lessonId = urlParams.get('lesson');
            const lessonDifficulty = urlParams.get('difficulty');
            const lessonTopic = urlParams.get('topic');
            
            // Check for preloaded content first
            if (checkPreloadedContent()) {
                return; // preloaded content will handle initialization
            }
            
            // Get user difficulty level from URL, profile, or use default
            const userDifficulty = lessonDifficulty || '{{ profile.skill_level|default:"beginner"|lower }}';
            
            // Vary question types based on difficulty and randomness
            let questionType;
            if (userDifficulty === 'beginner') {
                // Beginners get more multiple choice (70% of the time)
                questionType = Math.random() < 0.7 ? 'multiple_choice' : 'fill_blank';
            } else if (userDifficulty === 'intermediate') {
                // Intermediate gets balanced mix (50/50)
                questionType = Math.random() < 0.5 ? 'multiple_choice' : 'fill_blank';
            } else {
                // Advanced gets more fill-in-blank (70% of the time)
                questionType = Math.random() < 0.3 ? 'multiple_choice' : 'fill_blank';
            }
            
            // Build URL with parameters - request full lesson length
            const params = new URLSearchParams({
                'difficulty': userDifficulty,
                'type': questionType,
                'language': '{{ profile.target_language|default:"spanish"|lower }}',
                'lesson_length': totalQuestions.toString() // 🚀 Request full set
            });
            
            // Add lesson-specific parameters if available
            if (lessonId) {
                params.append('lesson_id', lessonId);
            }
            if (lessonTopic) {
                params.append('topic', lessonTopic);
            }
            
            fetch(`/api/flashcard/?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 🚀 Use full flashcard set if available, otherwise create set from single flashcard
                    if (data.flashcards && Array.isArray(data.flashcards)) {
                        flashcardSet = data.flashcards.slice(0, totalQuestions);
                        console.log(`🚀 Preloaded ${flashcardSet.length} flashcards for instant transitions`);
                    } else if (data.flashcard) {
                        // Single flashcard - we'll need to generate more on demand
                        flashcardSet = [data.flashcard];
                        console.log('⚠️ Only received single flashcard, will generate more on demand');
                    }
                    
                    totalQuestions = Math.min(flashcardSet.length, totalQuestions);
                    startFlashcardSession();
                } else {
                    showError('Failed to load questions. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Connection error. Please check your internet and try again.');
            });
        }

        function startFlashcardSession() {
            isSessionInitialized = true;
            loadNextQuestion();
        }

        function startTimer() {
            sessionTimer = setInterval(function() {
                const elapsed = Math.floor((new Date() - sessionStats.startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                document.getElementById('time-elapsed').textContent = 
                    `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function loadNextQuestion() {
            // 🚀 Check if we've completed all questions
            if (currentQuestion >= totalQuestions || currentQuestion >= flashcardSet.length) {
                showSessionComplete();
                return;
            }

            // Hide previous states
            document.getElementById('feedback-section').classList.add('hidden');
            
            // 🚀 INSTANT: Use preloaded flashcard if available
            if (flashcardSet.length > currentQuestion) {
                currentFlashcard = flashcardSet[currentQuestion];
                currentQuestion++; // Increment AFTER getting the flashcard
                displayFlashcard();
            } else {
                // Fallback: fetch individual flashcard if set is incomplete
                document.getElementById('loading-state').classList.remove('hidden');
                document.getElementById('flashcard-container').classList.add('hidden');
                
                // 🚀 Reduced delay for fallback (from 1500ms to 300ms)
                setTimeout(() => {
                    fetchSingleFlashcard();
                }, 300);
            }
        }

        // 🚀 Fallback method for individual flashcard fetching (rarely used)
        function fetchSingleFlashcard() {
            const urlParams = new URLSearchParams(window.location.search);
            const lessonId = urlParams.get('lesson');
            const lessonDifficulty = urlParams.get('difficulty');
            const lessonTopic = urlParams.get('topic');
            
            const userDifficulty = lessonDifficulty || '{{ profile.skill_level|default:"beginner"|lower }}';
            
            let questionType;
            if (userDifficulty === 'beginner') {
                questionType = Math.random() < 0.7 ? 'multiple_choice' : 'fill_blank';
            } else if (userDifficulty === 'intermediate') {
                questionType = Math.random() < 0.5 ? 'multiple_choice' : 'fill_blank';
            } else {
                questionType = Math.random() < 0.3 ? 'multiple_choice' : 'fill_blank';
            }
            
            const params = new URLSearchParams({
                'difficulty': userDifficulty,
                'type': questionType,
                'language': '{{ profile.target_language|default:"spanish"|lower }}'
            });
            
            if (lessonId) {
                params.append('lesson_id', lessonId);
            }
            if (lessonTopic) {
                params.append('topic', lessonTopic);
            }
            
            fetch(`/api/flashcard/?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentFlashcard = data.flashcard;
                    currentQuestion++;
                    displayFlashcard();
                } else {
                    showError('Failed to load question. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Connection error. Please check your internet and try again.');
            });
        }

        function displayFlashcard() {
            // Update progress (currentQuestion is already incremented)
            updateProgress();
            
            // Hide loading, show flashcard with smooth transition
            document.getElementById('loading-state').classList.add('hidden');
            document.getElementById('flashcard-container').classList.remove('hidden');
            
            // Set question content
            document.getElementById('flashcard-question').textContent = currentFlashcard.question;
            document.getElementById('question-type').textContent = 
                currentFlashcard.question_type === 'multiple_choice' ? 'Multiple Choice' : 'Type Answer';
            
            // Set hint
            if (currentFlashcard.hint) {
                document.getElementById('hint-text').textContent = currentFlashcard.hint;
                document.getElementById('show-hint-btn').classList.remove('hidden');
            } else {
                document.getElementById('show-hint-btn').classList.add('hidden');
            }
            
            // Reset hint visibility
            document.getElementById('hint-section').classList.add('hidden');
            
            // Setup answer interface
            if (currentFlashcard.question_type === 'multiple_choice') {
                setupMultipleChoice();
            } else {
                setupTextInput();
            }
            
            // Reset submit button
            const submitBtn = document.getElementById('submit-answer-btn');
            submitBtn.disabled = true;
            submitBtn.classList.add('opacity-50');
            selectedAnswer = null;
        }

        function setupMultipleChoice() {
            document.getElementById('answer-options').classList.remove('hidden');
            document.getElementById('text-input-area').classList.add('hidden');
            
            const optionsContainer = document.getElementById('answer-options');
            optionsContainer.innerHTML = '';
            
            currentFlashcard.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = `
                    bg-white hover:bg-blue-50 border-3 border-gray-300 hover:border-talon-blue 
                    rounded-3xl p-6 text-xl font-medium text-gray-700 hover:text-talon-blue 
                    transition-all transform hover:scale-105 shadow-lg hover:shadow-xl 
                    focus-ring min-h-[120px] flex items-center justify-center text-center
                    leading-relaxed
                `;
                button.textContent = option;
                button.onclick = () => selectOption(button, option);
                optionsContainer.appendChild(button);
            });
        }

        function setupTextInput() {
            document.getElementById('answer-options').classList.add('hidden');
            document.getElementById('text-input-area').classList.remove('hidden');
            
            const textInput = document.getElementById('text-answer');
            textInput.value = '';
            textInput.focus();
            
            textInput.oninput = function() {
                selectedAnswer = this.value.trim();
                toggleSubmitButton(selectedAnswer.length > 0);
            };
            
            textInput.onkeypress = function(e) {
                if (e.key === 'Enter' && selectedAnswer) {
                    submitAnswer();
                }
            };
        }

        function selectOption(button, answer) {
            // Remove previous selection
            document.querySelectorAll('#answer-options button').forEach(btn => {
                btn.classList.remove('border-talon-blue', 'bg-blue-100', 'text-talon-blue');
                btn.classList.add('border-gray-300', 'bg-white', 'text-gray-700');
            });
            
            // Highlight selected option
            button.classList.add('border-talon-blue', 'bg-blue-100', 'text-talon-blue');
            button.classList.remove('border-gray-300', 'bg-white', 'text-gray-700');
            
            selectedAnswer = answer;
            toggleSubmitButton(true);
        }

        function toggleSubmitButton(enabled) {
            const submitBtn = document.getElementById('submit-answer-btn');
            submitBtn.disabled = !enabled;
            if (enabled) {
                submitBtn.classList.remove('opacity-50');
            } else {
                submitBtn.classList.add('opacity-50');
            }
        }

        function showHint() {
            document.getElementById('hint-section').classList.remove('hidden');
            document.getElementById('show-hint-btn').classList.add('hidden');
        }

        function submitAnswer() {
            if (!selectedAnswer) return;
            
            const isCorrect = selectedAnswer.toLowerCase().trim() === 
                            currentFlashcard.correct_answer.toLowerCase().trim();
            
            // Update stats
            sessionStats.total++;
            if (isCorrect) {
                sessionStats.correct++;
                sessionStats.streak++;
                sessionStats.maxStreak = Math.max(sessionStats.maxStreak, sessionStats.streak);
                sessionStats.xpEarned += 10; // Base XP
                
                // Bonus XP for streaks
                if (sessionStats.streak >= 3) {
                    sessionStats.xpEarned += 5;
                }
            } else {
                sessionStats.streak = 0;
            }
            
            // Update real-time stats
            updateStats();
            
            // Show feedback
            showFeedback(isCorrect);
            
            // Submit to backend
            submitToBackend(isCorrect);
        }

        function showFeedback(isCorrect) {
            document.getElementById('flashcard-container').classList.add('hidden');
            document.getElementById('feedback-section').classList.remove('hidden');
            
            const feedbackCard = document.getElementById('feedback-card');
            
            if (isCorrect) {
                feedbackCard.className = 'rounded-3xl shadow-2xl p-10 mb-8 border-4 border-green-300 bg-gradient-to-r from-green-50 to-emerald-50 max-w-4xl mx-auto w-full';
                feedbackCard.innerHTML = `
                    <div class="text-center">
                        <div class="text-6xl mb-4">✅</div>
                        <h3 class="text-3xl font-bold text-success-green mb-4">Correct!</h3>
                        <p class="text-xl text-green-700 mb-6 leading-relaxed">Great job! You're building your vocabulary.</p>
                        ${sessionStats.streak >= 3 ? `<div class="bg-yellow-100 border-2 border-yellow-300 rounded-xl p-4 mb-4 max-w-lg mx-auto">
                            <span class="text-2xl">🔥</span>
                            <span class="text-lg font-bold text-yellow-700 ml-2">${sessionStats.streak} question streak!</span>
                        </div>` : ''}
                        <div class="bg-white rounded-xl p-6 border-2 border-green-200 max-w-2xl mx-auto">
                            <p class="text-gray-700 text-base leading-relaxed"><strong>Explanation:</strong> ${currentFlashcard.explanation}</p>
                        </div>
                    </div>
                `;
            } else {
                feedbackCard.className = 'rounded-3xl shadow-2xl p-10 mb-8 border-4 border-red-300 bg-gradient-to-r from-red-50 to-pink-50 max-w-4xl mx-auto w-full';
                feedbackCard.innerHTML = `
                    <div class="text-center">
                        <div class="text-6xl mb-4">❌</div>
                        <h3 class="text-3xl font-bold text-error-red mb-4">Not quite right</h3>
                        <p class="text-xl text-red-700 mb-4 leading-relaxed">Don't worry, learning takes practice!</p>
                        <div class="bg-white rounded-xl p-4 border-2 border-red-200 mb-4 max-w-lg mx-auto">
                            <p class="text-gray-700 text-base"><strong>Correct Answer:</strong> ${currentFlashcard.correct_answer}</p>
                        </div>
                        <div class="bg-white rounded-xl p-6 border-2 border-red-200 max-w-2xl mx-auto">
                            <p class="text-gray-700 text-base leading-relaxed"><strong>Explanation:</strong> ${currentFlashcard.explanation}</p>
                        </div>
                    </div>
                `;
            }
        }

        function submitToBackend(isCorrect) {
            fetch('/api/answer/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    flashcard_id: currentFlashcard.id,
                    user_answer: selectedAnswer,
                    correct_answer: currentFlashcard.correct_answer,
                    question: currentFlashcard.question,
                    language: currentFlashcard.language || 'spanish',
                    is_correct: isCorrect
                })
            })
            .then(response => response.json())
            .then(data => {
                // AI grading completed but we don't need to show extra feedback
                console.log('Answer submitted successfully');
            })
            .catch(error => console.log('Backend submission error (non-critical):', error));
        }

        function updateProgress() {
            const percentage = (currentQuestion / totalQuestions) * 100;
            document.getElementById('progress-bar').style.width = `${percentage}%`;
            document.getElementById('progress-text').textContent = `${currentQuestion} of ${totalQuestions}`;
            document.getElementById('session-score').textContent = sessionStats.correct;
        }

        function updateStats() {
            // Add bump animation to changed stats
            const streakEl = document.getElementById('streak-count');
            const xpEl = document.getElementById('xp-earned');
            
            if (parseInt(streakEl.textContent) !== sessionStats.streak) {
                streakEl.classList.add('stat-bump');
                setTimeout(() => streakEl.classList.remove('stat-bump'), 500);
            }
            
            if (!xpEl.textContent.includes(sessionStats.xpEarned.toString())) {
                xpEl.classList.add('stat-bump');
                setTimeout(() => xpEl.classList.remove('stat-bump'), 500);
            }
            
            streakEl.textContent = sessionStats.streak;
            xpEl.textContent = `${sessionStats.xpEarned} XP`;
        }

        function showSessionComplete() {
            if (sessionTimer) {
                clearInterval(sessionTimer);
            }
            
            document.getElementById('flashcard-container').classList.add('hidden');
            document.getElementById('feedback-section').classList.add('hidden');
            document.getElementById('session-complete').classList.remove('hidden');
            
            // Update final stats
            const accuracy = Math.round((sessionStats.correct / sessionStats.total) * 100);
            document.getElementById('total-questions').textContent = sessionStats.total;
            document.getElementById('correct-answers').textContent = sessionStats.correct;
            document.getElementById('accuracy-rate').textContent = `${accuracy}%`;
        }

        function startNewSession() {
            // Reset all session variables
            currentQuestion = 0;
            flashcardSet = [];
            isSessionInitialized = false;
            sessionStats = {
                correct: 0,
                total: 0,
                streak: 0,
                maxStreak: 0,
                xpEarned: 0,
                startTime: new Date()
            };
            
            // Restart the session
            initializeSession();
            startTimer();
            preloadFlashcardSet(); // 🚀 Preload new set
        }

        function showError(message) {
            document.getElementById('loading-state').innerHTML = `
                <div class="text-center py-20">
                    <div class="text-6xl mb-6">⚠️</div>
                    <h2 class="text-2xl font-bold text-red-600 mb-4">Oops!</h2>
                    <p class="text-gray-600 text-lg mb-6">${message}</p>
                    <button onclick="loadNextQuestion()" 
                            class="bg-talon-blue text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors">
                        Try Again
                    </button>
                </div>
            `;
        }

        // Event listeners
        document.getElementById('show-hint-btn').onclick = showHint;
        document.getElementById('submit-answer-btn').onclick = submitAnswer;
        document.getElementById('continue-btn').onclick = loadNextQuestion;

        // Check for preloaded content on page load
        function checkPreloadedContent() {
            try {
                const preloadedData = WelcomeModalIntegration.getPreloadedContent();
                if (preloadedData && preloadedData.lessons) {
                    console.log('Found preloaded content:', preloadedData);
                    usePreloadedContent(preloadedData);
                    return true;
                }
            } catch (error) {
                console.warn('Could not load preloaded content:', error);
            }
            return false;
        }

        function usePreloadedContent(contentData) {
            // Convert preloaded lessons to flashcard format
            const allFlashcards = [];
            
            if (contentData.lessons && Array.isArray(contentData.lessons)) {
                contentData.lessons.forEach(lesson => {
                    if (lesson.flashcards && Array.isArray(lesson.flashcards)) {
                        lesson.flashcards.forEach(flashcard => {
                            allFlashcards.push({
                                id: flashcard.id,
                                question: flashcard.question,
                                question_type: flashcard.question_type,
                                options: flashcard.options || [],
                                correct_answer: flashcard.correct_answer,
                                hint: flashcard.hint,
                                explanation: flashcard.explanation,
                                example_sentence: flashcard.example_sentence || '',
                                lesson_topic: lesson.topic,
                                lesson_title: lesson.title,
                                ai_generated: true,
                                preloaded: true
                            });
                        });
                    }
                });
            }

            if (allFlashcards.length > 0) {
                // 🚀 Set up preloaded flashcard set for instant transitions
                flashcardSet = allFlashcards.slice(0, totalQuestions);
                totalQuestions = Math.min(allFlashcards.length, totalQuestions);
                
                // Show notification about using preloaded content
                showPreloadedNotification(contentData);
                
                // Clear the preloaded content from storage since we're using it
                sessionStorage.removeItem('preloadedLessons');
                
                console.log(`🚀 Using ${totalQuestions} preloaded flashcards from ${contentData.lessons.length} lessons`);
                
                // Start the session immediately with preloaded content
                startFlashcardSession();
                return true;
            }
            return false;
        }

        function showPreloadedNotification(contentData) {
            // Create a temporary notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <div>
                        <div class="font-medium">Ready to practice!</div>
                        <div class="text-sm opacity-90">Using your personalized ${contentData.lessons?.length || 0} lessons</div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Slide in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Slide out after 3 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Check URL parameters for preloaded flag
            const urlParams = new URLSearchParams(window.location.search);
            const hasPreloaded = urlParams.get('preloaded') === 'true';
            
            if (hasPreloaded) {
                // Try to use preloaded content first
                const usedPreloaded = checkPreloadedContent();
                
                if (!usedPreloaded) {
                    // Fall back to regular API call
                    console.log('No preloaded content found, using API');
                    initializeSession();
                    startTimer();
                    preloadFlashcardSet(); // 🚀 Use new preload system
                }
            } else {
                // Regular initialization
                initializeSession();
                startTimer();
                preloadFlashcardSet(); // 🚀 Use new preload system
            }
        });

        // Prevent accidental page refresh during learning
        window.addEventListener('beforeunload', function(e) {
            if (currentQuestion > 0 && currentQuestion < totalQuestions) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>

    <!-- Welcome Modal Integration Script -->
    <script src="{% static 'js/welcome-modal-integration.js' %}"></script>
</body>
</html>
