{% extends 'base.html' %}
{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-100 p-4">
  <div class="w-full max-w-lg bg-white rounded-2xl shadow-xl p-8 space-y-6">

    <!-- Progress Indicator -->
    <div>
      <div class="text-center mb-1 text-sm font-medium text-gray-500">Step <span id="current-step-text">1</span> of 5</div>
      <div class="w-full bg-gray-200 rounded-full h-2.5">
        <div id="progress-bar" class="bg-primary h-2.5 rounded-full" style="width: 20%; transition: width 0.3s ease-in-out;"></div>
      </div>
    </div>

    <form id="onboarding-form">
      <!-- Step 1: Welcome -->
      <div class="onboarding-step" id="step-1">
        <h2 class="text-3xl font-bold text-primary mb-4 text-center">Welcome to TalonTalk!</h2>
        <p class="mb-6 text-gray-600 text-center">Let's personalize your learning experience in just a few quick steps.</p>
        <button type="button" class="btn btn-primary w-full" onclick="navigateToStep(2)">Get Started</button>
      </div>

      <!-- Step 2: Personal Info -->
      <div class="onboarding-step hidden" id="step-2">
        <h2 class="text-2xl font-semibold text-primary mb-6">First, what should we call you?</h2>
        <div class="space-y-4">
          <div>
            <label class="block mb-2 font-medium">Display Name</label>
            <input type="text" name="display_name" class="input input-bordered w-full" placeholder="e.g., Alex" required>
          </div>
          <div>
            <label class="block mb-2 font-medium">What is your native language?</label>
            <select name="native_language" class="select select-bordered w-full" required>
              <option disabled selected>Select your language</option>
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Chinese">Mandarin Chinese</option>
              <option value="Portuguese">Portuguese</option>
              <option value="Italian">Italian</option>
              <option value="Japanese">Japanese</option>
              <option value="Korean">Korean</option>
              <option value="Russian">Russian</option>
              <option value="Arabic">Arabic</option>
              <option value="Hindi">Hindi</option>
              <option value="Other">Other</option>
            </select>
          </div>
        </div>
        <div class="flex justify-between mt-8">
          <button type="button" class="btn btn-ghost" onclick="navigateToStep(1)">Back</button>
          <button type="button" class="btn btn-primary" onclick="navigateToStep(3)">Next</button>
        </div>
      </div>

      <!-- Step 3: Language & Skill Level -->
      <div class="onboarding-step hidden" id="step-3">
        <h2 class="text-2xl font-semibold text-primary mb-6">What language are you excited to learn?</h2>
        <div class="space-y-4">
            <div>
                <label class="block mb-2 font-medium">I want to learn...</label>
                <select name="target_language" class="select select-bordered w-full" required>
                    <option disabled selected>Select a language</option>
                    <option value="French">French</option>
                    <option value="Spanish">Spanish</option>
                    <option value="German">German</option>
                    <option value="Japanese">Japanese</option>
                    <option value="Italian">Italian</option>
                    <option value="Portuguese">Portuguese</option>
                    <option value="Chinese">Mandarin Chinese</option>
                    <option value="Korean">Korean</option>
                    <option value="Russian">Russian</option>
                    <option value="Arabic">Arabic</option>
                    <option value="Dutch">Dutch</option>
                    <option value="Swedish">Swedish</option>
                </select>
            </div>
            <div>
                <label class="block mb-2 font-medium">How would you describe your current skill level?</label>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button type="button" class="btn btn-outline h-20 flex flex-col items-center justify-center skill-btn" onclick="selectSkill('Beginner', this)">
                        <span class="font-semibold">Beginner</span>
                        <span class="text-xs text-gray-500 mt-1">Just starting</span>
                    </button>
                    <button type="button" class="btn btn-outline h-20 flex flex-col items-center justify-center skill-btn" onclick="selectSkill('Intermediate', this)">
                        <span class="font-semibold">Intermediate</span>
                        <span class="text-xs text-gray-500 mt-1">Some experience</span>
                    </button>
                    <button type="button" class="btn btn-outline h-20 flex flex-col items-center justify-center skill-btn" onclick="selectSkill('Advanced', this)">
                        <span class="font-semibold">Advanced</span>
                        <span class="text-xs text-gray-500 mt-1">Pretty fluent</span>
                    </button>
                </div>
                <input type="hidden" name="skill_level" id="skill-level-input" required>
            </div>
        </div>
        <div class="flex justify-between mt-8">
            <button type="button" class="btn btn-ghost" onclick="navigateToStep(2)">Back</button>
            <button type="button" class="btn btn-primary" onclick="navigateToStep(4)">Next</button>
        </div>
      </div>

      <!-- Step 4: Learning Goals -->
      <div class="onboarding-step hidden" id="step-4">
        <h2 class="text-2xl font-semibold text-primary mb-6">What's your main goal?</h2>
        <p class="mb-4 text-gray-500 text-sm">This helps us recommend the best learning path for you.</p>
        <select name="main_goal" class="select select-bordered w-full" required>
          <option disabled selected>Select a goal</option>
          <option value="Conversational Fluency">Conversational Fluency</option>
          <option value="Traveling Abroad">Traveling Abroad</option>
          <option value="Career & Business">Career & Business</option>
          <option value="Exams & Certification">Exams & Certification</option>
          <option value="Connecting with Family">Connecting with Family</option>
          <option value="Academic Studies">Academic Studies</option>
          <option value="Just for fun!">Just for fun!</option>
        </select>
        <div class="flex justify-between mt-8">
          <button type="button" class="btn btn-ghost" onclick="navigateToStep(3)">Back</button>
          <button type="button" class="btn btn-primary" onclick="navigateToStep(5)">Finish</button>
        </div>
      </div>

      <!-- Step 5: Complete -->
      <div class="onboarding-step hidden" id="step-5">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h2 class="text-2xl font-bold text-primary mt-4 mb-2">You're all set!</h2>
          <p class="mb-6 text-gray-600">We've personalized your dashboard to help you reach your goals. Your TalonTalk journey begins now.</p>
          <button type="button" class="btn btn-success w-full" onclick="finishOnboarding()">Go to Dashboard</button>
        </div>
      </div>
    </form>
  </div>
</div>

<script>
let currentStep = 1;
const totalSteps = 5;

function navigateToStep(step) {
  // Simple validation for required fields in the current step before proceeding
  if (step > currentStep) {
    const currentStepElement = document.getElementById('step-' + currentStep);
    const inputs = currentStepElement.querySelectorAll('[required]');
    let allValid = true;
    
    inputs.forEach(input => {
      if (!input.value) {
        input.classList.add('input-error', 'select-error'); // Highlight empty required fields
        allValid = false;
      } else {
        input.classList.remove('input-error', 'select-error');
      }
    });

    if (!allValid) {
      // Show a brief error message
      const existingError = currentStepElement.querySelector('.error-message');
      if (existingError) existingError.remove();
      
      const msg = document.createElement('div');
      msg.className = 'error-message text-red-500 text-sm mt-2';
      msg.textContent = 'Please fill in all required fields before continuing.';
      currentStepElement.appendChild(msg);
      setTimeout(() => msg.remove(), 3000);
      return;
    }
  }
  
  currentStep = step;
  
  // Hide all steps
  document.querySelectorAll('.onboarding-step').forEach(el => el.classList.add('hidden'));
  
  // Show the target step
  document.getElementById('step-' + step).classList.remove('hidden');

  // Update Progress Bar
  const progressBar = document.getElementById('progress-bar');
  const progress = (step / totalSteps) * 100;
  progressBar.style.width = progress + '%';
  document.getElementById('current-step-text').innerText = step;
}

function selectSkill(skill, button) {
    // Set the value of the hidden input
    document.getElementById('skill-level-input').value = skill;

    // Visual feedback for the selected button
    document.querySelectorAll('.skill-btn').forEach(btn => {
        btn.classList.remove('btn-active');
    });
    button.classList.add('btn-active');
}

function finishOnboarding() {
  const form = document.getElementById('onboarding-form');
  const formData = new FormData(form);
  const data = Object.fromEntries(formData.entries());

  console.log('Onboarding Data:', data); // For debugging

  // TODO: Send 'data' object to your backend via an AJAX/fetch call
  /*
  fetch('/onboarding/complete/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': '{{ csrf_token }}'
    },
    body: JSON.stringify(data)
  })
  .then(response => response.json())
  .then(result => {
    console.log('Success:', result);
    window.location.href = '/dashboard/';
  })
  .catch(error => {
    console.error('Error:', error);
    // Still redirect on error for now
    window.location.href = '/dashboard/';
  });
  */
  
  // For now, we'll just redirect
  window.location.href = '/dashboard/';
}
</script>
{% endblock %}
