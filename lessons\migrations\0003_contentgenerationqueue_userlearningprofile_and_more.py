# Generated by Django 5.2.3 on 2025-07-03 23:52

import datetime
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ContentGenerationQueue',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content_type', models.CharField(choices=[('flashcard', 'Flashcard'), ('lesson', 'Lesson'), ('exercise', 'Exercise'), ('quiz', 'Quiz'), ('pronunciation', 'Pronunciation')], max_length=20)),
                ('language', models.CharField(max_length=50)),
                ('difficulty', models.CharField(choices=[('beginner', 'Beginner'), ('elementary', 'Elementary'), ('intermediate', 'Intermediate'), ('upper_intermediate', 'Upper Intermediate'), ('advanced', 'Advanced'), ('proficient', 'Proficient')], max_length=20)),
                ('topic', models.CharField(blank=True, max_length=100, null=True)),
                ('quantity', models.IntegerField(default=5)),
                ('priority', models.IntegerField(default=5)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('generating', 'Generating'), ('ready', 'Ready'), ('expired', 'Expired'), ('error', 'Error')], default='pending', max_length=20)),
                ('attempts', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('result_content_id', models.UUIDField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-priority', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserLearningProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('preferred_difficulty', models.CharField(choices=[('beginner', 'Beginner'), ('elementary', 'Elementary'), ('intermediate', 'Intermediate'), ('upper_intermediate', 'Upper Intermediate'), ('advanced', 'Advanced'), ('proficient', 'Proficient')], default='beginner', max_length=20)),
                ('preferred_content_types', models.JSONField(default=list)),
                ('learning_pace', models.CharField(default='moderate', max_length=20)),
                ('average_accuracy', models.FloatField(default=0.0)),
                ('total_sessions', models.IntegerField(default=0)),
                ('total_practice_time', models.DurationField(default=datetime.timedelta)),
                ('strengths', models.JSONField(default=list)),
                ('weaknesses', models.JSONField(default=list)),
                ('learning_patterns', models.JSONField(default=dict)),
                ('motivation_level', models.FloatField(default=0.5)),
                ('engagement_score', models.FloatField(default=0.5)),
                ('learning_style', models.CharField(default='visual', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PreloadedContent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content_type', models.CharField(choices=[('flashcard', 'Flashcard'), ('lesson', 'Lesson'), ('exercise', 'Exercise'), ('quiz', 'Quiz'), ('pronunciation', 'Pronunciation')], max_length=20)),
                ('language', models.CharField(default='spanish', max_length=50)),
                ('difficulty', models.CharField(choices=[('beginner', 'Beginner'), ('elementary', 'Elementary'), ('intermediate', 'Intermediate'), ('upper_intermediate', 'Upper Intermediate'), ('advanced', 'Advanced'), ('proficient', 'Proficient')], default='beginner', max_length=20)),
                ('topic', models.CharField(blank=True, max_length=100, null=True)),
                ('content_data', models.JSONField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('generating', 'Generating'), ('ready', 'Ready'), ('expired', 'Expired'), ('error', 'Error')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('expires_at', models.DateTimeField()),
                ('last_accessed', models.DateTimeField(blank=True, null=True)),
                ('access_count', models.IntegerField(default=0)),
                ('generation_time_ms', models.IntegerField(blank=True, null=True)),
                ('adaptive_score', models.FloatField(default=0.0)),
                ('personalization_factors', models.JSONField(default=dict)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'content_type', 'status'], name='lessons_pre_user_id_da73b0_idx'), models.Index(fields=['language', 'difficulty', 'status'], name='lessons_pre_languag_a47911_idx'), models.Index(fields=['expires_at'], name='lessons_pre_expires_5abbcb_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserPerformanceMetrics',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_id', models.UUIDField()),
                ('content_id', models.UUIDField(blank=True, null=True)),
                ('question_count', models.IntegerField(default=0)),
                ('correct_answers', models.IntegerField(default=0)),
                ('time_spent', models.DurationField()),
                ('completion_rate', models.FloatField(default=0.0)),
                ('response_times', models.JSONField(default=list)),
                ('error_patterns', models.JSONField(default=dict)),
                ('hint_usage', models.IntegerField(default=0)),
                ('difficulty_attempted', models.CharField(choices=[('beginner', 'Beginner'), ('elementary', 'Elementary'), ('intermediate', 'Intermediate'), ('upper_intermediate', 'Upper Intermediate'), ('advanced', 'Advanced'), ('proficient', 'Proficient')], max_length=20)),
                ('topics_covered', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='lessons_use_user_id_5493d2_idx'), models.Index(fields=['session_id'], name='lessons_use_session_da951d_idx')],
            },
        ),
    ]
