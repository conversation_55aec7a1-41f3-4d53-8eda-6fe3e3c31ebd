/**
 * Utility functions for the TalonTalk C.A.R.E. system
 */
import type { Debounced, Throttled, UtilityFunctions, BrowserInfo } from '../types/common.types.js';
/**
 * Debounce function calls
 */
export declare function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): Debounced<T>;
/**
 * Throttle function calls
 */
export declare function throttle<T extends (...args: any[]) => any>(fn: T, limit: number): Throttled<T>;
/**
 * Format date using a simple format string
 */
export declare function formatDate(date: Date, format: string): string;
/**
 * Format seconds into readable time string
 */
export declare function formatTime(seconds: number): string;
/**
 * Generate unique ID with optional prefix
 */
export declare function generateId(prefix?: string): string;
/**
 * Convert string to URL-friendly slug
 */
export declare function slugify(text: string): string;
/**
 * Capitalize first letter of string
 */
export declare function capitalize(text: string): string;
/**
 * Truncate text with optional suffix
 */
export declare function truncate(text: string, length: number, suffix?: string): string;
/**
 * Parse URL query string into object
 */
export declare function parseQuery(search: string): Record<string, string>;
/**
 * Build query string from object
 */
export declare function buildQuery(params: Record<string, string | number | boolean>): string;
/**
 * Escape HTML entities
 */
export declare function escapeHtml(text: string): string;
/**
 * Get browser information
 */
export declare function getBrowserInfo(): BrowserInfo;
/**
 * Deep clone an object
 */
export declare function deepClone<T>(obj: T): T;
/**
 * Check if device has touch capabilities
 */
export declare function isTouchDevice(): boolean;
/**
 * Get viewport dimensions
 */
export declare function getViewportSize(): {
    width: number;
    height: number;
};
/**
 * Smooth scroll to element
 */
export declare function scrollToElement(element: Element, offset?: number): void;
/**
 * Wait for specified milliseconds
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * Get all utility functions as a single object
 */
export declare const utils: UtilityFunctions;
/**
 * Export all utilities as default
 */
declare const _default: {
    debounce: typeof debounce;
    throttle: typeof throttle;
    formatDate: typeof formatDate;
    formatTime: typeof formatTime;
    generateId: typeof generateId;
    slugify: typeof slugify;
    capitalize: typeof capitalize;
    truncate: typeof truncate;
    parseQuery: typeof parseQuery;
    buildQuery: typeof buildQuery;
    escapeHtml: typeof escapeHtml;
    getBrowserInfo: typeof getBrowserInfo;
    deepClone: typeof deepClone;
    isTouchDevice: typeof isTouchDevice;
    getViewportSize: typeof getViewportSize;
    scrollToElement: typeof scrollToElement;
    sleep: typeof sleep;
    utils: UtilityFunctions;
};
export default _default;
//# sourceMappingURL=index.d.ts.map