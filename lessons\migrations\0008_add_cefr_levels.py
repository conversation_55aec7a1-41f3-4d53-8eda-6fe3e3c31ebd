# Generated by Django 5.2.3 on 2025-07-07 18:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('lessons', '0007_alter_lesson_options_contentitem_progressive_hints_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contentitem',
            name='cefr_level',
            field=models.CharField(choices=[('A1', 'A1 - Breakthrough (Beginner)'), ('A2', 'A2 - Waystage (Elementary)'), ('B1', 'B1 - Threshold (Intermediate)'), ('B2', 'B2 - Vantage (Upper Intermediate)'), ('C1', 'C1 - Proficiency (Advanced)'), ('C2', 'C2 - Mastery (Near Native)')], db_index=True, default='A1', help_text='Common European Framework of Reference level', max_length=2),
        ),
        migrations.AddField(
            model_name='lesson',
            name='cefr_level',
            field=models.Char<PERSON>ield(choices=[('A1', 'A1 - Breakthrough (Beginner)'), ('A2', 'A2 - Waystage (Elementary)'), ('B1', 'B1 - Threshold (Intermediate)'), ('B2', 'B2 - Vantage (Upper Intermediate)'), ('C1', 'C1 - Proficiency (Advanced)'), ('C2', 'C2 - Mastery (Near Native)')], db_index=True, default='A1', help_text='Common European Framework of Reference level', max_length=2),
        ),
    ]
