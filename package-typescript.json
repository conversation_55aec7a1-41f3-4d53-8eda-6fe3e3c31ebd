{"name": "talontalk-typescript", "version": "1.0.0", "description": "TypeScript implementation of TalonTalk C.A.R.E. framework", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc && npm run copy-static", "build:watch": "tsc --watch", "copy-static": "powershell -Command \"Copy-Item 'talontalk/static/js/dist/*' 'talontalk/static/js/' -Recurse -Force\"", "dev": "tsc --watch", "clean": "powershell -Command \"Remove-Item 'dist' -Recurse -Force -ErrorAction SilentlyContinue; Remove-Item 'talontalk/static/js/dist' -Recurse -Force -ErrorAction SilentlyContinue\"", "type-check": "tsc --noEmit", "lint": "echo 'Linting TypeScript files...' && tsc --noEmit", "test": "echo 'Tests coming soon...'", "deploy": "npm run clean && npm run build && npm run copy-static"}, "keywords": ["typescript", "language-learning", "care-framework", "education"], "author": "TalonTalk Development Team", "license": "MIT", "devDependencies": {"typescript": "^5.0.0"}, "dependencies": {}, "engines": {"node": ">=16.0.0"}}