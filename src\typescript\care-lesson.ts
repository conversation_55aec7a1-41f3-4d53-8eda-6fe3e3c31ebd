/**
 * C.A.R.E. Framework Interactive Lesson System - TypeScript Edition
 * Handles phase navigation, progress tracking, and AI tutor interaction with full type safety
 */

import type {
    CAREPhaseContent,
    CAREPhaseAPIResponse,
    Exercise,
    MultipleChoiceExercise,
    TranslationExercise,
    PronunciationExercise,
    EventCallback,
    Scenario,
    CulturalContext,
    KeyPhrase,
    VocabularyItem,
    GrammarSection
} from './types/index.js';

import { CAREError } from './types/index.js';

interface CARELessonManagerConfig {
    lessonId: string;
    initialPhase?: string;
    onPhaseChange?: EventCallback<string>;
    onLessonComplete?: EventCallback<void>;
    debugMode?: boolean;
}

class CARELessonManager {
    private currentPhase: string = 'contextualize';
    private phaseProgress: number = 0;
    private readonly totalPhases: number = 4;
    private readonly phases: readonly string[] = ['contextualize', 'acquire', 'reinforce', 'extend'] as const;
    private readonly lessonId: string;
    private debugMode: boolean;
    private eventCallbacks: Map<string, EventCallback[]> = new Map();

    constructor(config: CARELessonManagerConfig) {
        this.lessonId = config.lessonId;
        this.debugMode = config.debugMode ?? false;

        if (config.initialPhase && this.phases.includes(config.initialPhase)) {
            this.currentPhase = config.initialPhase;
        }

        if (config.onPhaseChange) {
            this.on('phase_change', config.onPhaseChange);
        }

        if (config.onLessonComplete) {
            this.on('lesson_complete', config.onLessonComplete);
        }

        this.init();
    }

    private init(): void {
        this.setupEventListeners();
        this.loadPhase(this.currentPhase);
        this.updateProgressBar();
    }

    private setupEventListeners(): void {
        // Phase navigation buttons
        const phaseNavItems = document.querySelectorAll<HTMLElement>('.care-nav-item');
        phaseNavItems.forEach(btn => {
            btn.addEventListener('click', (e: Event) => {
                const target = e.target as HTMLElement;
                const phase = target.dataset.phase || target.closest<HTMLElement>('.care-nav-item')?.dataset.phase;
                if (phase) {
                    this.loadPhase(phase);
                }
            });
        });

        // Phase indicators (also clickable)
        const phaseIndicators = document.querySelectorAll<HTMLElement>('.care-phase-indicator');
        phaseIndicators.forEach(btn => {
            btn.addEventListener('click', (e: Event) => {
                const target = e.target as HTMLElement;
                const phase = target.dataset.phase || target.closest<HTMLElement>('.care-phase-indicator')?.dataset.phase;
                if (phase) {
                    this.loadPhase(phase);
                }
            });
        });

        // Next phase buttons
        const nextPhaseButtons = document.querySelectorAll<HTMLButtonElement>('.next-phase-btn');
        nextPhaseButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.nextPhase();
            });
        });

        // AI Tutor setup
        this.setupAITutorListeners();
    }

    private setupAITutorListeners(): void {
        const aiTutorBtn = document.getElementById('aiTutorBtn');
        const closeTutorBtn = document.getElementById('closeTutorBtn');
        const sendTutorBtn = document.getElementById('sendTutorBtn');
        const tutorInput = document.getElementById('tutorInput') as HTMLInputElement;

        if (aiTutorBtn) {
            aiTutorBtn.addEventListener('click', () => this.openAITutor());
        }

        if (closeTutorBtn) {
            closeTutorBtn.addEventListener('click', () => this.closeAITutor());
        }

        if (sendTutorBtn) {
            sendTutorBtn.addEventListener('click', () => this.sendTutorMessage());
        }

        if (tutorInput) {
            tutorInput.addEventListener('keypress', (e: KeyboardEvent) => {
                if (e.key === 'Enter') {
                    this.sendTutorMessage();
                }
            });
        }
    }

    public loadPhase(phaseName: string): void {
        this.log(`Loading phase: ${phaseName}`);

        if (!this.phases.includes(phaseName)) {
            throw new CAREError(`Invalid phase: ${phaseName}`, 'INVALID_PHASE', phaseName);
        }

        // Hide all phases
        const allPhases = document.querySelectorAll<HTMLElement>('.care-phase-content');
        allPhases.forEach(content => {
            content.classList.add('hidden');
        });

        // Show target phase
        const phaseContent = document.getElementById(`${phaseName}Phase`);
        if (phaseContent) {
            phaseContent.classList.remove('hidden');
            phaseContent.classList.add('care-phase-enter');

            // Trigger animation
            setTimeout(() => {
                phaseContent.classList.remove('care-phase-enter');
                phaseContent.classList.add('care-phase-active');
            }, 50);
        }

        // Update phase indicators
        this.updatePhaseIndicators(phaseName);

        // Update current phase
        this.currentPhase = phaseName;
        this.phaseProgress = this.phases.indexOf(phaseName) + 1;
        this.updateProgressBar();

        // Load phase-specific content
        this.loadPhaseContent(phaseName);

        // Emit phase change event
        this.emit('phase_change', phaseName);
    }

    private async loadPhaseContent(phaseName: string): Promise<void> {
        this.log(`🔄 Loading content for phase: ${phaseName}`);

        try {
            const response = await fetch(`/care/api/phase/${phaseName}/`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json',
                },
            });

            this.log(`📡 API Response for ${phaseName}:`, response.status);

            if (!response.ok) {
                throw new CAREError(
                    `Failed to load phase content: ${response.status}`,
                    'API_ERROR',
                    phaseName,
                    { status: response.status }
                );
            }

            const data: CAREPhaseAPIResponse = await response.json();
            this.log(`📊 Data received for ${phaseName}:`, data);

            if (data.success && data.content) {
                this.log(`✅ Rendering content for ${phaseName}`);
                this.renderPhaseContent(phaseName, data.content);
            } else {
                throw new CAREError(
                    data.error || 'Unknown error loading phase content',
                    'CONTENT_ERROR',
                    phaseName,
                    data
                );
            }
        } catch (error) {
            this.log(`❌ Error loading ${phaseName} content:`, error);
            this.showFallbackContent(phaseName);

            if (error instanceof CAREError) {
                throw error;
            } else {
                throw new CAREError(
                    `Network error loading ${phaseName}`,
                    'NETWORK_ERROR',
                    phaseName,
                    error
                );
            }
        }
    }

    private renderPhaseContent(phaseName: string, content: CAREPhaseContent): void {
        this.log(`🎨 Rendering ${phaseName} with content:`, content);

        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (!phaseContainer) {
            throw new CAREError(
                `Phase container not found: ${phaseName}Phase`,
                'CONTAINER_NOT_FOUND',
                phaseName
            );
        }

        // Generate phase-specific HTML
        const contentHTML = this.generatePhaseHTML(phaseName, content);
        this.log(`🔧 Generated HTML length for ${phaseName}:`, contentHTML.length);

        phaseContainer.innerHTML = contentHTML;

        // Reinitialize event listeners for new content
        this.setupPhaseSpecificListeners(phaseName);

        this.log(`✅ Phase ${phaseName} rendered successfully`);
    }

    private generatePhaseHTML(phaseName: string, content: CAREPhaseContent): string {
        switch (phaseName) {
            case 'contextualize':
                return this.generateContextualizeHTML(content);
            case 'acquire':
                return this.generateAcquireHTML(content);
            case 'reinforce':
                return this.generateReinforceHTML(content);
            case 'extend':
                return this.generateExtendHTML(content);
            default:
                return '<p>Phase content loading...</p>';
        }
    }

    private generateContextualizeHTML(content: CAREPhaseContent): string {
        const scenario: Scenario = content.scenario || {} as Scenario;
        const culturalContext: CulturalContext = content.cultural_context || {} as CulturalContext;
        const keyPhrases: KeyPhrase[] = content.key_phrases || [];

        return `
      <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white p-6">
          <div class="flex items-center gap-3 mb-2">
            <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <i class="fas fa-globe-americas text-lg"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold">Contextualize</h2>
              <p class="text-emerald-100">Set the scene for your learning journey</p>
            </div>
          </div>
        </div>
        
        <!-- Content -->
        <div class="p-6 space-y-6">
          <!-- Scenario Card -->
          <div class="bg-gray-50 rounded-xl p-6">
            <div class="flex items-start gap-3 mb-4">
              <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-map-marker-alt text-emerald-600"></i>
              </div>
              <div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">${scenario.title || 'Real-World Scenario'}</h3>
                <p class="text-gray-700 leading-relaxed">
                  ${scenario.description || "Imagine you're visiting a café in Madrid during a busy morning. You want to order coffee and a pastry, but you need to know the right phrases and cultural context."}
                </p>
                ${scenario.location ? `<div class="mt-3 flex items-center gap-2 text-sm text-gray-600">
                  <i class="fas fa-location-dot"></i>
                  <span>${scenario.location}</span>
                </div>` : ''}
              </div>
            </div>
          </div>
          
          <!-- Two Column Layout -->
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Cultural Context -->
            <div class="bg-blue-50 rounded-xl p-6">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-users text-blue-600"></i>
                </div>
                <h4 class="text-lg font-semibold text-gray-900">${culturalContext.title || 'Cultural Context'}</h4>
              </div>
              <ul class="space-y-3">
                ${culturalContext.facts ? culturalContext.facts.map(fact => `
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>${fact}</span>
                  </li>
                `).join('') : `
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Spanish cafés open early (around 6 AM)</span>
                  </li>
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Locals often stand at the bar for coffee</span>
                  </li>
                  <li class="flex items-start gap-3 text-gray-700">
                    <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Tipping is appreciated but not mandatory</span>
                  </li>
                `}
              </ul>
            </div>
            
            <!-- Key Phrases -->
            <div class="bg-purple-50 rounded-xl p-6">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-comment text-purple-600"></i>
                </div>
                <h4 class="text-lg font-semibold text-gray-900">Key Phrases</h4>
              </div>
              <div class="space-y-4">
                ${keyPhrases.slice(0, 3).map(phrase => `
                  <div class="bg-white rounded-lg p-4 border border-purple-100">
                    <div class="text-lg font-semibold text-gray-900 mb-1">${phrase.spanish}</div>
                    <div class="text-gray-600 mb-2">${phrase.english}</div>
                    <div class="text-sm text-purple-600 font-mono">[${phrase.pronunciation}]</div>
                  </div>
                `).join('')}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Button -->
        <div class="p-6 bg-gray-50 border-t border-gray-100">
          <div class="flex justify-center">
            <button class="next-phase-btn bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all">
              Ready to Learn Vocabulary →
            </button>
          </div>
        </div>
      </div>
    `;
    }

    private generateAcquireHTML(content: CAREPhaseContent): string {
        const vocabulary: VocabularyItem[] = content.vocabulary || [];
        const grammar: GrammarSection = content.grammar || {} as GrammarSection;

        return `
      <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6">
          <div class="flex items-center gap-3 mb-2">
            <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <i class="fas fa-lightbulb text-lg"></i>
            </div>
            <div>
              <h2 class="text-2xl font-bold">Acquire</h2>
              <p class="text-blue-100">Learn essential vocabulary and phrases</p>
            </div>
          </div>
        </div>
        
        <!-- Content -->
        <div class="p-6 space-y-6">
          <!-- Two Column Layout -->
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Vocabulary Section -->
            <div class="space-y-4">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-book text-blue-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">Essential Vocabulary</h3>
              </div>
              <div class="space-y-3">
                ${vocabulary.map(item => `
                  <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                    <div class="flex justify-between items-start mb-2">
                      <div class="text-lg font-bold text-gray-900">${item.word}</div>
                      <div class="text-sm text-blue-600 font-medium">${item.translation}</div>
                    </div>
                    <div class="text-sm text-gray-600 font-mono mb-2">[${item.pronunciation}]</div>
                    <div class="border-t border-gray-200 pt-2 mt-2">
                      <div class="text-sm font-medium text-gray-700">${item.example}</div>
                      <div class="text-sm text-gray-500">${item.example_translation}</div>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
            
            <!-- Grammar Section -->
            <div class="space-y-4">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-cogs text-green-600"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900">${grammar.topic || 'Grammar Patterns'}</h3>
              </div>
              <div class="space-y-4">
                ${grammar.structures ? grammar.structures.map(structure => `
                  <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                    <div class="text-lg font-bold text-gray-900 mb-2">${structure.pattern}</div>
                    <div class="text-gray-600 mb-3">${structure.meaning}</div>
                    <div class="space-y-2">
                      ${structure.examples.map(example => `
                        <div class="bg-white rounded p-3 text-sm border border-green-200">
                          ${example}
                        </div>
                      `).join('')}
                    </div>
                  </div>
                `).join('') : `
                  <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                    <div class="text-lg font-bold text-gray-900 mb-2">Basic Greetings</div>
                    <div class="text-gray-600 mb-3">Essential phrases for polite conversation</div>
                    <div class="bg-white rounded p-3 text-sm border border-green-200">
                      "Buenos días" - Good morning
                    </div>
                  </div>
                `}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Button -->
        <div class="p-6 bg-gray-50 border-t border-gray-100">
          <div class="flex justify-center">
            <button class="next-phase-btn bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all">
              Practice What You Learned →
            </button>
          </div>
        </div>
      </div>
    `;
    }

    private generateReinforceHTML(content: CAREPhaseContent): string {
        const exercises: Exercise[] = content.exercises || [];

        return `
      <div class="reinforce-theme text-white p-8 rounded-2xl shadow-xl">
        <div class="text-center mb-8">
          <h2 class="text-4xl font-bold mb-4">💪 Reinforce</h2>
          <p class="text-xl opacity-90">Practice and strengthen your knowledge</p>
        </div>
        
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
          <h3 class="text-2xl font-semibold mb-4">Interactive Practice</h3>
          <div id="practiceArea" class="space-y-6">
            ${exercises.map((exercise) => {
            if (exercise.type === 'multiple_choice') {
                const mcExercise = exercise as MultipleChoiceExercise;
                return `
                  <div class="practice-question bg-white/20 rounded-lg p-4">
                    <p class="text-lg mb-3">${mcExercise.question}</p>
                    <div class="grid grid-cols-1 gap-2">
                      ${mcExercise.options.map((option, optIndex) => `
                        <button class="practice-option bg-white/20 hover:bg-white/30 p-3 rounded-lg transition-colors text-left" 
                                data-answer="${optIndex === mcExercise.correct ? 'correct' : 'wrong'}"
                                data-explanation="${mcExercise.explanation}">
                          ${option}
                        </button>
                      `).join('')}
                    </div>
                  </div>
                `;
            } else if (exercise.type === 'translation') {
                const transExercise = exercise as TranslationExercise;
                return `
                  <div class="practice-question bg-white/20 rounded-lg p-4">
                    <p class="text-lg mb-3">${transExercise.question}</p>
                    <input type="text" class="translation-input w-full p-3 rounded-lg bg-white/20 text-white placeholder-gray-300" 
                           placeholder="Type your translation here..."
                           data-answer="${transExercise.answer}"
                           data-explanation="${transExercise.explanation}">
                    <button class="check-translation mt-2 bg-white/30 hover:bg-white/40 px-4 py-2 rounded-lg">Check Answer</button>
                  </div>
                `;
            } else if (exercise.type === 'pronunciation') {
                const pronExercise = exercise as PronunciationExercise;
                return `
                  <div class="practice-question bg-white/20 rounded-lg p-4">
                    <p class="text-lg mb-2">Practice pronunciation:</p>
                    <div class="text-center">
                      <p class="text-2xl font-bold mb-2">${pronExercise.phrase}</p>
                      <p class="text-lg opacity-80 mb-2">${pronExercise.translation}</p>
                      <p class="text-sm opacity-60 mb-4">[${pronExercise.phonetic}]</p>
                      <button class="pronunciation-btn bg-white/30 hover:bg-white/40 px-6 py-3 rounded-lg">
                        🔊 Listen & Practice
                      </button>
                    </div>
                  </div>
                `;
            }
            return '';
        }).join('')}
          </div>
        </div>
        
        <div class="text-center">
          <button class="next-phase-btn bg-white text-purple-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
            Apply in Real Conversations →
          </button>
        </div>
      </div>
    `;
    }

    private generateExtendHTML(content: CAREPhaseContent): string {
        const realWorldApps = content.real_world_applications || [];
        const expansionTopics = content.expansion_topics || [];
        const homework = content.homework;

        return `
      <div class="extend-theme text-white p-8 rounded-2xl shadow-xl">
        <div class="text-center mb-8">
          <h2 class="text-4xl font-bold mb-4">🚀 Extend</h2>
          <p class="text-xl opacity-90">Apply your knowledge in real-world situations</p>
        </div>
        
        <div class="grid md:grid-cols-2 gap-6 mb-8">
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <h3 class="text-2xl font-semibold mb-4">Real-World Practice</h3>
            <div class="space-y-4">
              ${realWorldApps.map(app => `
                <div class="bg-white/20 rounded-lg p-4">
                  <h4 class="font-bold text-lg mb-2">${app.title}</h4>
                  <p class="text-sm opacity-90 mb-3">${app.description}</p>
                  <div class="text-xs opacity-80 mb-2">Scenario: ${app.scenario}</div>
                  <div class="space-y-1">
                    ${app.tasks ? app.tasks.map(task => `
                      <div class="text-xs bg-white/10 rounded px-2 py-1">• ${task}</div>
                    `).join('') : ''}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
          
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <h3 class="text-2xl font-semibold mb-4">Expand Your Knowledge</h3>
            <div class="space-y-4">
              ${expansionTopics.map(topic => `
                <div class="bg-white/20 rounded-lg p-4">
                  <h4 class="font-bold mb-2">${topic.topic}</h4>
                  <div class="text-sm space-y-2">
                    <div>
                      <strong>Vocabulary:</strong> ${topic.vocabulary.join(', ')}
                    </div>
                    <div>
                      <strong>Phrases:</strong>
                      ${topic.phrases.map(phrase => `<div class="ml-2">• ${phrase}</div>`).join('')}
                    </div>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
        
        ${homework?.title ? `
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
            <h3 class="text-2xl font-semibold mb-4">${homework.title}</h3>
            <p class="mb-4">${homework.description}</p>
            <div class="space-y-2">
              ${homework.steps ? homework.steps.map((step, index) => `
                <div class="flex items-start space-x-2">
                  <span class="bg-white/20 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">${index + 1}</span>
                  <span>${step}</span>
                </div>
              `).join('') : ''}
            </div>
          </div>
        ` : ''}
        
        <div class="text-center">
          <button class="next-phase-btn bg-white text-amber-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
            Complete Lesson ✓
          </button>
        </div>
      </div>
    `;
    }

    private showFallbackContent(phaseName: string): void {
        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (!phaseContainer) return;

        phaseContainer.innerHTML = `
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
        <div class="text-yellow-600 mb-2">
          <i class="fas fa-exclamation-triangle text-2xl"></i>
        </div>
        <h3 class="text-lg font-semibold text-yellow-800 mb-2">Content Loading Error</h3>
        <p class="text-yellow-700 mb-4">We're having trouble loading this phase content.</p>
        <button class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg" 
                onclick="window.careManager.loadPhase('${phaseName}')">
          Try Again
        </button>
      </div>
    `;
    }

    private setupPhaseSpecificListeners(phaseName: string): void {
        // Setup event listeners for dynamically generated content
        const nextButtons = document.querySelectorAll<HTMLButtonElement>('.next-phase-btn');
        nextButtons.forEach(btn => {
            btn.addEventListener('click', () => this.nextPhase());
        });

        // Phase-specific listeners
        switch (phaseName) {
            case 'reinforce':
                this.setupReinforceListeners();
                break;
            case 'extend':
                this.setupExtendListeners();
                break;
        }
    }

    private setupReinforceListeners(): void {
        // Practice option listeners
        const practiceOptions = document.querySelectorAll<HTMLButtonElement>('.practice-option');
        practiceOptions.forEach(btn => {
            btn.addEventListener('click', () => this.handlePracticeAnswer(btn));
        });

        // Translation check listeners
        const checkButtons = document.querySelectorAll<HTMLButtonElement>('.check-translation');
        checkButtons.forEach(btn => {
            btn.addEventListener('click', () => this.handleTranslationCheck(btn));
        });

        // Pronunciation listeners
        const pronButtons = document.querySelectorAll<HTMLButtonElement>('.pronunciation-btn');
        pronButtons.forEach(btn => {
            btn.addEventListener('click', () => this.handlePronunciation(btn));
        });
    }

    private setupExtendListeners(): void {
        // Add any extend-specific event listeners here
        this.log('Extend phase listeners setup complete');
    }

    private handlePracticeAnswer(button: HTMLButtonElement): void {
        const isCorrect = button.dataset.answer === 'correct';

        if (isCorrect) {
            button.classList.add('bg-green-500');
            button.innerHTML += ' ✓';
        } else {
            button.classList.add('bg-red-500');
            button.innerHTML += ' ✗';
        }

        // Disable all options
        const allOptions = document.querySelectorAll<HTMLButtonElement>('.practice-option');
        allOptions.forEach(btn => {
            btn.disabled = true;
            btn.classList.add('opacity-75');
        });

        // Show feedback
        setTimeout(() => {
            const explanation = button.dataset.explanation || '';
            alert(isCorrect ? 'Correct! Well done!' : `Not quite right. ${explanation}`);
        }, 500);
    }

    private handleTranslationCheck(button: HTMLButtonElement): void {
        const input = button.parentElement?.querySelector<HTMLInputElement>('.translation-input');
        if (!input) return;

        const userAnswer = input.value.trim().toLowerCase();
        const correctAnswer = input.dataset.answer?.toLowerCase() || '';
        const isCorrect = userAnswer === correctAnswer;

        input.classList.add(isCorrect ? 'border-green-500' : 'border-red-500');

        setTimeout(() => {
            const explanation = input.dataset.explanation || '';
            alert(isCorrect ? 'Excellent translation!' : `Close! The correct answer is: "${correctAnswer}". ${explanation}`);
        }, 300);
    }

    private handlePronunciation(button: HTMLButtonElement): void {
        // For now, just provide feedback. In a real implementation, this would integrate with speech recognition
        const phrase = button.parentElement?.querySelector('.text-2xl')?.textContent || '';

        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(phrase);
            utterance.lang = 'es-ES';
            utterance.rate = 0.8;
            speechSynthesis.speak(utterance);
        } else {
            alert(`Practice saying: "${phrase}"`);
        }
    }

    public nextPhase(): void {
        const currentIndex = this.phases.indexOf(this.currentPhase);
        if (currentIndex < this.phases.length - 1) {
            const nextPhase = this.phases[currentIndex + 1];
            this.loadPhase(nextPhase);
        } else {
            this.completeLesson();
        }
    }

    private completeLesson(): void {
        this.log('🎉 Lesson completed!');
        this.emit('lesson_complete', undefined);

        // Show completion modal or redirect
        alert('Congratulations! You have completed this C.A.R.E. lesson!');
    }

    private updatePhaseIndicators(activePhaseName: string): void {
        const indicators = document.querySelectorAll<HTMLElement>('.care-phase-indicator');
        indicators.forEach(indicator => {
            const phase = indicator.dataset.phase;
            indicator.classList.remove('active', 'completed');

            if (phase === activePhaseName) {
                indicator.classList.add('active');
            } else if (this.phases.indexOf(phase || '') < this.phases.indexOf(activePhaseName)) {
                indicator.classList.add('completed');
            }
        });
    }

    private updateProgressBar(): void {
        const progressBar = document.getElementById('progressBar') as HTMLElement;
        if (progressBar) {
            const progressPercentage = (this.phaseProgress / this.totalPhases) * 100;
            progressBar.style.width = `${progressPercentage}%`;
        }
    }

    // AI Tutor methods
    private openAITutor(): void {
        const tutorModal = document.getElementById('aiTutorModal');
        if (tutorModal) {
            tutorModal.classList.remove('hidden');
        }
    }

    private closeAITutor(): void {
        const tutorModal = document.getElementById('aiTutorModal');
        if (tutorModal) {
            tutorModal.classList.add('hidden');
        }
    }

    private sendTutorMessage(): void {
        const input = document.getElementById('tutorInput') as HTMLInputElement;
        if (!input) return;

        const message = input.value.trim();
        if (!message) return;

        // Add user message to chat
        const chatArea = document.getElementById('tutorChatArea');
        if (chatArea) {
            const userMessage = document.createElement('div');
            userMessage.className = 'user-message bg-blue-600 text-white p-4 rounded-lg mb-4 ml-8';
            userMessage.innerHTML = `<p>${message}</p>`;
            chatArea.appendChild(userMessage);

            // Clear input
            input.value = '';

            // Simulate AI response
            setTimeout(() => {
                const aiMessage = document.createElement('div');
                aiMessage.className = 'tutor-message bg-blue-50 p-4 rounded-lg mb-4';
                aiMessage.innerHTML = `<p>That's a great question! ${this.generateTutorResponse(message)}</p>`;
                chatArea.appendChild(aiMessage);

                // Scroll to bottom
                chatArea.scrollTop = chatArea.scrollHeight;
            }, 1000);
        }
    }

    private generateTutorResponse(_userMessage: string): string {
        // Simple response generation - can be enhanced with actual AI
        const responses = [
            "Let me explain that concept in more detail...",
            "That's related to what we learned in the previous phase.",
            "Here's a helpful tip for remembering that phrase...",
            "In Spanish culture, this is particularly important because...",
            "Let me give you another example to clarify..."
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    // Event system
    public on<T>(event: string, callback: EventCallback<T>): void {
        if (!this.eventCallbacks.has(event)) {
            this.eventCallbacks.set(event, []);
        }
        this.eventCallbacks.get(event)!.push(callback as EventCallback);
    }

    public off<T>(event: string, callback: EventCallback<T>): void {
        const callbacks = this.eventCallbacks.get(event);
        if (callbacks) {
            const index = callbacks.indexOf(callback as EventCallback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    public emit<T>(event: string, data: T): void {
        const callbacks = this.eventCallbacks.get(event);
        if (callbacks) {
            callbacks.forEach(callback => callback(data));
        }
    }

    // Utility methods
    private getCSRFToken(): string {
        const token = document.querySelector<HTMLInputElement>('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    private log(...args: any[]): void {
        if (this.debugMode) {
            console.log('[CARE]', ...args);
        }
    }

    // Public API
    public getCurrentPhase(): string {
        return this.currentPhase;
    }

    public getProgress(): number {
        return this.phaseProgress;
    }

    public getTotalPhases(): number {
        return this.totalPhases;
    }
}

// Global initialization and export
declare global {
    interface Window {
        careManager?: CARELessonManager;
        CARELessonManager: typeof CARELessonManager;
    }
}

// Make CARELessonManager globally available
window.CARELessonManager = CARELessonManager;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
    console.log('🚀 Initializing C.A.R.E. Lesson Manager (TypeScript)...');

    // Check if lesson ID is available (from Django context)
    const lessonElement = document.querySelector<HTMLElement>('[data-lesson-id]');
    const lessonId = lessonElement?.dataset.lessonId || '1';

    window.careManager = new CARELessonManager({
        lessonId,
        debugMode: true, // Enable debug mode for development
        onPhaseChange: (phase) => console.log(`📍 Phase changed to: ${phase}`),
        onLessonComplete: () => console.log('🎯 Lesson completed!')
    });
});

export default CARELessonManager;
