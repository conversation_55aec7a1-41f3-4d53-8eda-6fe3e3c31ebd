# TalonTalk AI Services

AI-powered language learning features for TalonTalk, including LLM-based flashcard generation, answer grading, and intelligent lesson planning.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# From the ai_services directory
pip install -r requirements.txt
```

### 2. Configure API Keys
```bash
# Copy the environment template
cp .env.example .env

# Edit .env and add your API keys (choose one provider)
# Recommended: OPENROUTER_API_KEY for free access to multiple models
```

### 3. Start the Service
```bash
# Using the startup script (recommended)
python start.py

# Or directly with uvicorn
uvicorn ai_services.api:app --host 127.0.0.1 --port 8001 --reload
```

### 4. Test the Service
Visit http://127.0.0.1:8001/docs for interactive API documentation.

## 🤖 LLM Provider Comparison

### Recommended for Language Learning:

1. **OpenRouter** (FREE tier available)
   - Model: `mistralai/mistral-7b-instruct:free`
   - ✅ Free tier with good quality
   - ✅ Excellent multilingual support
   - ✅ Access to many models via one API
   - 💰 $0.00/1K tokens (free tier)

2. **DeepSeek** (Best value)
   - Model: `deepseek-chat`
   - ✅ Excellent reasoning capabilities
   - ✅ Very cost-effective
   - ✅ Good multilingual support
   - 💰 $0.00014/1K tokens

3. **OpenAI** (Most reliable)
   - Model: `gpt-4o-mini`
   - ✅ Proven reliability
   - ✅ Good documentation
   - ✅ Fast response times
   - 💰 $0.00015/1K tokens

## 📚 API Endpoints

### Generate Flashcard
```http
POST /generate_flashcard
Content-Type: application/json

{
  "target_language": "spanish",
  "difficulty": "beginner",
  "exercise_type": "translation",
  "vocabulary": ["hello", "goodbye"],
  "grammar_topic": "present_tense"
}
```

### Grade Answer
```http
POST /grade_answer
Content-Type: application/json

{
  "question": "How do you say 'hello' in Spanish?",
  "correct_answer": "hola",
  "user_answer": "ola",
  "language": "spanish"
}
```

### Health Check
```http
GET /health
```

## 🔧 Configuration Options

### Environment Variables
```bash
# LLM Provider (automatically selected based on available keys)
OPENROUTER_API_KEY=your_key_here
DEEPSEEK_API_KEY=your_key_here  
OPENAI_API_KEY=your_key_here
ANTHROPIC_API_KEY=your_key_here

# Service Configuration
AI_SERVICE_HOST=127.0.0.1
AI_SERVICE_PORT=8001
AI_SERVICE_RELOAD=true
```

## 🎯 Integration with Django

### 1. Add to Django Settings
```python
# settings.py
AI_SERVICE_BASE_URL = "http://127.0.0.1:8001"
```

### 2. Create Django Service Client
```python
import requests
from django.conf import settings

def generate_flashcard(target_language, difficulty, exercise_type):
    response = requests.post(
        f"{settings.AI_SERVICE_BASE_URL}/generate_flashcard",
        json={
            "target_language": target_language,
            "difficulty": difficulty,
            "exercise_type": exercise_type
        }
    )
    return response.json()
```

## 🔮 Future Features

- [ ] **FastWhisper Integration** - Local speech recognition
- [ ] **Deepgram Integration** - Cloud speech-to-text
- [ ] **Conversation Practice** - AI-powered chat practice
- [ ] **Adaptive Learning** - Personalized lesson difficulty
- [ ] **Content Training** - Fine-tune models on language learning data
- [ ] **Multi-modal Learning** - Image-based vocabulary exercises

## 🏗️ Architecture

```
Django App (Port 8000)
    ↓ HTTP API calls
AI Services (Port 8001)
    ↓ API calls
LLM Providers (OpenRouter/DeepSeek/OpenAI)
```

## 🐛 Troubleshooting

### Service won't start
- Check that all dependencies are installed: `pip install -r requirements.txt`
- Verify port 8001 is available
- Check logs for detailed error messages

### No flashcards generated
- Verify at least one API key is configured in `.env`
- Check API key validity and quotas
- Review service logs for API errors

### Poor quality responses
- Try a different model/provider
- Adjust temperature settings in the code
- Ensure appropriate difficulty level and context

## 📈 Monitoring

The service provides detailed logging and health endpoints for monitoring:
- Health: `GET /health`
- Metrics: `GET /metrics` (future)
- Logs: Check console output or configure file logging
