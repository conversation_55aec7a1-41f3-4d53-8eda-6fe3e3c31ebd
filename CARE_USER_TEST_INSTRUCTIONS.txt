C.A.R.E. Framework User Test Guide

OPEN THE LESSON:
Visit: http://127.0.0.1:8000/care/lesson/

VERIFY INITIAL STATE:
- See "Restaurant Ordering in Spanish" title
- See four phase indicators: Contextualize, Acquire, Reinforce, Extend
- See progress bar at top

TEST CONTEXTUALIZE PHASE:
- Should show real restaurant scenario in Barcelona
- Should show Spanish dining culture facts  
- Should show key phrases with pronunciation

TEST ACQUIRE PHASE:
- Should show vocabulary (restaurante, menu, mesero, etc.)
- Should show grammar patterns (Puedo + infinitive?)
- Should show examples and translations

TEST REINFORCE PHASE:
- Should show interactive practice questions
- Should show multiple choice questions
- Should show translation exercises
- Should show pronunciation practice

TEST EXTEND PHASE:
- Should show real-world role-play scenarios
- Should show expansion topics (food types, dietary restrictions)
- Should show homework assignment

TEST NAVIGATION:
- Click between phases using top indicators
- Use "Next" buttons to progress
- Verify smooth transitions and loading

EXPECTED RESULT: Seamless, engaging lesson experience with rich, contextual content!
