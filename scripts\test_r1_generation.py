#!/usr/bin/env python
"""
Test R1 Generation
Quick test to verify DeepSeek R1 flashcard generation works
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talontalk.settings')
django.setup()

from lessons.r1_flashcard_generator import R1FlashcardGenerator
from lessons.models import ContentItem

def main():
    print("R1 Flashcard Generation Test")
    print("=" * 40)
    
    # Initialize generator
    generator = R1FlashcardGenerator()
    
    if not generator.client:
        print("ERROR: DeepSeek R1 not available")
        return
    
    print("DeepSeek R1 is available!")
    print("Generating test flashcards...")
    
    # Test different types
    test_cases = [
        {
            "language": "spanish",
            "difficulty": 2,
            "template_type": "vocabulary_contextual",
            "topic_focus": "family"
        },
        {
            "language": "spanish", 
            "difficulty": 2,
            "template_type": "grammar_situational",
            "topic_focus": "present_tense"
        }
    ]
    
    total_generated = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['template_type']} - {test_case['topic_focus']}")
        
        try:
            result = generator.generate_flashcard_batch(
                language=test_case["language"],
                difficulty=test_case["difficulty"],
                template_type=test_case["template_type"],
                batch_size=2,  # Just 2 for testing
                topic_focus=test_case["topic_focus"]
            )
            
            if result["success"]:
                generated = result["generated_count"]
                total_generated += generated
                print(f"  SUCCESS: Generated {generated}/2 flashcards")
                print(f"  Success rate: {result['success_rate']:.1f}%")
                
                # Show sample content
                if result["flashcards"]:
                    sample = result["flashcards"][0]
                    print(f"  Sample question: {sample.question_text[:60]}...")
                    print(f"  Sample answer: {sample.answer_text[:40]}...")
            else:
                print(f"  FAILED: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"  ERROR: {e}")
    
    # Final summary
    print(f"\nTest Results:")
    print(f"  Total generated: {total_generated}")
    print(f"  Database count: {ContentItem.objects.count()}")
    
    if total_generated > 0:
        print("SUCCESS: R1 generation is working!")
        
        # Show quality sample
        sample_item = ContentItem.objects.first()
        if sample_item:
            print(f"\nSample Generated Content:")
            print(f"  Question: {sample_item.question_text}")
            print(f"  Answer: {sample_item.answer_text}")
            print(f"  Explanation: {sample_item.explanation_text[:100]}...")
    else:
        print("FAILED: No content generated")

if __name__ == "__main__":
    main()
