# TalonTalk Technical Architecture & Code Structure

## 🏗️ **System Architecture Overview**

```
┌─────────────────────────────────────────────────────┐
│                    Frontend Layer                   │
├─────────────────────────────────────────────────────┤
│  TailwindCSS + Vanilla JS + HTMX + Responsive UI   │
│  Templates: landing.html, dashboard.html, etc.     │
└─────────────────────────────────────────────────────┘
                            │
                    HTTP Requests
                            ▼
┌─────────────────────────────────────────────────────┐
│                 Django Web Layer                    │
├─────────────────────────────────────────────────────┤
│  URL Routing → Views → Templates → JSON APIs        │
│  Authentication, CSRF, Session Management          │
└─────────────────────────────────────────────────────┘
                            │
                    Business Logic
                            ▼
┌─────────────────────────────────────────────────────┐
│                  Application Layer                  │
├─────────────────────────────────────────────────────┤
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌─────────┐│
│  │   CARE   │ │Gamifica- │ │ Lessons  │ │Profiles ││
│  │Framework │ │   tion   │ │   Mgmt   │ │  Users  ││
│  └──────────┘ └──────────┘ └──────────┘ └─────────┘│
└─────────────────────────────────────────────────────┘
                            │
                    Data Persistence
                            ▼
┌─────────────────────────────────────────────────────┐
│                   Data Layer                        │
├─────────────────────────────────────────────────────┤
│  SQLite (Dev) / PostgreSQL (Prod) + Django ORM     │
│  Models, Migrations, Relationships                  │
└─────────────────────────────────────────────────────┘
                            │
                    AI Integration
                            ▼
┌─────────────────────────────────────────────────────┐
│                   AI Services                       │
├─────────────────────────────────────────────────────┤
│  Local Ollama (Mistral 7B) + OpenRouter Fallback   │
│  Content Generation, Validation, Grading           │
└─────────────────────────────────────────────────────┘
```

## 📂 **Detailed Code Structure**

### **Main Project (talontalk/)**

#### **settings.py** - Core Configuration
```python
# Key Settings
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth', 
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',        # API framework
    'django_rq',            # Background jobs
    'profiles',             # User profiles
    'lessons',              # Content management
    'gamification',         # XP/achievements
    'users',                # User management
    'care',                 # C.A.R.E. framework
]

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Static Files
STATIC_URL = '/static/'
STATICFILES_DIRS = [BASE_DIR / 'talontalk' / 'static']
STATIC_ROOT = BASE_DIR / 'staticfiles'

# AI Configuration
AI_SERVICE_URL = "http://127.0.0.1:8001"
LOCAL_AI_AVAILABLE = True
OLLAMA_MODEL = "mistral:7b"
```

#### **urls.py** - URL Configuration
```python
from django.urls import path, include
from . import views

urlpatterns = [
    # Core pages
    path("", views.landing_view, name="landing"),
    path("dashboard/", views.dashboard_view, name="dashboard"),
    path("practice/flashcards/", views.flashcard_practice_view, name="flashcard_practice"),
    
    # User flows
    path("onboarding/", views.onboarding_view, name="onboarding"),
    path("onboarding/complete/", views.onboarding_complete_view, name="onboarding_complete"),
    
    # Authentication
    path("accounts/", include("allauth.urls")),
    
    # App integrations
    path("api/", include("gamification.urls")),  # API endpoints
    path("care/", include("care.urls")),         # C.A.R.E. framework
    path("admin/", admin.site.urls),
]
```

#### **views.py** - Core Views
```python
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from profiles.models import Profile
from gamification.models import Level, Achievement, Streak
from lessons.models import Lesson

def landing_view(request):
    """Marketing landing page with conversion optimization"""
    return render(request, "landing.html")

@login_required
def dashboard_view(request):
    """Central user dashboard with progress overview"""
    try:
        profile = Profile.objects.get(user=request.user)
        level = Level.objects.get_or_create(user=request.user)[0]
        streak = Streak.objects.get_or_create(user=request.user)[0]
        achievements = Achievement.objects.filter(user=request.user)
        lessons = Lesson.objects.filter(language=profile.target_language)
        
        context = {
            'profile': profile,
            'level': level,
            'streak': streak,
            'achievements': achievements,
            'lessons': lessons,
            'completed_lessons': lessons.filter(userlessonprogress__user=request.user, userlessonprogress__completed=True).count(),
            'words_learned': profile.lessons_completed * 10,  # Estimate
        }
        return render(request, "dashboard.html", context)
    except Profile.DoesNotExist:
        return redirect("onboarding")

@login_required
def flashcard_practice_view(request):
    """Dedicated immersive flashcard practice interface"""
    try:
        profile = Profile.objects.get(user=request.user)
        context = {'profile': profile, 'user': request.user}
        return render(request, "flashcard_practice.html", context)
    except Profile.DoesNotExist:
        return redirect("onboarding")

def onboarding_view(request):
    """5-step user onboarding flow"""
    return render(request, "onboarding.html")

@login_required
def onboarding_complete_view(request):
    """Process onboarding completion and create profile"""
    if request.method == 'POST':
        data = json.loads(request.body)
        profile, created = Profile.objects.get_or_create(
            user=request.user,
            defaults={
                'target_language': data.get('target_language'),
                'native_language': data.get('native_language'), 
                'skill_level': data.get('skill_level'),
                'main_goal': data.get('main_goal'),
            }
        )
        return JsonResponse({'success': True, 'redirect_url': '/dashboard/'})
```

### **AI Services (ai_services/)**

#### **llm_config.py** - AI Configuration
```python
import os
from enum import Enum
from dataclasses import dataclass

class LLMProvider(Enum):
    OLLAMA = "ollama"
    OPENROUTER = "openrouter"

class DifficultyLevel(Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

class ExerciseType(Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    FILL_BLANK = "fill_blank"
    TRANSLATION = "translation"

@dataclass
class FlashcardRequest:
    language: str
    target_language: str
    difficulty: DifficultyLevel
    exercise_type: ExerciseType
    grammar_topic: str = None
    context: str = None

class LLMConfig:
    # Model Configuration
    DEFAULT_PROVIDER = LLMProvider.OLLAMA
    DEFAULT_MODEL = "mistral:7b"
    FALLBACK_MODELS = ["llama2:7b", "codellama:7b"]
    
    # Service URLs
    OLLAMA_BASE_URL = "http://localhost:11434"
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # API Configuration
    TIMEOUT_SECONDS = 30
    MAX_RETRIES = 3
    
    # Prompt Templates
    FLASHCARD_SYSTEM_PROMPT = """You are an expert language teacher creating educational flashcards. 
    Generate high-quality, pedagogically sound questions that help students learn effectively."""
    
    FLASHCARD_PROMPT_TEMPLATE = """
    Create a {difficulty} level {exercise_type} flashcard for learning {target_language}.
    
    Requirements:
    - Question should test {topic} knowledge
    - Include 4 multiple choice options (if applicable)
    - Provide helpful hint
    - Give clear explanation
    - Add example sentence in target language
    - Include pronunciation guide
    
    Format as JSON with fields: question, options, correct_answer, hint, explanation, example_sentence, pronunciation_guide
    """
```

#### **llm_flashcards.py** - Content Generation Service
```python
import requests
import json
import logging
from typing import List, Optional
from .llm_config import LLMConfig, FlashcardRequest, LLMProvider

logger = logging.getLogger(__name__)

class LLMFlashcardService:
    def __init__(self):
        self.config = LLMConfig()
        self.current_provider = self.config.DEFAULT_PROVIDER
        
    def generate_lesson(self, request: FlashcardRequest, lesson_length: int = 5) -> List[dict]:
        """Generate a complete lesson with multiple flashcards"""
        flashcards = []
        
        for i in range(lesson_length):
            try:
                flashcard = self.generate_single_flashcard(request)
                if flashcard and self.validate_flashcard_quality(flashcard):
                    flashcards.append(flashcard)
                else:
                    logger.warning(f"Generated flashcard {i+1} failed quality check")
            except Exception as e:
                logger.error(f"Error generating flashcard {i+1}: {e}")
                
        return flashcards
    
    def generate_single_flashcard(self, request: FlashcardRequest) -> Optional[dict]:
        """Generate a single flashcard using AI"""
        
        if self.current_provider == LLMProvider.OLLAMA:
            return self._generate_with_ollama(request)
        elif self.current_provider == LLMProvider.OPENROUTER:
            return self._generate_with_openrouter(request)
        else:
            raise ValueError(f"Unsupported provider: {self.current_provider}")
    
    def _generate_with_ollama(self, request: FlashcardRequest) -> Optional[dict]:
        """Generate flashcard using local Ollama"""
        url = f"{self.config.OLLAMA_BASE_URL}/api/generate"
        
        prompt = self.config.FLASHCARD_PROMPT_TEMPLATE.format(
            difficulty=request.difficulty.value,
            exercise_type=request.exercise_type.value,
            target_language=request.target_language,
            topic=request.grammar_topic or "general vocabulary"
        )
        
        payload = {
            "model": self.config.DEFAULT_MODEL,
            "prompt": prompt,
            "system": self.config.FLASHCARD_SYSTEM_PROMPT,
            "format": "json",
            "stream": False
        }
        
        try:
            response = requests.post(
                url, 
                json=payload, 
                timeout=self.config.TIMEOUT_SECONDS
            )
            response.raise_for_status()
            
            result = response.json()
            flashcard_data = json.loads(result.get('response', '{}'))
            
            return self._format_flashcard_response(flashcard_data)
            
        except Exception as e:
            logger.error(f"Ollama generation failed: {e}")
            return None
    
    def _format_flashcard_response(self, data: dict) -> dict:
        """Standardize flashcard format"""
        return {
            'id': str(uuid.uuid4()),
            'question': data.get('question', ''),
            'question_type': 'multiple_choice',
            'options': data.get('options', []),
            'correct_answer': data.get('correct_answer', ''),
            'hint': data.get('hint', ''),
            'explanation': data.get('explanation', ''),
            'example_sentence': data.get('example_sentence', ''),
            'pronunciation_guide': data.get('pronunciation_guide', ''),
            'difficulty': 'beginner',
            'ai_generated': True
        }
    
    def validate_flashcard_quality(self, flashcard: dict) -> bool:
        """Validate generated flashcard meets quality standards"""
        required_fields = ['question', 'correct_answer', 'explanation']
        
        # Check required fields exist and are not empty
        for field in required_fields:
            if not flashcard.get(field, '').strip():
                return False
        
        # For multiple choice, validate options
        if flashcard.get('question_type') == 'multiple_choice':
            options = flashcard.get('options', [])
            correct_answer = flashcard.get('correct_answer', '').strip().lower()
            
            if len(options) < 2:
                return False
                
            # Ensure correct answer is in options
            options_lower = [opt.strip().lower() for opt in options]
            if correct_answer not in options_lower:
                return False
        
        return True
```

### **C.A.R.E. Framework (care/)**

#### **models.py** - Learning Progress Models
```python
from django.db import models
from django.contrib.auth.models import User
from lessons.models import Lesson

class CareSession(models.Model):
    """Track user progress through C.A.R.E. phases"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, null=True, blank=True)
    current_phase = models.CharField(max_length=20, default='contextualize')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['user', 'lesson']

class CarePhaseProgress(models.Model):
    """Track completion of individual C.A.R.E. phases"""
    session = models.ForeignKey(CareSession, on_delete=models.CASCADE)
    phase = models.CharField(max_length=20)
    completed = models.BooleanField(default=False)
    score = models.IntegerField(null=True, blank=True)
    time_spent = models.DurationField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ['session', 'phase']
```

#### **views.py** - C.A.R.E. Implementation
```python
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from .models import CareSession, CarePhaseProgress
from lessons.models import Lesson

@login_required
def care_lesson_view(request, lesson_id=None):
    """Main C.A.R.E. lesson interface"""
    lesson = None
    if lesson_id:
        lesson = get_object_or_404(Lesson, id=lesson_id)
    
    # Get or create session
    session, created = CareSession.objects.get_or_create(
        user=request.user,
        lesson=lesson,
        defaults={'current_phase': 'contextualize'}
    )
    
    context = {
        'lesson': lesson,
        'session': session,
        'current_phase': session.current_phase,
    }
    
    return render(request, 'care/lesson.html', context)

@require_http_methods(["GET"])
def care_phase_data(request, phase):
    """API endpoint to get dynamic data for each C.A.R.E. phase"""
    
    # Phase-specific content generation
    if phase == 'contextualize':
        data = {
            'scenario': 'Ordering coffee in a Madrid café',
            'cultural_notes': [
                'Spanish café culture emphasizes social interaction',
                'Common to stand at the bar for quick service'
            ],
            'setting': 'Busy morning at Café Central'
        }
    elif phase == 'acquire':
        data = {
            'vocabulary': [
                {'word': 'café', 'translation': 'coffee', 'pronunciation': 'ka-FE'},
                {'word': 'con leche', 'translation': 'with milk', 'pronunciation': 'kon LE-che'},
                {'word': 'cortado', 'translation': 'espresso with milk', 'pronunciation': 'kor-TA-do'}
            ]
        }
    elif phase == 'reinforce':
        data = {
            'exercises': [
                {
                    'type': 'flashcard',
                    'question': '¿Cómo se dice "coffee" en español?',
                    'options': ['café', 'leche', 'agua', 'té'],
                    'correct': 'café'
                }
            ]
        }
    elif phase == 'extend':
        data = {
            'conversation_starters': [
                'Buenos días, quisiera un café, por favor',
                '¿Tienen café con leche?'
            ],
            'scenarios': ['Ordering for friends', 'Asking about prices']
        }
    
    return JsonResponse({'success': True, 'data': data})

@require_http_methods(["POST"])
def care_submit_answer(request, phase):
    """Handle answer submissions for each C.A.R.E. phase"""
    import json
    
    data = json.loads(request.body)
    answer = data.get('answer')
    question_id = data.get('question_id')
    
    # Process answer and provide feedback
    is_correct = validate_answer(phase, question_id, answer)
    feedback = generate_phase_feedback(phase, is_correct, answer)
    
    # Update progress
    session = CareSession.objects.filter(user=request.user).first()
    if session:
        phase_progress, created = CarePhaseProgress.objects.get_or_create(
            session=session,
            phase=phase,
            defaults={'completed': is_correct, 'score': 100 if is_correct else 0}
        )
    
    return JsonResponse({
        'success': True,
        'correct': is_correct,
        'feedback': feedback,
        'next_phase': get_next_phase(phase) if is_correct else None
    })
```

### **Gamification (gamification/)**

#### **models.py** - Achievement System
```python
from django.db import models
from django.contrib.auth.models import User

class Badge(models.Model):
    """Achievement badges users can earn"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    icon = models.CharField(max_length=50)  # Emoji or icon class
    category = models.CharField(max_length=50)
    xp_reward = models.IntegerField(default=0)
    
    def __str__(self):
        return self.name

class Level(models.Model):
    """User level and XP tracking"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    level = models.IntegerField(default=1)
    xp = models.IntegerField(default=0)
    
    @property
    def xp_for_next_level(self):
        return (self.level * 100) + 500  # Progressive XP requirements
    
    @property
    def progress_percentage(self):
        return min((self.xp / self.xp_for_next_level) * 100, 100)

class Achievement(models.Model):
    """User achievements"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    badge = models.ForeignKey(Badge, on_delete=models.CASCADE)
    earned_date = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'badge']

class Streak(models.Model):
    """Daily practice streak tracking"""
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    current_streak = models.IntegerField(default=0)
    max_streak = models.IntegerField(default=0)
    last_activity_date = models.DateField(null=True, blank=True)
    
    def update_streak(self):
        """Update streak based on current activity"""
        from django.utils import timezone
        today = timezone.now().date()
        
        if self.last_activity_date == today:
            return  # Already counted today
        elif self.last_activity_date == today - timezone.timedelta(days=1):
            # Consecutive day
            self.current_streak += 1
        else:
            # Streak broken
            self.current_streak = 1
            
        self.max_streak = max(self.max_streak, self.current_streak)
        self.last_activity_date = today
        self.save()
```

#### **views.py** - API Endpoints & Flashcard Generation
```python
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.http import JsonResponse
from django.core.cache import cache
import uuid
import logging

logger = logging.getLogger(__name__)

@api_view(["GET"])
@permission_classes([AllowAny])
def generate_flashcard(request):
    """Generate AI-powered flashcards with intelligent caching"""
    
    # Extract parameters
    language = request.GET.get("language", "spanish")
    difficulty = request.GET.get("difficulty", "beginner")
    exercise_type = request.GET.get("type", "multiple_choice")
    topic = request.GET.get("topic", None)
    lesson_length = int(request.GET.get("lesson_length", 5))
    
    # Smart caching for fast responses
    cache_key = f"flashcard_{language}_{difficulty}_{exercise_type}_v4"
    cached_flashcard = cache.get(cache_key)
    
    if cached_flashcard:
        logger.info(f"🚀 Serving cached flashcard for {cache_key}")
        return JsonResponse({
            "success": True, 
            "flashcard": cached_flashcard, 
            "cached": True
        })
    
    # Try AI generation first
    try:
        if AI_SERVICE_AVAILABLE:
            from ai_services.llm_flashcards import LLMFlashcardService
            from ai_services.llm_config import FlashcardRequest, DifficultyLevel, ExerciseType
            
            llm_service = LLMFlashcardService()
            flashcard_request = FlashcardRequest(
                language="english",
                target_language=language,
                difficulty=DifficultyLevel(difficulty.lower()),
                exercise_type=ExerciseType(exercise_type.lower()),
                grammar_topic=topic,
            )
            
            flashcards = llm_service.generate_lesson(flashcard_request, lesson_length)
            
            if flashcards:
                # Cache the generated flashcard
                cache.set(cache_key, flashcards[0], timeout=600)  # 10 minutes
                return JsonResponse({
                    "success": True,
                    "flashcard": flashcards[0],
                    "flashcards": flashcards,
                    "cached": False,
                })
    except Exception as ai_error:
        logger.warning(f"AI service failed, falling back to demo: {ai_error}")
    
    # Fallback to demo content
    demo_flashcards = [
        {
            "id": str(uuid.uuid4()),
            "question": '¿Cómo se dice "Hello" en español?',
            "question_type": "multiple_choice",
            "options": ["Hola", "Adiós", "Gracias", "Por favor"],
            "correct_answer": "Hola",
            "hint": "It's a common greeting.",
            "explanation": '"Hola" means "Hello" in Spanish.',
            "example_sentence": "Hola, ¿cómo estás?",
            "pronunciation_guide": "OH-lah",
            "difficulty": "beginner",
            "language": "spanish",
            "ai_generated": False,
        },
        # ... more demo flashcards
    ]
    
    # Randomly select and cache
    import random
    selected_demo = random.choice(demo_flashcards)
    cache.set(cache_key, selected_demo, timeout=300)  # 5 minutes
    
    return JsonResponse({
        "success": True,
        "flashcard": selected_demo,
        "cached": False,
        "demo": True,
    })

@api_view(["POST"])
def submit_flashcard_answer(request):
    """Process flashcard answers and update user progress"""
    import json
    
    data = json.loads(request.body)
    is_correct = data.get('correct', False)
    time_taken = data.get('time_taken', 0)
    
    if request.user.is_authenticated:
        # Update XP and level
        level, created = Level.objects.get_or_create(user=request.user)
        xp_earned = 10 if is_correct else 5  # Base XP
        level.xp += xp_earned
        
        # Level up check
        while level.xp >= level.xp_for_next_level:
            level.xp -= level.xp_for_next_level
            level.level += 1
            
        level.save()
        
        # Update streak
        streak, created = Streak.objects.get_or_create(user=request.user)
        if is_correct:
            streak.update_streak()
        
        # Check for new achievements
        check_achievements(request.user)
    
    return JsonResponse({
        'success': True,
        'xp_earned': xp_earned if request.user.is_authenticated else 0,
        'new_level': level.level if request.user.is_authenticated else None
    })

def check_achievements(user):
    """Check and award new achievements"""
    level = Level.objects.get(user=user)
    streak = Streak.objects.get(user=user)
    
    # Level-based achievements
    if level.level >= 5 and not Achievement.objects.filter(user=user, badge__name="Level 5 Master").exists():
        badge = Badge.objects.get(name="Level 5 Master")
        Achievement.objects.create(user=user, badge=badge)
    
    # Streak-based achievements
    if streak.current_streak >= 7 and not Achievement.objects.filter(user=user, badge__name="Week Warrior").exists():
        badge = Badge.objects.get(name="Week Warrior")
        Achievement.objects.create(user=user, badge=badge)
```

## 🎯 **Key Integration Points**

### **Data Flow Architecture**
```
User Action (Frontend)
    ↓
Django View (Backend)
    ↓
Business Logic (Models/Services)
    ↓
Database Persistence (SQLite/PostgreSQL)
    ↓
External AI Service (Ollama/OpenRouter)
    ↓
Cached Response (Redis/Memory)
    ↓
JSON Response (API)
    ↓
Frontend Update (JavaScript)
```

### **Authentication Flow**
```
Landing Page → Registration/Login → Onboarding → Profile Creation → Dashboard Access → Learning Activities
```

### **Learning Progress Tracking**
```
User Answer → Gamification Update → Profile Progress → Achievement Check → Streak Update → Level Progression
```

This technical documentation provides the exact code structure and architecture needed for sharing with other AI systems or developers for debugging and enhancement recommendations.
