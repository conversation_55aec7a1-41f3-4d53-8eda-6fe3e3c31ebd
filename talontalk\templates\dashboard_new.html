{% extends 'base.html' %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
  <!-- Enhanced TalonTalk Dashboard Header -->
  <div class="bg-gradient-to-r from-talon-blue via-talon-blue to-talon-blue-dark shadow-xl border-b-4 border-falcon-yellow">
    <div class="w-full px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <!-- TalonTalk Brand Identity -->
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-falcon-yellow to-yellow-500 rounded-xl flex items-center justify-center shadow-lg border-2 border-yellow-300">
              <span class="text-talon-blue font-bold text-2xl">🦅</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-white">TalonTalk</h1>
              <p class="text-blue-100 text-sm">{{ profile.target_language|title }} Learning Dashboard</p>
            </div>
          </div>
          <div class="hidden lg:block w-px h-12 bg-blue-400 opacity-30"></div>
          <div class="hidden lg:block">
            <h2 class="text-xl font-semibold text-white">Welcome back, {{ user.first_name|default:user.username }}!</h2>
            <p class="text-blue-100">Ready to soar higher in your language journey? 🚀</p>
          </div>
        </div>
        
        <!-- Enhanced Gamification Stats -->
        <div class="flex items-center space-x-4">
          <!-- Level Badge -->
          <div class="bg-gradient-to-br from-falcon-yellow via-yellow-400 to-amber-500 text-talon-blue px-6 py-3 rounded-xl shadow-lg border-2 border-yellow-300 transform hover:scale-105 transition-all duration-200">
            <div class="text-center">
              <div class="flex items-center space-x-2">
                <span class="text-xl">🏆</span>
                <div>
                  <div class="text-lg font-bold">Level {{ level.level }}</div>
                  <div class="text-xs font-medium opacity-80">{{ level.xp }}/{{ xp_for_next_level }} XP</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Streak Counter -->
          <div class="bg-gradient-to-br from-orange-400 via-red-400 to-pink-500 text-white px-6 py-3 rounded-xl shadow-lg border-2 border-orange-300 transform hover:scale-105 transition-all duration-200">
            <div class="flex items-center space-x-2">
              <span class="text-2xl animate-pulse">🔥</span>
              <div>
                <div class="text-lg font-bold">{{ streak.current_streak }} Days</div>
                <div class="text-xs opacity-90">Best: {{ streak.longest_streak }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid lg:grid-cols-3 gap-8">
      
      <!-- Main Content Area -->
      <div class="lg:col-span-2 space-y-6">
        
        <!-- Enhanced Progress Overview -->
        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-talon-blue to-blue-600 rounded-lg flex items-center justify-center">
              <span class="text-white text-xl">📊</span>
            </div>
            <h2 class="text-xl font-bold text-talon-blue">Your Learning Progress</h2>
          </div>
          
          <div class="grid md:grid-cols-3 gap-6 mb-6">
            <div class="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200">
              <div class="text-3xl font-bold text-talon-blue mb-1">{{ completed_lessons }}</div>
              <div class="text-sm font-medium text-blue-700">Lessons Completed</div>
              <div class="text-xs text-blue-600 mt-1">🎯 Keep going!</div>
            </div>
            <div class="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200">
              <div class="text-3xl font-bold text-green-700 mb-1">{{ profile.xp }}</div>
              <div class="text-sm font-medium text-green-700">Total XP Earned</div>
              <div class="text-xs text-green-600 mt-1">⚡ Amazing!</div>
            </div>
            <div class="text-center p-6 bg-gradient-to-br from-falcon-yellow to-yellow-100 rounded-xl border border-yellow-200">
              <div class="text-3xl font-bold text-amber-700 mb-1">{{ achievements.count }}</div>
              <div class="text-sm font-medium text-amber-700">Badges Earned</div>
              <div class="text-xs text-amber-600 mt-1">🏆 Excellent!</div>
            </div>
          </div>
          
          <!-- Enhanced Level Progress Bar -->
          <div class="bg-gray-50 rounded-xl p-4">
            <div class="flex justify-between items-center text-sm font-medium text-talon-blue mb-3">
              <span>Level {{ level.level }} Progress</span>
              <span>{{ level.xp }}/{{ xp_for_next_level }} XP</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-4 shadow-inner">
              <div class="bg-gradient-to-r from-talon-blue via-blue-500 to-falcon-yellow h-4 rounded-full transition-all duration-500 shadow-lg" style="width: {{ progress_percentage }}%"></div>
            </div>
            <div class="text-xs text-gray-600 mt-2 text-center">
              {{ xp_for_next_level|add:"-{{ level.xp }}" }} XP to Level {{ level.level|add:"1" }}
            </div>
          </div>
        </div>

        <!-- Enhanced AI Flashcard Practice -->
        <div class="bg-gradient-to-br from-talon-blue via-blue-600 to-purple-700 rounded-2xl shadow-xl p-6 text-white relative overflow-hidden">
          <div class="absolute top-0 right-0 w-32 h-32 bg-falcon-yellow opacity-10 rounded-full -mr-16 -mt-16"></div>
          <div class="relative">
            <div class="flex items-center justify-between">
              <div>
                <div class="flex items-center space-x-3 mb-3">
                  <span class="text-3xl">🧠</span>
                  <h2 class="text-2xl font-bold">AI Flashcard Practice</h2>
                </div>
                <p class="text-blue-100 text-lg mb-2">Master {{ profile.target_language|title }} vocabulary with AI-powered flashcards</p>
                <p class="text-blue-200 text-sm">Personalized questions • Instant feedback • Smart progression</p>
              </div>
              <div class="flex flex-col space-y-3">
                <button onclick="startFlashcardPractice()" class="bg-falcon-yellow hover:bg-yellow-400 text-talon-blue px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
                  Start Practice
                </button>
                <button onclick="resetTutorial()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 backdrop-blur-sm">
                  🔄 Reset Tutorial
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Continue Learning -->
        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <span class="text-white text-xl">📚</span>
            </div>
            <h2 class="text-xl font-bold text-talon-blue">Continue Learning</h2>
          </div>
          
          <div class="space-y-4">
            {% for lesson in lessons %}
            <div class="border-2 border-gray-100 hover:border-talon-blue rounded-xl p-6 transition-all duration-200 hover:shadow-lg bg-gradient-to-r hover:from-blue-50 hover:to-purple-50">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-14 h-14 bg-gradient-to-br from-talon-blue to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-lg">{{ lesson.order }}</span>
                  </div>
                  <div>
                    <h3 class="font-bold text-talon-blue text-lg">{{ lesson.title }}</h3>
                    <p class="text-gray-600 mb-2">{{ lesson.description }}</p>
                    <div class="flex items-center space-x-4 text-sm">
                      <span class="bg-blue-100 text-talon-blue px-3 py-1 rounded-full font-medium">
                        📝 {{ lesson.vocabularies.count }} vocabulary words
                      </span>
                      {% if lesson.id in completed_lesson_ids %}
                        <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full font-medium">
                          ✅ Completed
                        </span>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="flex items-center space-x-3">
                  {% if lesson.id in completed_lesson_ids %}
                    <div class="text-green-600 font-bold text-lg">✓</div>
                  {% else %}
                    <a href="{% url 'lesson_detail' lesson.id %}" class="bg-gradient-to-r from-talon-blue to-blue-600 hover:from-blue-600 hover:to-purple-600 text-white px-6 py-3 rounded-xl font-bold shadow-lg transform hover:scale-105 transition-all duration-200">
                      Start Lesson →
                    </a>
                  {% endif %}
                </div>
              </div>
            </div>
            {% empty %}
            <div class="text-center py-12 bg-gray-50 rounded-xl">
              <span class="text-6xl mb-4 block">📚</span>
              <h3 class="text-xl font-bold text-talon-blue mb-2">Lessons Coming Soon!</h3>
              <p class="text-gray-600">We're preparing amazing content for you. Check back soon!</p>
            </div>
            {% endfor %}
          </div>
        </div>

      </div>

      <!-- Enhanced Sidebar -->
      <div class="space-y-6">
        
        <!-- Enhanced Daily Goal -->
        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-8 h-8 bg-gradient-to-br from-falcon-yellow to-amber-500 rounded-lg flex items-center justify-center">
              <span class="text-talon-blue text-lg">🎯</span>
            </div>
            <h3 class="text-lg font-bold text-talon-blue">Today's Goal</h3>
          </div>
          <div class="text-center">
            <div class="w-24 h-24 mx-auto bg-gradient-to-br from-falcon-yellow via-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-talon-blue text-2xl font-bold mb-4 shadow-lg border-4 border-yellow-200">
              15
            </div>
            <p class="font-medium text-talon-blue mb-1">Study for 15 minutes</p>
            <p class="text-sm text-gray-600 mb-4">You're doing great! Keep it up! 💪</p>
            <div class="bg-gray-100 rounded-full h-3 mb-2">
              <div class="bg-gradient-to-r from-falcon-yellow to-amber-500 h-3 rounded-full shadow-sm" style="width: 60%"></div>
            </div>
            <p class="text-xs font-medium text-green-600">9 minutes completed today</p>
          </div>
        </div>

        <!-- Enhanced Recent Achievements -->
        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-8 h-8 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-lg flex items-center justify-center">
              <span class="text-white text-lg">🏆</span>
            </div>
            <h3 class="text-lg font-bold text-talon-blue">Recent Achievements</h3>
          </div>
          {% if achievements %}
            <div class="space-y-4">
              {% for achievement in achievements %}
              <div class="flex items-center space-x-3 p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl border border-yellow-200">
                <div class="w-12 h-12 bg-gradient-to-br from-falcon-yellow to-amber-500 rounded-full flex items-center justify-center shadow-lg">
                  <span class="text-talon-blue text-lg">🏆</span>
                </div>
                <div>
                  <div class="font-bold text-talon-blue">{{ achievement.badge.name }}</div>
                  <div class="text-sm text-amber-700">{{ achievement.badge.description }}</div>
                </div>
              </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-6 bg-gray-50 rounded-xl">
              <span class="text-4xl mb-3 block">🎯</span>
              <p class="font-medium text-talon-blue mb-1">Start Learning!</p>
              <p class="text-sm text-gray-600">Complete lessons to earn your first badges!</p>
            </div>
          {% endif %}
        </div>

        <!-- Enhanced Learning Stats -->
        <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <span class="text-white text-lg">📈</span>
            </div>
            <h3 class="text-lg font-bold text-talon-blue">Learning Stats</h3>
          </div>
          <div class="space-y-4">
            <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <span class="text-talon-blue font-medium">📚 Words Learned</span>
              <span class="font-bold text-talon-blue text-lg">{{ words_learned }}</span>
            </div>
            <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <span class="text-green-700 font-medium">⏱️ Time Studied</span>
              <span class="font-bold text-green-700 text-lg">{{ minutes_studied }}min</span>
            </div>
            <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
              <span class="text-purple-700 font-medium">🎖️ Current Level</span>
              <span class="font-bold text-purple-700 text-lg">{{ level.level }}</span>
            </div>
            <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
              <span class="text-amber-700 font-medium">🌍 Native Language</span>
              <span class="font-bold text-amber-700">{{ profile.native_language }}</span>
            </div>
            <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
              <span class="text-red-700 font-medium">🎯 Learning</span>
              <span class="font-bold text-red-700">{{ profile.target_language }}</span>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- Enhanced Flashcard Practice Modal -->
<div id="flashcardModal" class="fixed inset-0 bg-black bg-opacity-60 hidden z-50 flex items-center justify-center backdrop-blur-sm">
  <div class="bg-white rounded-2xl shadow-2xl max-w-3xl w-full mx-4 transform transition-all border-4 border-talon-blue">
    
    <!-- Enhanced Modal Header -->
    <div class="flex items-center justify-between p-6 bg-gradient-to-r from-talon-blue to-blue-600 text-white rounded-t-2xl">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-falcon-yellow rounded-lg flex items-center justify-center">
          <span class="text-talon-blue text-xl">🧠</span>
        </div>
        <div>
          <h3 class="text-xl font-bold">Flashcard Practice</h3>
          <p class="text-blue-100 text-sm">{{ profile.target_language|title }} vocabulary mastery</p>
        </div>
      </div>
      <button onclick="closeFlashcardModal()" class="text-blue-200 hover:text-white p-2 hover:bg-white/20 rounded-lg transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Flashcard Content -->
    <div id="flashcardContent" class="p-6">
      
      <!-- Loading State -->
      <div id="flashcardLoading" class="text-center py-16">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-talon-blue border-t-falcon-yellow mx-auto mb-6"></div>
        <p class="text-talon-blue font-medium text-lg">Generating your personalized flashcard...</p>
        <p class="text-gray-600 text-sm mt-2">Our AI is creating the perfect question for you!</p>
      </div>

      <!-- Flashcard Display -->
      <div id="flashcardDisplay" class="hidden">
        <!-- Enhanced Progress Bar -->
        <div class="mb-8">
          <div class="flex justify-between items-center text-sm font-medium text-talon-blue mb-3">
            <span>Progress</span>
            <span id="flashcardProgress" class="bg-blue-100 px-3 py-1 rounded-full">1/5</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
            <div id="flashcardProgressBar" class="bg-gradient-to-r from-talon-blue to-falcon-yellow h-3 rounded-full transition-all duration-500 shadow-lg" style="width: 20%"></div>
          </div>
        </div>

        <!-- Enhanced Question -->
        <div class="bg-gradient-to-r from-blue-50 via-purple-50 to-blue-50 rounded-xl p-8 mb-8 border-2 border-blue-100">
          <div class="flex items-center space-x-3 mb-4">
            <span class="text-2xl">❓</span>
            <h4 class="text-lg font-bold text-talon-blue">Question</h4>
          </div>
          <p id="flashcardQuestion" class="text-2xl font-medium text-talon-blue mb-3"></p>
          <div id="flashcardHint" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-4 hidden">
            <div class="flex items-center space-x-2">
              <span class="text-xl">💡</span>
              <div>
                <div class="font-medium text-yellow-800">Hint:</div>
                <div class="text-yellow-700"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Answer Options (Multiple Choice) -->
        <div id="multipleChoiceOptions" class="space-y-3 mb-8 hidden">
          <div class="grid gap-3">
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option A
            </button>
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option B
            </button>
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option C
            </button>
            <button class="answer-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium">
              Option D
            </button>
          </div>
        </div>

        <!-- Enhanced Text Input (Translation) -->
        <div id="textInputArea" class="mb-8 hidden">
          <label class="block text-sm font-bold text-talon-blue mb-3">Your Answer:</label>
          <input id="userAnswer" type="text" class="w-full p-4 border-2 border-gray-200 rounded-xl focus:border-talon-blue focus:outline-none text-lg" placeholder="Type your answer here...">
        </div>

        <!-- Enhanced Action Buttons -->
        <div class="flex justify-between items-center">
          <button id="showHintBtn" onclick="showHint()" class="flex items-center space-x-2 text-falcon-yellow hover:text-amber-500 font-bold transition-colors">
            <span class="text-xl">💡</span>
            <span>Show Hint</span>
          </button>
          <div class="space-x-4">
            <button id="submitAnswerBtn" onclick="submitAnswer()" class="bg-gradient-to-r from-talon-blue to-blue-600 hover:from-blue-600 hover:to-purple-600 text-white px-8 py-3 rounded-xl font-bold shadow-lg transform hover:scale-105 transition-all duration-200">
              Submit Answer
            </button>
            <button id="nextQuestionBtn" onclick="nextQuestion()" class="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-emerald-600 hover:to-green-700 text-white px-8 py-3 rounded-xl font-bold shadow-lg transform hover:scale-105 transition-all duration-200 hidden">
              Next Question →
            </button>
          </div>
        </div>

        <!-- Enhanced Feedback Area -->
        <div id="feedbackArea" class="mt-8 p-6 rounded-xl hidden">
          <div id="feedbackContent"></div>
          <div id="explanationContent" class="mt-4 text-sm text-gray-700 bg-gray-50 p-4 rounded-lg"></div>
        </div>
      </div>

      <!-- Enhanced Session Complete -->
      <div id="sessionComplete" class="text-center py-16 hidden">
        <div class="text-8xl mb-6">🎉</div>
        <h3 class="text-3xl font-bold text-talon-blue mb-3">Practice Complete!</h3>
        <p class="text-gray-600 text-lg mb-8">Outstanding work! You've completed this flashcard session.</p>
        <div id="sessionStats" class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 mb-8 border border-blue-200">
          <div class="grid grid-cols-3 gap-6 text-center">
            <div>
              <div id="totalAnswered" class="text-3xl font-bold text-talon-blue mb-1">5</div>
              <div class="text-sm font-medium text-blue-700">Answered</div>
            </div>
            <div>
              <div id="correctAnswers" class="text-3xl font-bold text-green-600 mb-1">4</div>
              <div class="text-sm font-medium text-green-700">Correct</div>
            </div>
            <div>
              <div id="accuracyRate" class="text-3xl font-bold text-purple-600 mb-1">80%</div>
              <div class="text-sm font-medium text-purple-700">Accuracy</div>
            </div>
          </div>
        </div>
        <button onclick="startNewSession()" class="bg-gradient-to-r from-talon-blue to-purple-600 hover:from-purple-600 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
          Practice Again
        </button>
      </div>

      <!-- Enhanced Tutorial Flow -->
      <div id="tutorialFlow" class="hidden">
        <!-- Tutorial Step 1: Welcome -->
        <div id="tutorialStep1" class="text-center py-12">
          <div class="text-8xl mb-8">👋</div>
          <h3 class="text-3xl font-bold text-talon-blue mb-4">Welcome to {{ profile.target_language|title }} Learning!</h3>
          <p class="text-gray-600 text-lg mb-2">Let's start with a quick story to introduce you to your first words.</p>
          <p class="text-blue-600 font-medium mb-8">This will take just 2 minutes! 🚀</p>
          <button onclick="showTutorialStory()" class="bg-gradient-to-r from-talon-blue to-purple-600 hover:from-purple-600 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
            Start Learning Journey
          </button>
        </div>

        <!-- Tutorial Step 2: Story Introduction -->
        <div id="tutorialStep2" class="hidden py-6">
          <div class="flex items-center space-x-3 mb-6">
            <span class="text-3xl">📖</span>
            <h3 class="text-2xl font-bold text-talon-blue">Your First {{ profile.target_language|title }} Story</h3>
          </div>
          <div class="bg-gradient-to-r from-blue-50 via-purple-50 to-blue-50 rounded-2xl p-8 mb-8 border-2 border-blue-100">
            <div class="space-y-6">
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Hola, me llamo María.</div>
                <div class="native-text text-gray-600 italic">Hello, my name is María.</div>
              </div>
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Buenos días. ¿Cómo estás?</div>
                <div class="native-text text-gray-600 italic">Good morning. How are you?</div>
              </div>
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Muy bien, gracias.</div>
                <div class="native-text text-gray-600 italic">Very well, thank you.</div>
              </div>
              <div class="story-line bg-white rounded-lg p-4 shadow-sm">
                <div class="target-text text-xl font-bold text-talon-blue mb-2">Hasta luego.</div>
                <div class="native-text text-gray-600 italic">See you later.</div>
              </div>
            </div>
          </div>
          <div class="text-center">
            <p class="text-talon-blue font-medium text-lg mb-2">Congratulations! You just learned 8 important words! 🎉</p>
            <p class="text-gray-600 mb-6">Let's practice them now to make sure you remember.</p>
            <button onclick="startTutorialPractice()" class="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-emerald-600 hover:to-green-700 text-white px-8 py-4 rounded-xl font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200">
              Practice These Words
            </button>
          </div>
        </div>

        <!-- Tutorial Step 3: Guided Practice -->
        <div id="tutorialStep3" class="hidden py-6">
          <div class="flex items-center space-x-3 mb-6">
            <span class="text-3xl">🎯</span>
            <h3 class="text-2xl font-bold text-talon-blue">Let's Practice!</h3>
          </div>
          <div class="bg-gradient-to-r from-yellow-50 to-amber-50 border-2 border-yellow-200 rounded-xl p-6 mb-8">
            <div class="flex items-center">
              <span class="text-3xl mr-4">💡</span>
              <div>
                <div class="font-bold text-yellow-800 text-lg">How it works:</div>
                <div class="text-yellow-700">Read the question, then click the correct answer. Don't worry about mistakes - they help you learn!</div>
              </div>
            </div>
          </div>
          
          <!-- Guided question -->
          <div class="bg-gradient-to-r from-blue-50 via-purple-50 to-blue-50 rounded-xl p-8 mb-8 border-2 border-blue-100">
            <h4 class="text-lg font-bold text-talon-blue mb-4">Question 1/3</h4>
            <p class="text-2xl font-medium text-talon-blue mb-6">What does "Hola" mean in English?</p>
            <div class="grid gap-4">
              <button class="tutorial-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium" data-answer="correct">
                Hello
              </button>
              <button class="tutorial-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium" data-answer="wrong">
                Goodbye
              </button>
              <button class="tutorial-option p-4 text-left border-2 border-gray-200 rounded-xl hover:border-talon-blue hover:bg-blue-50 transition-all duration-200 font-medium" data-answer="wrong">
                Thank you
              </button>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<script>
// Enhanced Flashcard System with TalonTalk branding
let currentFlashcard = null;
let currentQuestionIndex = 0;
let totalQuestions = 5;
let sessionStats = {
    totalAnswered: 0,
    correctAnswers: 0,
    partialCredit: 0
};
let isInTutorial = false;

function startFlashcardPractice() {
    document.getElementById('flashcardModal').classList.remove('hidden');
    document.getElementById('flashcardLoading').classList.remove('hidden');
    document.getElementById('flashcardDisplay').classList.add('hidden');
    document.getElementById('sessionComplete').classList.add('hidden');
    document.getElementById('tutorialFlow').classList.add('hidden');
    
    // Reset session stats
    sessionStats = { totalAnswered: 0, correctAnswers: 0, partialCredit: 0 };
    currentQuestionIndex = 0;
    
    // Check if user has completed tutorial
    if (!localStorage.getItem('tutorialCompleted')) {
        showTutorialFlow();
    } else {
        generateFlashcard();
    }
}

function showTutorialFlow() {
    isInTutorial = true;
    document.getElementById('flashcardLoading').classList.add('hidden');
    document.getElementById('tutorialFlow').classList.remove('hidden');
    document.getElementById('tutorialStep1').classList.remove('hidden');
}

function showTutorialStory() {
    document.getElementById('tutorialStep1').classList.add('hidden');
    document.getElementById('tutorialStep2').classList.remove('hidden');
}

function startTutorialPractice() {
    document.getElementById('tutorialStep2').classList.add('hidden');
    document.getElementById('tutorialStep3').classList.remove('hidden');
    
    // Add click handlers for tutorial options
    document.querySelectorAll('.tutorial-option').forEach(option => {
        option.addEventListener('click', function() {
            const isCorrect = this.dataset.answer === 'correct';
            
            // Remove previous styling
            document.querySelectorAll('.tutorial-option').forEach(opt => {
                opt.classList.remove('border-green-500', 'bg-green-100', 'border-red-500', 'bg-red-100');
            });
            
            if (isCorrect) {
                this.classList.add('border-green-500', 'bg-green-100');
                setTimeout(() => {
                    localStorage.setItem('tutorialCompleted', 'true');
                    showTutorialComplete();
                }, 1500);
            } else {
                this.classList.add('border-red-500', 'bg-red-100');
                // Highlight correct answer
                document.querySelector('[data-answer="correct"]').classList.add('border-green-500', 'bg-green-100');
                setTimeout(() => {
                    localStorage.setItem('tutorialCompleted', 'true');
                    showTutorialComplete();
                }, 2000);
            }
        });
    });
}

function showTutorialComplete() {
    document.getElementById('tutorialFlow').classList.add('hidden');
    document.getElementById('flashcardLoading').classList.remove('hidden');
    isInTutorial = false;
    setTimeout(() => {
        generateFlashcard();
    }, 1000);
}

function resetTutorial() {
    localStorage.removeItem('tutorialCompleted');
    showNotification('Tutorial reset! Next practice session will show the tutorial again.', 'success');
}

function closeFlashcardModal() {
    document.getElementById('flashcardModal').classList.add('hidden');
}

function generateFlashcard() {
    document.getElementById('flashcardLoading').classList.remove('hidden');
    document.getElementById('flashcardDisplay').classList.add('hidden');
    
    fetch('/gamification/flashcard/', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentFlashcard = data.flashcard;
            displayFlashcard();
        } else {
            showNotification('Error generating flashcard. Please try again.', 'error');
            closeFlashcardModal();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error generating flashcard. Please try again.', 'error');
        closeFlashcardModal();
    });
}

function displayFlashcard() {
    document.getElementById('flashcardLoading').classList.add('hidden');
    document.getElementById('flashcardDisplay').classList.remove('hidden');
    
    // Update progress
    currentQuestionIndex++;
    document.getElementById('flashcardProgress').textContent = `${currentQuestionIndex}/${totalQuestions}`;
    document.getElementById('flashcardProgressBar').style.width = `${(currentQuestionIndex / totalQuestions) * 100}%`;
    
    // Display question
    document.getElementById('flashcardQuestion').textContent = currentFlashcard.question;
    
    // Hide hint initially
    document.getElementById('flashcardHint').classList.add('hidden');
    document.getElementById('showHintBtn').classList.remove('hidden');
    
    // Show appropriate input type
    if (currentFlashcard.question_type === 'multiple_choice') {
        document.getElementById('multipleChoiceOptions').classList.remove('hidden');
        document.getElementById('textInputArea').classList.add('hidden');
        
        const options = document.querySelectorAll('.answer-option');
        options.forEach((option, index) => {
            if (currentFlashcard.options[index]) {
                option.textContent = currentFlashcard.options[index];
                option.style.display = 'block';
                option.onclick = () => selectMultipleChoice(option, currentFlashcard.options[index]);
                // Reset styling
                option.classList.remove('border-green-500', 'bg-green-100', 'border-red-500', 'bg-red-100', 'selected');
            } else {
                option.style.display = 'none';
            }
        });
    } else {
        document.getElementById('multipleChoiceOptions').classList.add('hidden');
        document.getElementById('textInputArea').classList.remove('hidden');
        document.getElementById('userAnswer').value = '';
        document.getElementById('userAnswer').focus();
    }
    
    // Reset buttons
    document.getElementById('submitAnswerBtn').classList.remove('hidden');
    document.getElementById('nextQuestionBtn').classList.add('hidden');
    document.getElementById('feedbackArea').classList.add('hidden');
}

let selectedAnswer = null;

function selectMultipleChoice(element, answer) {
    // Remove selection from all options
    document.querySelectorAll('.answer-option').forEach(opt => {
        opt.classList.remove('selected', 'border-talon-blue', 'bg-blue-100');
    });
    
    // Select this option
    element.classList.add('selected', 'border-talon-blue', 'bg-blue-100');
    selectedAnswer = answer;
}

function showHint() {
    const hintElement = document.getElementById('flashcardHint');
    hintElement.querySelector('.text-yellow-700').textContent = currentFlashcard.hint;
    hintElement.classList.remove('hidden');
    document.getElementById('showHintBtn').classList.add('hidden');
}

function submitAnswer() {
    let userAnswer;
    
    if (currentFlashcard.question_type === 'multiple_choice') {
        if (!selectedAnswer) {
            showNotification('Please select an answer first.', 'warning');
            return;
        }
        userAnswer = selectedAnswer;
    } else {
        userAnswer = document.getElementById('userAnswer').value.trim();
        if (!userAnswer) {
            showNotification('Please enter an answer first.', 'warning');
            return;
        }
    }
    
    // Enhanced instant feedback with Levenshtein distance
    const feedback = checkAnswerInstantly(userAnswer, currentFlashcard.correct_answer);
    displayFeedback(feedback);
    
    // Update session stats
    sessionStats.totalAnswered++;
    if (feedback.isCorrect) {
        sessionStats.correctAnswers++;
    } else if (feedback.partialCredit) {
        sessionStats.partialCredit++;
    }
    
    // Hide submit button, show next button
    document.getElementById('submitAnswerBtn').classList.add('hidden');
    
    if (currentQuestionIndex < totalQuestions) {
        document.getElementById('nextQuestionBtn').classList.remove('hidden');
    } else {
        setTimeout(() => {
            showSessionComplete();
        }, 2000);
    }
    
    // Send analytics in background (non-blocking)
    sendAnswerAnalytics(userAnswer, feedback);
}

function checkAnswerInstantly(userAnswer, correctAnswer) {
    const normalizedUser = userAnswer.toLowerCase().trim();
    const normalizedCorrect = correctAnswer.toLowerCase().trim();
    
    // Exact match
    if (normalizedUser === normalizedCorrect) {
        return {
            isCorrect: true,
            partialCredit: false,
            similarity: 1.0,
            message: "Perfect! Excellent work! 🎉"
        };
    }
    
    // Calculate Levenshtein distance for similarity
    const similarity = calculateSimilarity(normalizedUser, normalizedCorrect);
    
    if (similarity >= 0.8) {
        return {
            isCorrect: true,
            partialCredit: true,
            similarity: similarity,
            message: "Very close! Great job! 👏"
        };
    } else if (similarity >= 0.6) {
        return {
            isCorrect: false,
            partialCredit: true,
            similarity: similarity,
            message: "Close, but not quite. Keep trying! 💪"
        };
    } else {
        return {
            isCorrect: false,
            partialCredit: false,
            similarity: similarity,
            message: "Not quite right, but don't give up! 🚀"
        };
    }
}

function calculateSimilarity(str1, str2) {
    const len1 = str1.length;
    const len2 = str2.length;
    
    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;
    
    const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
    
    for (let i = 0; i <= len1; i++) matrix[i][0] = i;
    for (let j = 0; j <= len2; j++) matrix[0][j] = j;
    
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            if (str1[i - 1] === str2[j - 1]) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j - 1] + 1
                );
            }
        }
    }
    
    const maxLen = Math.max(len1, len2);
    return (maxLen - matrix[len1][len2]) / maxLen;
}

function displayFeedback(feedback) {
    const feedbackArea = document.getElementById('feedbackArea');
    const feedbackContent = document.getElementById('feedbackContent');
    const explanationContent = document.getElementById('explanationContent');
    
    if (feedback.isCorrect) {
        feedbackArea.className = 'mt-8 p-6 rounded-xl bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200';
        feedbackContent.innerHTML = `
            <div class="flex items-center space-x-3">
                <span class="text-3xl">✅</span>
                <div>
                    <div class="font-bold text-green-800 text-lg">${feedback.message}</div>
                    <div class="text-green-700">Correct answer: ${currentFlashcard.correct_answer}</div>
                </div>
            </div>
        `;
    } else {
        feedbackArea.className = 'mt-8 p-6 rounded-xl bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200';
        feedbackContent.innerHTML = `
            <div class="flex items-center space-x-3">
                <span class="text-3xl">❌</span>
                <div>
                    <div class="font-bold text-red-800 text-lg">${feedback.message}</div>
                    <div class="text-red-700">Correct answer: ${currentFlashcard.correct_answer}</div>
                </div>
            </div>
        `;
    }
    
    if (currentFlashcard.explanation) {
        explanationContent.innerHTML = `
            <div class="flex items-start space-x-3">
                <span class="text-xl">💡</span>
                <div>
                    <div class="font-medium text-gray-800">Explanation:</div>
                    <div class="text-gray-700">${currentFlashcard.explanation}</div>
                </div>
            </div>
        `;
    } else {
        explanationContent.innerHTML = '';
    }
    
    feedbackArea.classList.remove('hidden');
    
    // Visual feedback for multiple choice
    if (currentFlashcard.question_type === 'multiple_choice') {
        document.querySelectorAll('.answer-option').forEach(option => {
            if (option.textContent === currentFlashcard.correct_answer) {
                option.classList.add('border-green-500', 'bg-green-100');
            } else if (option.classList.contains('selected') && !feedback.isCorrect) {
                option.classList.add('border-red-500', 'bg-red-100');
            }
        });
    }
}

function sendAnswerAnalytics(userAnswer, feedback) {
    // Non-blocking analytics call
    fetch('/api/answer/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            flashcard_id: currentFlashcard.id,
            user_answer: userAnswer,
            is_correct: feedback.isCorrect,
            similarity_score: feedback.similarity
        })
    }).catch(error => {
        console.log('Analytics error (non-critical):', error);
    });
}

function nextQuestion() {
    if (currentQuestionIndex < totalQuestions) {
        generateFlashcard();
    } else {
        showSessionComplete();
    }
}

function showSessionComplete() {
    document.getElementById('flashcardDisplay').classList.add('hidden');
    document.getElementById('sessionComplete').classList.remove('hidden');
    
    // Update session stats display
    const accuracy = sessionStats.totalAnswered > 0 ? 
        Math.round(((sessionStats.correctAnswers + sessionStats.partialCredit * 0.5) / sessionStats.totalAnswered) * 100) : 0;
    
    document.getElementById('totalAnswered').textContent = sessionStats.totalAnswered;
    document.getElementById('correctAnswers').textContent = sessionStats.correctAnswers;
    document.getElementById('accuracyRate').textContent = `${accuracy}%`;
}

function startNewSession() {
    sessionStats = { totalAnswered: 0, correctAnswers: 0, partialCredit: 0 };
    currentQuestionIndex = 0;
    document.getElementById('sessionComplete').classList.add('hidden');
    generateFlashcard();
}

// Enhanced notification system with TalonTalk branding
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    const notification = document.createElement('div');
    notification.className = 'notification fixed top-4 right-4 z-50 p-4 rounded-xl shadow-xl transform translate-x-full transition-transform duration-300';
    
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = 'bg-gradient-to-r from-green-500 to-emerald-600';
            textColor = 'text-white';
            icon = '✅';
            break;
        case 'error':
            bgColor = 'bg-gradient-to-r from-red-500 to-pink-600';
            textColor = 'text-white';
            icon = '❌';
            break;
        case 'warning':
            bgColor = 'bg-gradient-to-r from-falcon-yellow to-amber-500';
            textColor = 'text-talon-blue';
            icon = '⚠️';
            break;
        default:
            bgColor = 'bg-gradient-to-r from-talon-blue to-blue-600';
            textColor = 'text-white';
            icon = 'ℹ️';
    }
    
    notification.className += ` ${bgColor} ${textColor}`;
    notification.innerHTML = `
        <div class="flex items-center space-x-3">
            <span class="text-xl">${icon}</span>
            <span class="font-medium">${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out after 4 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

// Handle Enter key for text input
document.addEventListener('DOMContentLoaded', function() {
    const userAnswerInput = document.getElementById('userAnswer');
    if (userAnswerInput) {
        userAnswerInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submitAnswerBtn').classList.contains('hidden')) {
                submitAnswer();
            }
        });
    }
});
</script>
{% endblock %}
