# TalonTalk AI Services Environment Configuration
# Copy this file to .env and add your API keys

# === LLM Provider API Keys ===
# Choose ONE provider (recommended order for language learning):

# 1. OpenRouter (RECOMMENDED) - Access to many models, free tier available
# Get your key at: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_key_here

# 2. DeepSeek (COST-EFFECTIVE) - Excellent reasoning, very low cost  
# Get your key at: https://platform.deepseek.com/api_keys
DEEPSEEK_API_KEY=your_deepseek_key_here

# 3. OpenAI (RELIABLE) - Most tested, good for production
# Get your key at: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_key_here

# 4. Anthropic (ADVANCED) - Claude models, excellent for complex tasks
# Get your key at: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_key_here

# === Speech Recognition (Future) ===
# DEEPGRAM_API_KEY=your_deepgram_key_here

# === FastAPI Configuration ===
AI_SERVICE_HOST=127.0.0.1
AI_SERVICE_PORT=8001
AI_SERVICE_RELOAD=true

# === Django Integration ===
AI_SERVICE_BASE_URL=http://127.0.0.1:8001
