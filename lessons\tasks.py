"""
Django-RQ Background Tasks for Content Preloading
=================================================

This module contains background tasks for generating personalized lesson queues
using Django-RQ for cross-platform reliability.
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()
from django.db import transaction
import django_rq

from .models import (
    ContentItem,
    UserContentPerformance,
    UserLearningProfile,
    UserLessonQueue,
)
from .preloading_service import content_preloader
from .adaptive_engine import adaptive_engine

logger = logging.getLogger(__name__)


@django_rq.job("default", timeout=300)  # 5 minute timeout
def generate_lesson_playlist(user_id: int, queue_type: str = "daily") -> Dict:
    """
    Background task to generate personalized lesson playlist for a user.

    Args:
        user_id: The user's ID
        queue_type: Type of queue ('daily', 'review', 'weak_areas', 'new_content')

    Returns:
        Dict with task results and statistics
    """
    logger.info(
        f"Starting playlist generation for user {user_id}, queue_type: {queue_type}"
    )

    try:
        user = User.objects.get(id=user_id)

        # Get or create user learning profile
        profile, created = UserLearningProfile.objects.get_or_create(user=user)

        # Generate content based on queue type
        if queue_type == "daily":
            content_ids = _generate_daily_content(user, profile)
        elif queue_type == "review":
            content_ids = _generate_review_content(user, profile)
        elif queue_type == "weak_areas":
            content_ids = _generate_weak_areas_content(user, profile)
        elif queue_type == "new_content":
            content_ids = _generate_new_content(user, profile)
        else:
            content_ids = _generate_daily_content(user, profile)  # fallback

        # Create or update the user's lesson queue
        with transaction.atomic():
            # Remove old expired queues
            UserLessonQueue.objects.filter(
                user=user, queue_type=queue_type, expires_at__lt=timezone.now()
            ).delete()

            # Create new queue
            expires_at = timezone.now() + timedelta(hours=24)  # Expires in 24 hours
            queue = UserLessonQueue.objects.create(
                user=user,
                ordered_content_ids=content_ids,
                queue_type=queue_type,
                total_items=len(content_ids),
                expires_at=expires_at,
            )

        result = {
            "success": True,
            "user_id": user_id,
            "queue_type": queue_type,
            "content_count": len(content_ids),
            "queue_id": queue.id,
            "expires_at": expires_at.isoformat(),
        }

        logger.info(
            f"Playlist generation completed for user {user_id}: {len(content_ids)} items"
        )
        return result

    except User.DoesNotExist:
        error_msg = f"User {user_id} not found"
        logger.error(error_msg)
        return {"success": False, "error": error_msg}

    except Exception as e:
        error_msg = f"Error generating playlist for user {user_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"success": False, "error": error_msg}


@django_rq.job("default", timeout=600)  # 10 minute timeout
def generate_all_user_playlists(queue_type: str = "daily") -> Dict:
    """
    Background task to generate playlists for all active users.

    Args:
        queue_type: Type of queue to generate

    Returns:
        Dict with summary statistics
    """
    logger.info(f"Starting bulk playlist generation, queue_type: {queue_type}")

    # Get active users (users who have been active in the last 7 days)
    cutoff_date = timezone.now() - timedelta(days=7)
    active_users = User.objects.filter(last_login__gte=cutoff_date, is_active=True)

    success_count = 0
    error_count = 0
    errors = []

    for user in active_users:
        try:
            result = generate_lesson_playlist(user.id, queue_type)
            if result.get("success"):
                success_count += 1
            else:
                error_count += 1
                errors.append(f"User {user.id}: {result.get('error', 'Unknown error')}")
        except Exception as e:
            error_count += 1
            errors.append(f"User {user.id}: {str(e)}")

    result = {
        "success": True,
        "queue_type": queue_type,
        "total_users": active_users.count(),
        "success_count": success_count,
        "error_count": error_count,
        "errors": errors[:10],  # Limit to first 10 errors
    }

    logger.info(
        f"Bulk playlist generation completed: {success_count} success, {error_count} errors"
    )
    return result


def _generate_daily_content(user: User, profile: UserLearningProfile) -> List[int]:
    """Generate a balanced mix of content for daily learning (15-60 minutes)"""
    target_count = 20  # ~30 minutes of content

    # Get content that needs review (spaced repetition)
    review_content = _get_review_content(user, count=5)

    # Get content for weak areas
    weak_areas_content = _get_weak_areas_content(user, count=5)

    # Get new content at appropriate difficulty
    new_content = _get_new_content(user, profile, count=10)

    # Combine and shuffle for variety
    all_content = review_content + weak_areas_content + new_content
    content_ids = [item.id for item in all_content[:target_count]]

    return content_ids


def _generate_review_content(user: User, profile: UserLearningProfile) -> List[int]:
    """Generate content specifically for spaced repetition review"""
    review_items = _get_review_content(user, count=15)
    return [item.id for item in review_items]


def _generate_weak_areas_content(user: User, profile: UserLearningProfile) -> List[int]:
    """Generate content targeting user's weak areas"""
    weak_items = _get_weak_areas_content(user, count=15)
    return [item.id for item in weak_items]


def _generate_new_content(user: User, profile: UserLearningProfile) -> List[int]:
    """Generate new content at appropriate difficulty level"""
    new_items = _get_new_content(user, profile, count=15)
    return [item.id for item in new_items]


def _get_review_content(user: User, count: int = 10) -> List[ContentItem]:
    """Get content items due for spaced repetition review"""
    performance_records = (
        UserContentPerformance.objects.filter(
            user=user, next_review_date__lte=timezone.now()
        )
        .select_related("content_item")
        .order_by("next_review_date")[:count]
    )

    return [
        perf.content_item for perf in performance_records if perf.content_item.is_active
    ]


def _get_weak_areas_content(user: User, count: int = 10) -> List[ContentItem]:
    """Get content items where user is struggling"""
    weak_performances = (
        UserContentPerformance.objects.filter(
            user=user, proficiency_score__lt=0.5, times_seen__gte=2
        )
        .select_related("content_item")
        .order_by("proficiency_score")[:count]
    )

    return [
        perf.content_item for perf in weak_performances if perf.content_item.is_active
    ]


def _get_new_content(
    user: User, profile: UserLearningProfile, count: int = 10
) -> List[ContentItem]:
    """Get new content items that user hasn't seen yet"""
    # Get content IDs user has already seen
    seen_content_ids = UserContentPerformance.objects.filter(user=user).values_list(
        "content_item_id", flat=True
    )

    # Get new content at appropriate difficulty
    difficulty = profile.preferred_difficulty
    new_content = (
        ContentItem.objects.filter(
            is_active=True,
            difficulty__lte=difficulty + 1,  # Allow slightly harder content
            language="spanish",  # TODO: Make this dynamic based on user's target language
        )
        .exclude(id__in=seen_content_ids)
        .order_by("difficulty", "?")[:count]
    )  # Random order within difficulty

    return list(new_content)


# Schedule daily playlist generation
@django_rq.job("scheduler")
def schedule_daily_playlist_generation():
    """Schedule daily playlist generation for all users"""
    queue = django_rq.get_queue("default")

    # Schedule for 2 AM server time to avoid peak usage
    queue.enqueue_at(
        timezone.now().replace(hour=2, minute=0, second=0, microsecond=0)
        + timedelta(days=1),
        generate_all_user_playlists,
        queue_type="daily",
    )

    # Also schedule review content generation
    queue.enqueue_at(
        timezone.now().replace(hour=6, minute=0, second=0, microsecond=0)
        + timedelta(days=1),
        generate_all_user_playlists,
        queue_type="review",
    )

    logger.info("Scheduled daily playlist generation tasks")


# Manual trigger functions for admin/development
def trigger_user_playlist_generation(user_id: int, queue_type: str = "daily"):
    """Manually trigger playlist generation for a specific user"""
    queue = django_rq.get_queue("default")
    job = queue.enqueue(generate_lesson_playlist, user_id, queue_type)
    return job


def trigger_all_user_playlists(queue_type: str = "daily"):
    """Manually trigger playlist generation for all users"""
    queue = django_rq.get_queue("default")
    job = queue.enqueue(generate_all_user_playlists, queue_type)
    return job


def get_job_status(job_id: str):
    """Get the status of a background job"""
    try:
        queue = django_rq.get_queue("default")
        job = queue.job_class.fetch(job_id, connection=queue.connection)
        return {
            "id": job.get_id(),
            "status": job.get_status(),
            "result": job.result,
            "created_at": job.created_at,
            "started_at": job.started_at,
            "ended_at": job.ended_at,
        }
    except Exception as e:
        return {"error": str(e)}
