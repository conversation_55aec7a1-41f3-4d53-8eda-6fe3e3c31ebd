/**
 * Type definitions for the TalonTalk C.A.R.E. Framework
 */

// ============================================================================
// Core Types
// ============================================================================

export type PhaseType = 'contextualize' | 'acquire' | 'reinforce' | 'extend';

export type ExerciseType = 'multiple_choice' | 'translation' | 'pronunciation' | 'conversation';

export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';

// ============================================================================
// API Response Types
// ============================================================================

export interface APIResponse<T = unknown> {
    success: boolean;
    content?: T;
    error?: string;
    message?: string;
}

// ============================================================================
// Content Structure Types
// ============================================================================

export interface Scenario {
    title: string;
    description: string;
    location?: string;
    setting?: string;
}

export interface CulturalContext {
    title: string;
    facts: string[];
    tips?: string[];
}

export interface KeyPhrase {
    spanish: string;
    english: string;
    pronunciation: string;
    usage?: string;
}

export interface VocabularyItem {
    word: string;
    translation: string;
    pronunciation: string;
    example: string;
    example_translation: string;
    part_of_speech?: string;
    difficulty?: DifficultyLevel;
}

export interface GrammarStructure {
    pattern: string;
    meaning: string;
    examples: string[];
    usage_notes?: string[];
}

export interface Grammar {
    topic: string;
    structures: GrammarStructure[];
    key_points?: string[];
}

// ============================================================================
// Exercise Types
// ============================================================================

export interface MultipleChoiceExercise {
    type: 'multiple_choice';
    question: string;
    options: string[];
    correct: number;
    explanation: string;
    hint?: string;
}

export interface TranslationExercise {
    type: 'translation';
    question: string;
    answer: string;
    explanation: string;
    hint?: string;
    alternative_answers?: string[];
}

export interface PronunciationExercise {
    type: 'pronunciation';
    phrase: string;
    translation: string;
    phonetic: string;
    audio_url?: string;
}

export interface ConversationExercise {
    type: 'conversation';
    scenario: string;
    participants: string[];
    exchanges: Array<{
        speaker: string;
        text: string;
        translation?: string;
    }>;
    user_responses: string[];
}

export type Exercise =
    | MultipleChoiceExercise
    | TranslationExercise
    | PronunciationExercise
    | ConversationExercise;

// ============================================================================
// Phase Content Types
// ============================================================================

export interface ContextualizeContent {
    scenario: Scenario;
    cultural_context: CulturalContext;
    key_phrases: KeyPhrase[];
    learning_objectives?: string[];
}

export interface AcquireContent {
    vocabulary: VocabularyItem[];
    grammar: Grammar;
    focus_areas?: string[];
}

export interface ReinforceContent {
    exercises: Exercise[];
    practice_goals?: string[];
    difficulty_progression?: DifficultyLevel[];
}

export interface RealWorldApplication {
    title: string;
    description: string;
    scenario: string;
    tasks?: string[];
}

export interface ExpansionTopic {
    topic: string;
    vocabulary: string[];
    phrases: string[];
    cultural_notes?: string[];
}

export interface Homework {
    title: string;
    description: string;
    steps: string[];
    estimated_time?: string;
    resources?: string[];
}

export interface ExtendContent {
    real_world_applications: RealWorldApplication[];
    expansion_topics: ExpansionTopic[];
    homework?: Homework;
    next_lesson_preview?: string;
}

// ============================================================================
// Event Types
// ============================================================================

export interface PhaseNavigationEvent {
    type: 'phase_navigation';
    from_phase: PhaseType;
    to_phase: PhaseType;
    timestamp: Date;
}

export interface ExerciseCompletionEvent {
    type: 'exercise_completion';
    exercise_type: ExerciseType;
    correct: boolean;
    time_taken: number;
    timestamp: Date;
}

export interface ProgressUpdateEvent {
    type: 'progress_update';
    phase: PhaseType;
    progress_percentage: number;
    timestamp: Date;
}

export type CAREEvent = PhaseNavigationEvent | ExerciseCompletionEvent | ProgressUpdateEvent;

// ============================================================================
// Component State Types
// ============================================================================

export interface CARELessonState {
    currentPhase: PhaseType;
    phaseProgress: number;
    totalPhases: number;
    phases: PhaseType[];
    lessonId?: string;
    userId?: string;
    isLoading: boolean;
    error?: string;
}

export interface AITutorState {
    isOpen: boolean;
    messages: Array<{
        id: string;
        sender: 'user' | 'ai';
        content: string;
        timestamp: Date;
    }>;
    isTyping: boolean;
}

// ============================================================================
// Configuration Types
// ============================================================================

export interface CAREConfig {
    apiBaseUrl: string;
    enableAnalytics: boolean;
    enableAITutor: boolean;
    autoSaveProgress: boolean;
    transitionDuration: number;
    maxRetries: number;
}

// ============================================================================
// Error Classes (exported as classes, not types)
// ============================================================================

export class CAREError extends Error {
    constructor(
        message: string,
        public readonly code: string,
        public readonly phase?: PhaseType,
        public readonly cause?: unknown
    ) {
        super(message);
        this.name = 'CAREError';
    }
}

export class APIError extends CAREError {
    constructor(
        message: string,
        public readonly statusCode: number,
        public readonly endpoint: string,
        cause?: unknown
    ) {
        super(message, 'API_ERROR', undefined, cause);
        this.name = 'APIError';
    }
}

export class ValidationError extends CAREError {
    constructor(
        message: string,
        public readonly field: string,
        cause?: unknown
    ) {
        super(message, 'VALIDATION_ERROR', undefined, cause);
        this.name = 'ValidationError';
    }
}

// ============================================================================
// Utility Types
// ============================================================================

export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type EventHandler<T extends CAREEvent> = (event: T) => void | Promise<void>;

export type PhaseContent =
    | ContextualizeContent
    | AcquireContent
    | ReinforceContent
    | ExtendContent;

// ============================================================================
// DOM Element Types
// ============================================================================

export interface CAREElements {
    progressBar: HTMLElement;
    phaseContainer: HTMLElement;
    aiTutorModal: HTMLElement;
    navigationItems: NodeListOf<HTMLElement>;
    phaseIndicators: NodeListOf<HTMLElement>;
}

// ============================================================================
// Animation Types
// ============================================================================

export interface AnimationConfig {
    duration: number;
    easing: string;
    delay?: number;
}

export type AnimationType = 'fadeIn' | 'fadeOut' | 'slideIn' | 'slideOut' | 'bounce';
