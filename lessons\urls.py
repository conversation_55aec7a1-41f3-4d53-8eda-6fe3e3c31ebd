from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    LessonViewSet,
    VocabularyViewSet,
    UserLessonProgressViewSet,
    get_lesson_content_for_care,
    complete_lesson,
    get_user_progress,
)

router = DefaultRouter()
router.register(r"lessons", LessonViewSet)
router.register(r"vocabularies", VocabularyViewSet)
router.register(r"progress", UserLessonProgressViewSet)

urlpatterns = [
    path("", include(router.urls)),
    path(
        "lessons/<int:lesson_id>/care-content/",
        get_lesson_content_for_care,
        name="lesson-care-content",
    ),
    path(
        "lessons/complete/",
        complete_lesson,
        name="complete-lesson",
    ),
    path(
        "user/progress/",
        get_user_progress,
        name="user-progress",
    ),
]
