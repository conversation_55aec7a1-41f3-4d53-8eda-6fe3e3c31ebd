"""
Advanced Flashcard Generator for TalonTalk
Optimized for DeepSeek R1's superior reasoning and multilingual capabilities
"""

import json
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from openai import OpenAI

from .models import ContentItem
from .advanced_quality_engine import AdvancedQualityEngine, QualityLevel
from ai_services.llm_config import get_recommended_config

logger = logging.getLogger(__name__)


@dataclass
class FlashcardTemplate:
    """Template for different types of flashcards"""
    name: str
    description: str
    prompt_template: str
    difficulty_scaling: Dict[int, str]
    quality_requirements: Dict[str, any]


class R1FlashcardGenerator:
    """
    Advanced flashcard generator leveraging DeepSeek R1's capabilities
    """
    
    def __init__(self):
        self.config = get_recommended_config()
        self.client = self._setup_client()
        self.quality_engine = AdvancedQualityEngine()
        self.templates = self._load_flashcard_templates()
    
    def _setup_client(self):
        """Setup OpenAI-compatible client"""
        try:
            client = OpenAI(api_key=self.config.api_key, base_url=self.config.base_url)
            logger.info(f"✅ R1 Flashcard Generator initialized with {self.config.model_config.name}")
            return client
        except Exception as e:
            logger.error(f"❌ Failed to initialize R1 client: {e}")
            return None
    
    def _load_flashcard_templates(self) -> Dict[str, FlashcardTemplate]:
        """Load sophisticated flashcard templates optimized for R1"""
        return {
            "vocabulary_contextual": FlashcardTemplate(
                name="Contextual Vocabulary",
                description="Vocabulary with rich cultural and situational context",
                prompt_template="""
Create a contextual vocabulary flashcard for {language} learners at {difficulty_level}.

REQUIREMENTS:
- Focus on the word: {target_word}
- Provide real-world context and cultural significance
- Include pronunciation guide with IPA notation
- Add memory techniques and etymology when helpful
- Create engaging, memorable scenarios

STRUCTURE:
{{
    "question_text": "Contextual question in {language} with scenario",
    "answer_text": "Target word with pronunciation",
    "explanation_text": "Detailed explanation with cultural context, usage notes, and memory aids",
    "choices_json": ["correct_answer", "plausible_distractor_1", "plausible_distractor_2", "plausible_distractor_3"],
    "hint_text": "Subtle hint that guides without giving away",
    "progressive_hints": ["gentle_nudge", "more_specific_hint", "almost_the_answer"],
    "cultural_context": "Cultural significance and usage scenarios",
    "memory_technique": "Mnemonic or memory aid"
}}

Generate ONE high-quality flashcard following this structure.
""",
                difficulty_scaling={
                    1: "A1 Beginner - Common daily words with simple contexts",
                    2: "A2 Elementary - Familiar situations and basic descriptions", 
                    3: "B1 Intermediate - Complex situations and abstract concepts",
                    4: "B2 Upper-Intermediate - Nuanced meanings and professional contexts",
                    5: "C1 Advanced - Sophisticated vocabulary and cultural subtleties"
                },
                quality_requirements={
                    "min_explanation_length": 100,
                    "requires_cultural_context": True,
                    "requires_pronunciation": True,
                    "requires_memory_aid": True
                }
            ),
            
            "grammar_situational": FlashcardTemplate(
                name="Situational Grammar",
                description="Grammar rules taught through real-life situations",
                prompt_template="""
Create a situational grammar flashcard for {language} learners at {difficulty_level}.

FOCUS: {grammar_topic}

REQUIREMENTS:
- Present grammar through realistic scenarios
- Explain the rule with clear examples
- Show common mistakes and how to avoid them
- Provide pattern recognition techniques
- Include cultural appropriateness notes

STRUCTURE:
{{
    "question_text": "Scenario-based question testing {grammar_topic}",
    "answer_text": "Correct grammatical form",
    "explanation_text": "Rule explanation with examples, common mistakes, and usage patterns",
    "choices_json": ["correct_form", "common_mistake_1", "common_mistake_2", "related_confusion"],
    "hint_text": "Grammar pattern hint",
    "progressive_hints": ["rule_category", "specific_pattern", "almost_complete_rule"],
    "grammar_rule": "Formal rule statement",
    "usage_examples": "Multiple contextual examples",
    "common_mistakes": "Typical errors and corrections"
}}

Generate ONE comprehensive grammar flashcard.
""",
                difficulty_scaling={
                    1: "A1 - Present tense, basic word order, simple questions",
                    2: "A2 - Past tense, comparatives, basic conjunctions",
                    3: "B1 - Subjunctive, complex tenses, conditional statements", 
                    4: "B2 - Advanced subjunctive, passive voice, complex clauses",
                    5: "C1 - Nuanced grammar, stylistic variations, formal registers"
                },
                quality_requirements={
                    "min_explanation_length": 150,
                    "requires_examples": True,
                    "requires_mistake_analysis": True,
                    "requires_pattern_explanation": True
                }
            ),
            
            "cultural_immersion": FlashcardTemplate(
                name="Cultural Immersion",
                description="Language learning through cultural understanding",
                prompt_template="""
Create a cultural immersion flashcard for {language} learners at {difficulty_level}.

CULTURAL TOPIC: {cultural_aspect}

REQUIREMENTS:
- Integrate language learning with cultural understanding
- Provide authentic cultural scenarios
- Explain social norms and expectations
- Include regional variations when relevant
- Connect language use to cultural values

STRUCTURE:
{{
    "question_text": "Cultural scenario requiring appropriate language response",
    "answer_text": "Culturally appropriate response",
    "explanation_text": "Cultural context, social norms, and language appropriateness",
    "choices_json": ["appropriate_response", "too_formal", "too_casual", "culturally_inappropriate"],
    "hint_text": "Cultural context clue",
    "progressive_hints": ["social_setting", "relationship_dynamic", "cultural_expectation"],
    "cultural_insight": "Deep cultural explanation",
    "regional_notes": "Regional or dialectal variations",
    "social_context": "When and where to use this language"
}}

Generate ONE rich cultural immersion flashcard.
""",
                difficulty_scaling={
                    1: "A1 - Basic greetings, politeness, simple social interactions",
                    2: "A2 - Family dynamics, shopping etiquette, basic social norms",
                    3: "B1 - Workplace culture, friendship customs, social expectations",
                    4: "B2 - Professional etiquette, cultural values, complex social situations", 
                    5: "C1 - Cultural nuances, historical context, sophisticated social dynamics"
                },
                quality_requirements={
                    "min_explanation_length": 120,
                    "requires_cultural_context": True,
                    "requires_social_context": True,
                    "requires_appropriateness_notes": True
                }
            )
        }
    
    def generate_flashcard_batch(
        self,
        language: str,
        difficulty: int,
        template_type: str,
        batch_size: int = 10,
        topic_focus: Optional[str] = None
    ) -> Dict:
        """
        Generate a batch of high-quality flashcards using R1's advanced reasoning
        """
        if not self.client:
            return {"success": False, "error": "R1 client not available"}
        
        if template_type not in self.templates:
            return {"success": False, "error": f"Unknown template type: {template_type}"}
        
        template = self.templates[template_type]
        generated_flashcards = []
        rejected_count = 0
        
        logger.info(f"🚀 Generating {batch_size} {template.name} flashcards for {language} (Level {difficulty})")
        
        for i in range(batch_size):
            try:
                # Generate single flashcard
                flashcard_data = self._generate_single_flashcard(
                    language, difficulty, template, topic_focus
                )
                
                if flashcard_data:
                    # Validate quality
                    quality_report = self.quality_engine.validate_content(flashcard_data, language)
                    
                    if (quality_report.overall_score >= 75 and 
                        quality_report.quality_level != QualityLevel.REJECTED):
                        
                        # Create ContentItem
                        flashcard = self._create_content_item(flashcard_data, language, difficulty)
                        if flashcard:
                            generated_flashcards.append(flashcard)
                            logger.info(f"✅ Generated quality flashcard {i+1}/{batch_size} (Score: {quality_report.overall_score:.1f})")
                    else:
                        rejected_count += 1
                        logger.warning(f"❌ Rejected flashcard {i+1} (Score: {quality_report.overall_score:.1f})")
                
            except Exception as e:
                logger.error(f"Error generating flashcard {i+1}: {e}")
                rejected_count += 1
        
        success_rate = len(generated_flashcards) / batch_size * 100
        
        return {
            "success": True,
            "flashcards": generated_flashcards,
            "generated_count": len(generated_flashcards),
            "rejected_count": rejected_count,
            "success_rate": success_rate,
            "template_used": template.name,
            "language": language,
            "difficulty": difficulty
        }
    
    def _generate_single_flashcard(
        self, 
        language: str, 
        difficulty: int, 
        template: FlashcardTemplate,
        topic_focus: Optional[str] = None
    ) -> Optional[Dict]:
        """Generate a single flashcard using R1's reasoning"""
        
        # Prepare prompt with template
        difficulty_description = template.difficulty_scaling.get(difficulty, "Intermediate")
        
        # Add topic-specific context
        topic_context = ""
        if topic_focus:
            topic_context = f"\nSPECIFIC FOCUS: {topic_focus}"
        
        prompt = template.prompt_template.format(
            language=language.title(),
            difficulty_level=difficulty_description,
            target_word=topic_focus or "contextually appropriate vocabulary",
            grammar_topic=topic_focus or "relevant grammar pattern",
            cultural_aspect=topic_focus or "important cultural element"
        ) + topic_context
        
        try:
            response = self.client.chat.completions.create(
                model=self.config.model_config.name,
                messages=[
                    {
                        "role": "system",
                        "content": f"""You are an expert language learning content creator with deep expertise in {language} language and culture. 

Your specialty is creating exceptional educational content that:
- Leverages advanced pedagogical principles
- Integrates cultural understanding with language learning
- Uses sophisticated reasoning to create memorable, effective learning experiences
- Provides rich context and meaningful connections
- Follows evidence-based language acquisition methods

Create content that would impress professional language educators and engage learners deeply."""
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config.model_config.max_tokens or 3000,
                temperature=0.7
            )
            
            # Parse response
            content = response.choices[0].message.content.strip()
            
            # Extract JSON from response
            if "```json" in content:
                json_start = content.find("```json") + 7
                json_end = content.find("```", json_start)
                content = content[json_start:json_end].strip()
            elif "{" in content and "}" in content:
                json_start = content.find("{")
                json_end = content.rfind("}") + 1
                content = content[json_start:json_end]
            
            flashcard_data = json.loads(content)
            
            # Add metadata
            flashcard_data.update({
                "type": "flashcard",
                "language": language,
                "difficulty": difficulty,
                "template_type": template.name,
                "generated_by": "deepseek_r1"
            })
            
            return flashcard_data
            
        except Exception as e:
            logger.error(f"Error in R1 flashcard generation: {e}")
            return None
    
    def _create_content_item(self, flashcard_data: Dict, language: str, difficulty: int) -> Optional[ContentItem]:
        """Create ContentItem from flashcard data"""
        try:
            content_item = ContentItem.objects.create(
                type="flashcard",
                question_text=flashcard_data.get("question_text", ""),
                answer_text=flashcard_data.get("answer_text", ""),
                explanation_text=flashcard_data.get("explanation_text", ""),
                hint_text=flashcard_data.get("hint_text", ""),
                choices_json=flashcard_data.get("choices_json", []),
                progressive_hints=flashcard_data.get("progressive_hints", []),
                language=language,
                difficulty=difficulty,
                cefr_level=self._difficulty_to_cefr(difficulty),
                tags=self._generate_tags(flashcard_data)
            )
            
            return content_item
            
        except Exception as e:
            logger.error(f"Error creating ContentItem: {e}")
            return None
    
    def _difficulty_to_cefr(self, difficulty: int) -> str:
        """Convert difficulty level to CEFR level"""
        mapping = {1: "A1", 2: "A2", 3: "B1", 4: "B2", 5: "C1"}
        return mapping.get(difficulty, "A1")
    
    def _generate_tags(self, flashcard_data: Dict) -> List[str]:
        """Generate appropriate tags for the flashcard"""
        tags = ["flashcard", "r1_generated"]
        
        template_type = flashcard_data.get("template_type", "")
        if "vocabulary" in template_type:
            tags.append("vocabulary")
        elif "grammar" in template_type:
            tags.append("grammar")
        elif "cultural" in template_type:
            tags.extend(["culture", "social"])
        
        return tags
