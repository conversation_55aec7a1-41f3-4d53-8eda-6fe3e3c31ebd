<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug C.A.R.E. Final</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">C.A.R.E. Final Debug Test</h1>

        <!-- Test JavaScript loading -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">JavaScript Test</h2>
            <div id="jsTest" class="text-red-500">JavaScript not loaded</div>
            <button id="testBtn" class="bg-blue-500 text-white px-4 py-2 rounded mt-4">Test Button</button>
        </div>

        <!-- Phase containers -->
        <div id="contextualizePhase" class="phase-container hidden">
            <h3>Contextualize Phase</h3>
        </div>
        <div id="acquirePhase" class="phase-container hidden">
            <h3>Acquire Phase</h3>
        </div>
        <div id="reinforcePhase" class="phase-container hidden">
            <h3>Reinforce Phase</h3>
        </div>
        <div id="extendPhase" class="phase-container hidden">
            <h3>Extend Phase</h3>
        </div>

        <!-- Progress container -->
        <div id="careProgress" class="mt-6">
            <div class="bg-gray-200 rounded-full h-2">
                <div id="progressBar" class="bg-blue-500 h-2 rounded-full" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <!-- CSRF Token -->
    <input type="hidden" name="csrfmiddlewaretoken" value="test-token">

    <script>
        console.log('Debug page loaded');

        // Test basic JavaScript functionality
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM Content Loaded');
            document.getElementById('jsTest').textContent = 'JavaScript loaded successfully!';
            document.getElementById('jsTest').className = 'text-green-500';

            document.getElementById('testBtn').addEventListener('click', function () {
                console.log('Test button clicked');
                alert('Test button works!');
            });
        });
    </script>

    <!-- Load the actual C.A.R.E. JavaScript -->
    <script>
        // First test if we can load the JavaScript file
        console.log('Loading C.A.R.E. JavaScript...');

        // Check if the file exists and can be loaded
        fetch('./talontalk/static/js/care-lesson.js')
            .then(response => {
                if (response.ok) {
                    console.log('C.A.R.E. JavaScript file found');
                    return response.text();
                } else {
                    console.error('C.A.R.E. JavaScript file not found');
                    throw new Error('File not found');
                }
            })
            .then(jsContent => {
                console.log('C.A.R.E. JavaScript content loaded, length:', jsContent.length);

                // Check for syntax errors by trying to create a function
                try {
                    new Function(jsContent);
                    console.log('JavaScript syntax is valid');
                } catch (error) {
                    console.error('JavaScript syntax error:', error);
                }
            })
            .catch(error => {
                console.error('Error loading C.A.R.E. JavaScript:', error);
            });
    </script>
</body>

</html>