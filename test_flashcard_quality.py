#!/usr/bin/env python3
"""
TalonTalk Flashcard System Quality Assurance Test Suite
Tests all aspects of the flashcard generation and learning system
"""

import requests
import json
import time
import sys
from typing import Dict, List, Optional

class FlashcardQATest:
    def __init__(self, base_url="http://127.0.0.1:8000"):
        self.base_url = base_url
        self.test_results = []
        self.passed = 0
        self.failed = 0
    
    def log_test(self, test_name: str, passed: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        result = {
            "test": test_name,
            "passed": passed,
            "details": details,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        if passed:
            self.passed += 1
        else:
            self.failed += 1
        
        print(f"{status} {test_name}")
        if details and not passed:
            print(f"    Details: {details}")
    
    def test_api_endpoint_accessibility(self):
        """Test that the flashcard API endpoint is accessible"""
        try:
            response = requests.get(f"{self.base_url}/api/flashcard/", timeout=10)
            self.log_test(
                "API Endpoint Accessibility",
                response.status_code == 200,
                f"Status: {response.status_code}"
            )
            return response.status_code == 200
        except Exception as e:
            self.log_test(
                "API Endpoint Accessibility",
                False,
                f"Connection error: {e}"
            )
            return False
    
    def test_response_format(self):
        """Test that API returns properly formatted JSON"""
        try:
            response = requests.get(f"{self.base_url}/api/flashcard/")
            data = response.json()
            
            required_fields = ["success", "flashcard"]
            flashcard_fields = ["question", "options", "correct_answer", "explanation"]
            
            # Check top-level fields
            has_required = all(field in data for field in required_fields)
            if not has_required:
                self.log_test(
                    "Response Format - Top Level",
                    False,
                    f"Missing fields. Got: {list(data.keys())}"
                )
                return False
            
            # Check flashcard fields
            flashcard = data.get("flashcard", {})
            has_flashcard_fields = all(field in flashcard for field in flashcard_fields)
            
            self.log_test(
                "Response Format - Flashcard Fields",
                has_flashcard_fields,
                f"Flashcard fields: {list(flashcard.keys())}"
            )
            
            return has_flashcard_fields
            
        except Exception as e:
            self.log_test(
                "Response Format",
                False,
                f"JSON parsing error: {e}"
            )
            return False
    
    def test_question_quality(self):
        """Test the quality of generated questions"""
        try:
            response = requests.get(f"{self.base_url}/api/flashcard/?language=spanish")
            data = response.json()
            flashcard = data.get("flashcard", {})
            
            question = flashcard.get("question", "")
            correct_answer = flashcard.get("correct_answer", "")
            options = flashcard.get("options", [])
            
            # Test 1: Question format (should use "¿Cómo se dice" format)
            good_format = "¿Cómo se dice" in question
            self.log_test(
                "Question Format Quality",
                good_format,
                f"Question: {question}"
            )
            
            # Test 2: Correct answer in options
            correct_in_options = any(
                opt.lower() == correct_answer.lower() for opt in options
            )
            self.log_test(
                "Correct Answer in Options",
                correct_in_options,
                f"Answer: {correct_answer}, Options: {options}"
            )
            
            # Test 3: No circular logic
            has_circular_logic = False
            if "qué palabra" in question.lower() and "significa" in question.lower():
                # Check if asking for Spanish word meaning and answer is that word
                import re
                match = re.search(r"significa ['\"]([^'\"]+)['\"]", question.lower())
                if match and match.group(1) == correct_answer.lower():
                    has_circular_logic = True
            
            self.log_test(
                "No Circular Logic",
                not has_circular_logic,
                f"Question: {question}, Answer: {correct_answer}"
            )
            
            # Test 4: Proper Spanish question markers
            has_spanish_markers = question.startswith("¿") and question.endswith("?")
            self.log_test(
                "Proper Spanish Question Markers",
                has_spanish_markers,
                f"Question: {question}"
            )
            
            return good_format and correct_in_options and not has_circular_logic
            
        except Exception as e:
            self.log_test(
                "Question Quality",
                False,
                f"Error: {e}"
            )
            return False
    
    def test_different_parameters(self):
        """Test different parameter combinations"""
        test_params = [
            {"language": "spanish", "difficulty": "beginner", "type": "multiple_choice"},
            {"language": "spanish", "difficulty": "intermediate", "type": "multiple_choice"},
            {"language": "spanish", "difficulty": "beginner", "type": "fill_blank"},
        ]
        
        all_passed = True
        
        for params in test_params:
            try:
                param_str = "&".join([f"{k}={v}" for k, v in params.items()])
                response = requests.get(f"{self.base_url}/api/flashcard/?{param_str}")
                
                success = response.status_code == 200 and response.json().get("success", False)
                
                param_label = f"Params: {params}"
                self.log_test(
                    f"Parameter Test - {param_label}",
                    success,
                    f"Status: {response.status_code}"
                )
                
                if not success:
                    all_passed = False
                    
            except Exception as e:
                self.log_test(
                    f"Parameter Test - {params}",
                    False,
                    f"Error: {e}"
                )
                all_passed = False
        
        return all_passed
    
    def test_consistency(self):
        """Test that multiple requests return consistent quality"""
        consistent_quality = True
        
        for i in range(3):
            try:
                response = requests.get(f"{self.base_url}/api/flashcard/?language=spanish")
                data = response.json()
                flashcard = data.get("flashcard", {})
                
                # Check basic quality markers
                question = flashcard.get("question", "")
                correct_answer = flashcard.get("correct_answer", "")
                options = flashcard.get("options", [])
                
                has_good_format = "¿Cómo se dice" in question
                correct_in_options = any(
                    opt.lower() == correct_answer.lower() for opt in options
                )
                
                test_passed = has_good_format and correct_in_options
                
                self.log_test(
                    f"Consistency Test #{i+1}",
                    test_passed,
                    f"Question: {question[:50]}..."
                )
                
                if not test_passed:
                    consistent_quality = False
                    
            except Exception as e:
                self.log_test(
                    f"Consistency Test #{i+1}",
                    False,
                    f"Error: {e}"
                )
                consistent_quality = False
        
        return consistent_quality
    
    def test_ui_integration(self):
        """Test that the flashcard page loads correctly"""
        try:
            response = requests.get(f"{self.base_url}/practice/flashcards/")
            page_loads = response.status_code == 200
            
            # Basic check that it's an HTML page
            is_html = "<!DOCTYPE html" in response.text
            
            self.log_test(
                "Flashcard Page Load",
                page_loads and is_html,
                f"Status: {response.status_code}, Is HTML: {is_html}"
            )
            
            return page_loads and is_html
            
        except Exception as e:
            self.log_test(
                "Flashcard Page Load",
                False,
                f"Error: {e}"
            )
            return False
    
    def run_all_tests(self):
        """Run all tests and return overall result"""
        print("🧪 TalonTalk Flashcard Quality Assurance Test Suite")
        print("=" * 50)
        
        # Run tests in logical order
        tests = [
            self.test_api_endpoint_accessibility,
            self.test_response_format,
            self.test_question_quality,
            self.test_different_parameters,
            self.test_consistency,
            self.test_ui_integration,
        ]
        
        for test in tests:
            test()
            time.sleep(0.5)  # Brief pause between tests
        
        print("\n" + "=" * 50)
        print(f"📊 Test Results: {self.passed} passed, {self.failed} failed")
        
        if self.failed == 0:
            print("🎉 All tests passed! Flashcard system is working correctly.")
            return True
        else:
            print(f"⚠️  {self.failed} test(s) failed. Please review the issues above.")
            return False
    
    def generate_report(self) -> Dict:
        """Generate a detailed test report"""
        return {
            "timestamp": time.time(),
            "total_tests": len(self.test_results),
            "passed": self.passed,
            "failed": self.failed,
            "success_rate": self.passed / len(self.test_results) if self.test_results else 0,
            "details": self.test_results
        }


if __name__ == "__main__":
    # Run the test suite
    tester = FlashcardQATest()
    success = tester.run_all_tests()
    
    # Generate and save report
    report = tester.generate_report()
    
    with open("flashcard_qa_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: flashcard_qa_report.json")
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
