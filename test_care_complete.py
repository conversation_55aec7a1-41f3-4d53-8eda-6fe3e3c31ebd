#!/usr/bin/env python3
"""
Complete C.A.R.E. Framework Test
Tests all four phases and their data rendering
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "talontalk.settings")
django.setup()


def test_care_phases():
    """Test all C.A.R.E. phases and their API responses"""

    base_url = "http://127.0.0.1:8000"
    phases = ["contextualize", "acquire", "reinforce", "extend"]

    print("🎯 Testing C.A.R.E. Framework Complete Integration")
    print("=" * 60)

    all_passed = True

    for phase in phases:
        print(f"\n📋 Testing {phase.upper()} Phase:")
        print("-" * 40)

        try:
            # Test API endpoint
            url = f"{base_url}/care/api/phase/{phase}/"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()

                # Check response format
                if "success" in data and data["success"]:
                    print(f"✅ API Response: Valid format")

                    content = data.get("content", {})

                    # Phase-specific content validation
                    if phase == "contextualize":
                        required_keys = ["scenario", "cultural_context", "key_phrases"]
                        for key in required_keys:
                            if key in content:
                                print(f"✅ {key}: Present")
                            else:
                                print(f"❌ {key}: Missing")
                                all_passed = False

                        # Check scenario structure
                        scenario = content.get("scenario", {})
                        if "title" in scenario and "description" in scenario:
                            print(f"✅ Scenario structure: Valid")
                        else:
                            print(f"❌ Scenario structure: Invalid")
                            all_passed = False

                    elif phase == "acquire":
                        if "vocabulary" in content and isinstance(
                            content["vocabulary"], list
                        ):
                            vocab_count = len(content["vocabulary"])
                            print(f"✅ Vocabulary: {vocab_count} items")

                            # Check vocabulary structure
                            if vocab_count > 0:
                                vocab_item = content["vocabulary"][0]
                                required_vocab_keys = [
                                    "word",
                                    "translation",
                                    "pronunciation",
                                    "example",
                                ]
                                vocab_valid = all(
                                    key in vocab_item for key in required_vocab_keys
                                )
                                if vocab_valid:
                                    print(f"✅ Vocabulary structure: Valid")
                                else:
                                    print(f"❌ Vocabulary structure: Invalid")
                                    all_passed = False
                        else:
                            print(f"❌ Vocabulary: Missing or invalid")
                            all_passed = False

                        if "grammar" in content:
                            print(f"✅ Grammar: Present")
                        else:
                            print(f"❌ Grammar: Missing")
                            all_passed = False

                    elif phase == "reinforce":
                        if "exercises" in content and isinstance(
                            content["exercises"], list
                        ):
                            exercise_count = len(content["exercises"])
                            print(f"✅ Exercises: {exercise_count} items")

                            # Check exercise types
                            exercise_types = set()
                            for exercise in content["exercises"]:
                                if "type" in exercise:
                                    exercise_types.add(exercise["type"])

                            print(f"✅ Exercise types: {', '.join(exercise_types)}")
                        else:
                            print(f"❌ Exercises: Missing or invalid")
                            all_passed = False

                    elif phase == "extend":
                        expected_keys = [
                            "real_world_applications",
                            "expansion_topics",
                            "homework",
                        ]
                        for key in expected_keys:
                            if key in content:
                                print(f"✅ {key}: Present")
                            else:
                                print(f"⚠️ {key}: Missing (optional)")

                    print(f"✅ Content size: {len(str(content))} characters")

                else:
                    print(f"❌ API Response: Invalid format")
                    print(f"   Response: {data}")
                    all_passed = False
            else:
                print(f"❌ API Status: {response.status_code}")
                print(f"   Response: {response.text}")
                all_passed = False

        except requests.exceptions.RequestException as e:
            print(f"❌ Connection Error: {e}")
            all_passed = False
        except Exception as e:
            print(f"❌ Unexpected Error: {e}")
            all_passed = False

    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL C.A.R.E. PHASES WORKING CORRECTLY!")
        print("✅ Ready for production use")
    else:
        print("⚠️ Some issues found - check details above")

    return all_passed


def test_care_lesson_page():
    """Test the main C.A.R.E. lesson page"""

    print("\n🌐 Testing C.A.R.E. Lesson Page:")
    print("-" * 40)

    try:
        url = "http://127.0.0.1:8000/care/lesson/"
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            print("✅ Lesson page loads successfully")

            # Check for key elements in the HTML
            html = response.text
            checks = [
                ("C.A.R.E. Framework", "Title present"),
                ("contextualize", "Contextualize phase"),
                ("acquire", "Acquire phase"),
                ("reinforce", "Reinforce phase"),
                ("extend", "Extend phase"),
                ("care-lesson.js", "JavaScript loaded"),
                ("progress-bar", "Progress tracking"),
            ]

            for check_text, description in checks:
                if check_text.lower() in html.lower():
                    print(f"✅ {description}")
                else:
                    print(f"⚠️ {description}: Not found")

            return True
        else:
            print(f"❌ Page load failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error loading page: {e}")
        return False


def main():
    """Run all C.A.R.E. tests"""

    print(f"🚀 C.A.R.E. Framework Complete Test")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    # Test API endpoints
    api_success = test_care_phases()

    # Test lesson page
    page_success = test_care_lesson_page()

    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"   API Endpoints: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"   Lesson Page: {'✅ PASS' if page_success else '❌ FAIL'}")

    if api_success and page_success:
        print("\n🎉 C.A.R.E. FRAMEWORK IS FULLY FUNCTIONAL!")
        print("✅ All phases loading with structured data")
        print("✅ JavaScript rendering working correctly")
        print("✅ Ready for user testing and production")
    else:
        print("\n⚠️ Some components need attention")
        print("   Check individual test results above")

    return api_success and page_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
