"""
CEFR Progression Service for TalonTalk
Manages user progression through Common European Framework of Reference levels
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from django.db.models import Avg, Count, Q

from lessons.models import (
    UserLearningProfile,
    UserContentPerformance,
    UserLessonProgress,
    ContentItem,
    Lesson,
)

logger = logging.getLogger(__name__)


class CEFRProgressionService:
    """
    Service for managing CEFR level progression based on user performance
    """
    
    # CEFR level order for progression
    CEFR_ORDER = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2']
    
    # Minimum requirements for CEFR level advancement
    ADVANCEMENT_REQUIREMENTS = {
        'A1': {'accuracy': 0.75, 'lessons_completed': 5, 'content_mastered': 20},
        'A2': {'accuracy': 0.80, 'lessons_completed': 8, 'content_mastered': 35},
        'B1': {'accuracy': 0.82, 'lessons_completed': 12, 'content_mastered': 50},
        'B2': {'accuracy': 0.85, 'lessons_completed': 15, 'content_mastered': 75},
        'C1': {'accuracy': 0.88, 'lessons_completed': 20, 'content_mastered': 100},
        'C2': {'accuracy': 0.90, 'lessons_completed': 25, 'content_mastered': 150},
    }
    
    @classmethod
    def evaluate_user_progression(cls, user) -> Dict:
        """
        Evaluate if user is ready for CEFR level advancement
        """
        try:
            profile, _ = UserLearningProfile.objects.get_or_create(user=user)
            current_cefr = profile.current_cefr_level
            
            # Get user's performance data
            performance_data = cls._get_user_performance_data(user, current_cefr)
            
            # Check if user meets advancement requirements
            advancement_check = cls._check_advancement_requirements(
                current_cefr, performance_data
            )
            
            # Determine recommended action
            recommendation = cls._get_progression_recommendation(
                current_cefr, advancement_check, performance_data
            )
            
            return {
                'current_cefr_level': current_cefr,
                'performance_data': performance_data,
                'advancement_check': advancement_check,
                'recommendation': recommendation,
                'ready_for_advancement': advancement_check['meets_requirements'],
            }
            
        except Exception as e:
            logger.error(f"Error evaluating CEFR progression for user {user.id}: {e}")
            return {'error': str(e)}
    
    @classmethod
    def _get_user_performance_data(cls, user, cefr_level: str) -> Dict:
        """
        Get comprehensive performance data for the user at current CEFR level
        """
        # Get performance for current CEFR level content
        cefr_performances = UserContentPerformance.objects.filter(
            user=user,
            content_item__cefr_level=cefr_level
        )
        
        # Get lesson progress for current CEFR level
        cefr_lessons = UserLessonProgress.objects.filter(
            user=user,
            lesson__cefr_level=cefr_level,
            completed=True
        )
        
        # Calculate metrics
        total_content_items = ContentItem.objects.filter(cefr_level=cefr_level).count()
        mastered_content = cefr_performances.filter(proficiency_score__gte=0.8).count()
        
        accuracy = 0.0
        if cefr_performances.exists():
            accuracy = cefr_performances.aggregate(
                avg_accuracy=Avg('proficiency_score')
            )['avg_accuracy'] or 0.0
        
        return {
            'total_attempts': cefr_performances.count(),
            'accuracy': accuracy,
            'lessons_completed': cefr_lessons.count(),
            'content_mastered': mastered_content,
            'total_content_available': total_content_items,
            'mastery_percentage': (mastered_content / max(total_content_items, 1)) * 100,
        }
    
    @classmethod
    def _check_advancement_requirements(cls, cefr_level: str, performance_data: Dict) -> Dict:
        """
        Check if user meets requirements for advancing to next CEFR level
        """
        requirements = cls.ADVANCEMENT_REQUIREMENTS.get(cefr_level, {})
        
        meets_accuracy = performance_data['accuracy'] >= requirements.get('accuracy', 0.75)
        meets_lessons = performance_data['lessons_completed'] >= requirements.get('lessons_completed', 5)
        meets_content = performance_data['content_mastered'] >= requirements.get('content_mastered', 20)
        
        return {
            'meets_accuracy': meets_accuracy,
            'meets_lessons': meets_lessons,
            'meets_content': meets_content,
            'meets_requirements': meets_accuracy and meets_lessons and meets_content,
            'requirements': requirements,
        }
    
    @classmethod
    def _get_progression_recommendation(cls, current_cefr: str, advancement_check: Dict, performance_data: Dict) -> Dict:
        """
        Generate progression recommendation based on performance
        """
        if advancement_check['meets_requirements']:
            next_cefr = cls._get_next_cefr_level(current_cefr)
            return {
                'action': 'advance',
                'next_level': next_cefr,
                'message': f"Congratulations! You're ready to advance to {next_cefr} level.",
                'confidence': 'high'
            }
        
        # Identify what needs improvement
        improvements_needed = []
        requirements = advancement_check['requirements']
        
        if not advancement_check['meets_accuracy']:
            current_acc = performance_data['accuracy'] * 100
            required_acc = requirements['accuracy'] * 100
            improvements_needed.append(f"Accuracy: {current_acc:.1f}% (need {required_acc:.1f}%)")
        
        if not advancement_check['meets_lessons']:
            improvements_needed.append(
                f"Lessons: {performance_data['lessons_completed']}/{requirements['lessons_completed']}"
            )
        
        if not advancement_check['meets_content']:
            improvements_needed.append(
                f"Content mastery: {performance_data['content_mastered']}/{requirements['content_mastered']}"
            )
        
        return {
            'action': 'continue_practice',
            'message': f"Keep practicing at {current_cefr} level. Focus on: {', '.join(improvements_needed)}",
            'improvements_needed': improvements_needed,
            'confidence': 'medium'
        }
    
    @classmethod
    def _get_next_cefr_level(cls, current_cefr: str) -> str:
        """
        Get the next CEFR level in progression
        """
        try:
            current_index = cls.CEFR_ORDER.index(current_cefr)
            if current_index < len(cls.CEFR_ORDER) - 1:
                return cls.CEFR_ORDER[current_index + 1]
            return current_cefr  # Already at highest level
        except ValueError:
            return 'A2'  # Default fallback
    
    @classmethod
    def advance_user_cefr_level(cls, user) -> Dict:
        """
        Advance user to next CEFR level if they meet requirements
        """
        try:
            with transaction.atomic():
                evaluation = cls.evaluate_user_progression(user)
                
                if not evaluation.get('ready_for_advancement', False):
                    return {
                        'success': False,
                        'message': 'User does not meet advancement requirements',
                        'evaluation': evaluation
                    }
                
                profile = UserLearningProfile.objects.get(user=user)
                old_level = profile.current_cefr_level
                new_level = cls._get_next_cefr_level(old_level)
                
                if old_level == new_level:
                    return {
                        'success': False,
                        'message': 'User is already at the highest CEFR level',
                        'current_level': old_level
                    }
                
                # Update user's CEFR level
                profile.current_cefr_level = new_level
                profile.save()
                
                # Award XP for level advancement
                from gamification.services import GamificationService
                xp_bonus = 100 * (cls.CEFR_ORDER.index(new_level) + 1)  # More XP for higher levels
                
                GamificationService.award_xp(
                    user=user,
                    amount=xp_bonus,
                    source='cefr_advancement',
                    description=f'Advanced from {old_level} to {new_level}'
                )
                
                logger.info(f"User {user.id} advanced from {old_level} to {new_level}")
                
                return {
                    'success': True,
                    'old_level': old_level,
                    'new_level': new_level,
                    'xp_bonus': xp_bonus,
                    'message': f'Congratulations! You have advanced to {new_level} level!'
                }
                
        except Exception as e:
            logger.error(f"Error advancing user {user.id} CEFR level: {e}")
            return {'success': False, 'error': str(e)}
    
    @classmethod
    def get_cefr_level_info(cls, cefr_level: str) -> Dict:
        """
        Get information about a specific CEFR level
        """
        level_descriptions = {
            'A1': {
                'name': 'Breakthrough',
                'description': 'Can understand and use familiar everyday expressions and basic phrases.',
                'skills': ['Basic greetings', 'Simple introductions', 'Basic questions and answers']
            },
            'A2': {
                'name': 'Waystage',
                'description': 'Can communicate in simple tasks requiring direct exchange of information.',
                'skills': ['Shopping', 'Local geography', 'Employment', 'Family information']
            },
            'B1': {
                'name': 'Threshold',
                'description': 'Can deal with most situations while traveling and express opinions.',
                'skills': ['Travel situations', 'Personal experiences', 'Dreams and ambitions']
            },
            'B2': {
                'name': 'Vantage',
                'description': 'Can interact fluently with native speakers and understand complex texts.',
                'skills': ['Complex discussions', 'Abstract topics', 'Professional communication']
            },
            'C1': {
                'name': 'Proficiency',
                'description': 'Can use language flexibly for social, academic and professional purposes.',
                'skills': ['Academic writing', 'Complex reasoning', 'Nuanced expression']
            },
            'C2': {
                'name': 'Mastery',
                'description': 'Can understand virtually everything and express themselves spontaneously.',
                'skills': ['Native-like fluency', 'Subtle distinctions', 'Complex literature']
            }
        }
        
        return level_descriptions.get(cefr_level, {
            'name': 'Unknown',
            'description': 'Level information not available',
            'skills': []
        })
