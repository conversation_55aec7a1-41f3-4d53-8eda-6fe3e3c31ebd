{"version": 3, "file": "welcome-modal-integration.js", "sourceRoot": "", "sources": ["../../../../src/typescript/welcome-modal-integration.ts"], "names": [], "mappings": "AAAA;;;GAGG;AA8BH,MAAM,uBAAuB;IAM3B,YAAY,SAA6B,EAAE;QAHnC,gBAAW,GAAW,CAAC,CAAC;QACxB,UAAK,GAAuB,IAAI,CAAC;QAGvC,IAAI,CAAC,MAAM,GAAG;YACZ,WAAW,EAAE,IAAI;YACjB,QAAQ,EAAE,IAAI;YACd,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,EAAE;YACT,GAAG,MAAM;SACV,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACtC,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,IAAI;QAChB,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACrD,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,mCAAmC;QACnC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAEtD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,wCAAwC;YACxC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9C,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;QAE9B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,iBAAgC,CAAC;QACtD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAEO,iBAAiB;QACvB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4CN,CAAC;IACJ,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QAEvD,QAAQ,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC5D,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAC7D,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1D,OAAO,EAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE1D,0BAA0B;QAC1B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,aAAa;QACb,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpE,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YACnE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;YAC3C,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC;YAExC,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,YAAY,EAAE,KAAK,KAAK,EAAE,CAAC;gBAC/D,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QAExB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,oBAAoB;QACpB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACpD,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,oBAAoB;QACpB,qBAAqB,CAAC,GAAG,EAAE;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,gBAAgB,CAAgB,CAAC;YAC3E,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC;gBACrC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QAExB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,gBAAgB,CAAgB,CAAC;QAC1E,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,aAAa,CAAC;YACxC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAC9B,CAAC;QAED,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAEO,iBAAiB;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;QAC1D,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEhD,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,YAAY,CAAsB,CAAC;QAC7E,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,YAAY,CAAsB,CAAC;QAE7E,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,SAAS,GAAG;;;;;6DAK4B,eAAe,CAAC,KAAK;qDAC7B,eAAe,CAAC,OAAO;;;;;YAKhE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;+CACW,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa;WAC1I,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;OAEd,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YACzD,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,eAAe;QACrB,OAAO;YACL;gBACE,EAAE,EAAE,SAAS;gBACb,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,wHAAwH;gBACjI,SAAS,EAAE,IAAI;aAChB;YACD;gBACE,EAAE,EAAE,aAAa;gBACjB,KAAK,EAAE,qBAAqB;gBAC5B,OAAO,EAAE,4HAA4H;gBACrI,SAAS,EAAE,IAAI;aAChB;YACD;gBACE,EAAE,EAAE,cAAc;gBAClB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,mGAAmG;gBAC5G,SAAS,EAAE,IAAI;aAChB;YACD;gBACE,EAAE,EAAE,OAAO;gBACX,KAAK,EAAE,iBAAiB;gBACxB,OAAO,EAAE,oFAAoF;gBAC7F,SAAS,EAAE,KAAK;aACjB;SACF,CAAC;IACJ,CAAC;IAEO,QAAQ;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;QAE1D,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,gCAAgC;YAChC,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChD,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC3B,eAAe,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,QAAQ;QACd,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;QAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,MAAM,YAAY,GAAqB;YACrC,cAAc,EAAE,KAAK;YACrB,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,EAAE;YAClB,WAAW,EAAE;gBACX,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,KAAK;aACjB;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;YAC/D,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,EAAE,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,aAAa;QACnB,IAAI,CAAC;YACH,YAAY,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAClF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,aAAa;IACN,IAAI;QACT,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,SAAS,GAAG;YACf,cAAc,EAAE,KAAK;YACrB,SAAS,EAAE,IAAI;YACf,cAAc,EAAE,EAAE;YAClB,WAAW,EAAE;gBACX,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,KAAK;aACjB;SACF,CAAC;QACF,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAEM,YAAY;QACjB,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;CACF;AAUD,kDAAkD;AAClD,MAAM,CAAC,uBAAuB,GAAG,uBAAuB,CAAC;AAEzD,mCAAmC;AACnC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;IACjD,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAE7D,MAAM,CAAC,YAAY,GAAG,IAAI,uBAAuB,CAAC;QAChD,WAAW,EAAE,IAAI;QACjB,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,KAAK,EAAE,wCAAwC;QAC3D,UAAU,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;QAC1D,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;KACrD,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,eAAe,uBAAuB,CAAC"}