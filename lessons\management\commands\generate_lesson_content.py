"""
Generate comprehensive lesson content from existing lessons and vocabulary
This command creates ContentItem objects that power the flashcard system
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from lessons.models import Lesson, Vocabulary, ContentItem
from ai_services.llm_flashcards import LLMFlashcardService, FlashcardRequest, DifficultyLevel, ExerciseType
import random


class Command(BaseCommand):
    help = "Generate comprehensive lesson content from existing lessons"

    def add_arguments(self, parser):
        parser.add_argument(
            '--lesson-id',
            type=int,
            help='Generate content for specific lesson ID'
        )
        parser.add_argument(
            '--language',
            type=str,
            default='spanish',
            help='Target language (default: spanish)'
        )
        parser.add_argument(
            '--use-ai',
            action='store_true',
            help='Use AI to generate additional content variations'
        )

    def handle(self, *args, **options):
        self.stdout.write("🚀 Generating lesson content...")
        
        # Initialize AI service if requested
        ai_service = None
        if options['use_ai']:
            try:
                ai_service = LLMFlashcardService()
                self.stdout.write("✓ AI service initialized")
            except Exception as e:
                self.stdout.write(f"⚠️ AI service failed to initialize: {e}")
                self.stdout.write("Continuing with manual content generation...")

        # Get lessons to process
        if options['lesson_id']:
            lessons = Lesson.objects.filter(id=options['lesson_id'])
        else:
            lessons = Lesson.objects.filter(is_active=True).order_by('order')

        if not lessons.exists():
            self.stdout.write("❌ No lessons found to process")
            return

        total_content_created = 0
        
        for lesson in lessons:
            self.stdout.write(f"\n📚 Processing lesson: {lesson.title}")
            
            # Get vocabulary for this lesson
            vocabularies = lesson.vocabularies.all()
            if not vocabularies.exists():
                self.stdout.write(f"  ⚠️ No vocabulary found for {lesson.title}")
                continue

            content_created = self.generate_content_for_lesson(
                lesson, vocabularies, options['language'], ai_service
            )
            total_content_created += content_created
            
            self.stdout.write(f"  ✓ Created {content_created} content items")

        self.stdout.write(
            self.style.SUCCESS(f"\n🎉 Successfully created {total_content_created} content items!")
        )

    def generate_content_for_lesson(self, lesson, vocabularies, language, ai_service):
        """Generate various types of content for a lesson"""
        content_created = 0
        
        # Determine difficulty based on lesson order
        difficulty = min(5, max(1, (lesson.order + 1) // 2))
        
        for vocab in vocabularies:
            # 1. Basic flashcard (Spanish to English)
            content_created += self.create_flashcard_content(
                vocab, difficulty, language, "es_to_en"
            )
            
            # 2. Reverse flashcard (English to Spanish)
            content_created += self.create_flashcard_content(
                vocab, difficulty, language, "en_to_es"
            )
            
            # 3. Multiple choice question
            content_created += self.create_mcq_content(
                vocab, vocabularies, difficulty, language
            )
            
            # 4. Translation exercise
            content_created += self.create_translation_content(
                vocab, difficulty, language
            )
            
            # 5. AI-generated variations (if available)
            if ai_service:
                content_created += self.create_ai_variations(
                    vocab, difficulty, language, ai_service
                )
        
        return content_created

    def create_flashcard_content(self, vocab, difficulty, language, direction):
        """Create flashcard content item"""
        if direction == "es_to_en":
            question = f"What does '{vocab.word}' mean in English?"
            answer = vocab.translation
            hint = f"Think about: {vocab.example_sentence}"
        else:  # en_to_es
            question = f"How do you say '{vocab.translation}' in Spanish?"
            answer = vocab.word
            hint = f"Example: {vocab.example_sentence}"
        
        content_item, created = ContentItem.objects.get_or_create(
            type="flashcard",
            question_text=question,
            answer_text=answer,
            defaults={
                "difficulty": difficulty,
                "language": language,
                "hint_text": hint,
                "explanation_text": f"Example: {vocab.example_sentence}",
                "tags": ["vocabulary", "flashcard", direction],
                "progressive_hints": [
                    "Think about the context...",
                    hint,
                    f"The answer starts with '{answer[0]}...'"
                ]
            }
        )
        return 1 if created else 0

    def create_mcq_content(self, vocab, all_vocabularies, difficulty, language):
        """Create multiple choice question"""
        # Get 3 random wrong answers from other vocabulary
        other_vocabs = list(all_vocabularies.exclude(id=vocab.id))
        if len(other_vocabs) < 3:
            return 0  # Not enough options
        
        wrong_answers = random.sample(other_vocabs, 3)
        choices = [vocab.translation] + [v.translation for v in wrong_answers]
        random.shuffle(choices)
        
        question = f"What does '{vocab.word}' mean?"
        
        content_item, created = ContentItem.objects.get_or_create(
            type="mcq",
            question_text=question,
            answer_text=vocab.translation,
            defaults={
                "difficulty": difficulty,
                "language": language,
                "choices_json": choices,
                "hint_text": f"Context: {vocab.example_sentence}",
                "explanation_text": f"'{vocab.word}' means '{vocab.translation}'. Example: {vocab.example_sentence}",
                "tags": ["vocabulary", "multiple_choice"],
                "progressive_hints": [
                    "Read the options carefully...",
                    f"Think about: {vocab.example_sentence}",
                    f"The answer is related to the context of the example sentence"
                ]
            }
        )
        return 1 if created else 0

    def create_translation_content(self, vocab, difficulty, language):
        """Create translation exercise"""
        # Create a simple sentence using the vocabulary
        question = f"Translate this sentence: '{vocab.example_sentence}'"
        
        # For now, we'll create a simplified version
        # In a real implementation, you'd have proper translations
        answer = f"Translation of: {vocab.example_sentence}"
        
        content_item, created = ContentItem.objects.get_or_create(
            type="translation",
            question_text=question,
            answer_text=answer,
            defaults={
                "difficulty": difficulty,
                "language": language,
                "hint_text": f"Key word: '{vocab.word}' means '{vocab.translation}'",
                "explanation_text": f"This sentence uses '{vocab.word}' which means '{vocab.translation}'",
                "tags": ["translation", "sentence", "vocabulary"],
                "progressive_hints": [
                    "Break down the sentence word by word...",
                    f"'{vocab.word}' means '{vocab.translation}'",
                    "Focus on the sentence structure and context"
                ]
            }
        )
        return 1 if created else 0

    def create_ai_variations(self, vocab, difficulty, language, ai_service):
        """Create AI-generated content variations"""
        try:
            # Create a flashcard request
            request = FlashcardRequest(
                language="english",
                target_language=language,
                difficulty=DifficultyLevel.BEGINNER if difficulty <= 2 else DifficultyLevel.INTERMEDIATE,
                exercise_type=ExerciseType.MULTIPLE_CHOICE,
                vocabulary=[vocab.word],
                context=f"Teaching the word '{vocab.word}' which means '{vocab.translation}'"
            )
            
            # Generate AI content
            response = ai_service.generate_flashcard(request)
            
            if response and response.question and response.correct_answer:
                content_item, created = ContentItem.objects.get_or_create(
                    type="mcq",
                    question_text=response.question,
                    answer_text=response.correct_answer,
                    defaults={
                        "difficulty": difficulty,
                        "language": language,
                        "choices_json": response.options or [],
                        "hint_text": response.hint,
                        "explanation_text": response.explanation,
                        "tags": ["ai_generated", "vocabulary", vocab.word.lower()],
                        "progressive_hints": [
                            "Think carefully about the context...",
                            response.hint or "Consider the meaning...",
                            response.explanation or "Check the example usage"
                        ]
                    }
                )
                return 1 if created else 0
        except Exception as e:
            self.stdout.write(f"  ⚠️ AI generation failed for {vocab.word}: {e}")
        
        return 0
