/**
 * Adaptive Learning System - TypeScript Edition
 * Intelligent content adaptation based on user performance and preferences
 */
interface LearningProfile {
    userId: string;
    level: 'beginner' | 'intermediate' | 'advanced';
    preferredPace: 'slow' | 'normal' | 'fast';
    weakAreas: string[];
    strongAreas: string[];
    learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'mixed';
    dailyGoalMinutes: number;
    completedLessons: string[];
    currentStreak: number;
    lastActivity: string;
}
interface PerformanceMetrics {
    accuracy: number;
    speed: number;
    retention: number;
    engagement: number;
    consistency: number;
}
interface ContentRecommendation {
    lessonId: string;
    title: string;
    difficulty: 'easy' | 'medium' | 'hard';
    estimatedDuration: number;
    topics: string[];
    reason: string;
    priority: number;
}
declare class AdaptiveLearningSystem {
    private profile;
    private metrics;
    private rules;
    private sessionData;
    constructor(userId: string);
    private init;
    private startSession;
    private setupPerformanceTracking;
    private setupTimeTracking;
    trackExerciseCompletion(data: {
        exerciseId: string;
        type: string;
        correct: boolean;
        timeSpent: number;
        attempts: number;
    }): void;
    trackPhaseCompletion(data: {
        phase: string;
        score: number;
        timeSpent: number;
        completionRate: number;
    }): void;
    private trackTimeSpent;
    private updateMetrics;
    private analyzePhasePerformance;
    private checkAdaptation;
    private getRecentInteractions;
    private calculateRecentAccuracy;
    private suggestDifficultyIncrease;
    private suggestDifficultyDecrease;
    private adjustPacing;
    private calculateAverageTime;
    generateContentRecommendations(): ContentRecommendation[];
    private getLevelBasedRecommendations;
    getPersonalizedSettings(): {
        exerciseDifficulty: string;
        contentPacing: string;
        reviewFrequency: number;
        recommendedStudyTime: number;
    };
    private calculateOptimalStudyTime;
    private dispatchAdaptationEvent;
    private loadProfile;
    private saveProfile;
    private loadMetrics;
    private saveMetrics;
    private getDefaultRules;
    getProfile(): LearningProfile;
    getMetrics(): PerformanceMetrics;
    updateProfile(updates: Partial<LearningProfile>): void;
    resetProgress(): void;
}
declare global {
    interface Window {
        AdaptiveLearningSystem: typeof AdaptiveLearningSystem;
        adaptiveLearning?: AdaptiveLearningSystem;
    }
}
export default AdaptiveLearningSystem;
//# sourceMappingURL=adaptive-learning.d.ts.map