{% extends 'base.html' %}

{% block content %}
<div class="p-8">
  <h1 class="text-2xl font-bold mb-4">Dashboard Debug</h1>
  
  <div class="mb-6">
    <h2 class="text-xl font-semibold mb-2">User Info:</h2>
    <p>User: {{ user.username }}</p>
    <p>Profile: {{ profile.display_name|default:"No display name" }}</p>
    <p>Target Language: {{ profile.target_language }}</p>
  </div>
  
  <div class="mb-6">
    <h2 class="text-xl font-semibold mb-2">Lessons Debug:</h2>
    <p>Lessons count: {{ lessons|length }}</p>
    <p>Completed lessons: {{ completed_lessons }}</p>
    <p>Completed lesson IDs: {{ completed_lesson_ids|length }}</p>
    
    {% if lessons %}
      <h3 class="font-semibold mt-4 mb-2">Available Lessons:</h3>
      <ul class="list-disc ml-6">
        {% for lesson in lessons %}
          <li>
            <strong>{{ lesson.title }}</strong> (ID: {{ lesson.id }}, Order: {{ lesson.order }})
            <br>Description: {{ lesson.description|default:"No description" }}
            <br>Vocabularies: {{ lesson.vocabularies.count }}
            <br>Active: {{ lesson.is_active }}
            {% if lesson.id in completed_lesson_ids %}
              <span class="text-green-600 font-bold">[COMPLETED]</span>
            {% else %}
              <span class="text-blue-600">[NOT COMPLETED]</span>
            {% endif %}
          </li>
        {% endfor %}
      </ul>
    {% else %}
      <p class="text-red-600 font-bold">NO LESSONS FOUND!</p>
    {% endif %}
  </div>
  
  <div class="mb-6">
    <h2 class="text-xl font-semibold mb-2">Gamification Data:</h2>
    <p>Level: {{ level.level }}</p>
    <p>XP: {{ level.xp }}</p>
    <p>Streak: {{ streak.current_streak }}</p>
  </div>
  
  <div>
    <a href="{% url 'dashboard' %}" class="bg-blue-500 text-white px-4 py-2 rounded">Back to Real Dashboard</a>
  </div>
</div>
{% endblock %}
