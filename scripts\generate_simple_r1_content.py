#!/usr/bin/env python
"""
Generate Simple R1 Content
Creates a comprehensive set of flashcards using the simple R1 generator
"""

import os
import sys
import django
import time

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'talontalk.settings')
django.setup()

from lessons.simple_r1_generator import SimpleR1Generator
from lessons.models import ContentItem

def main():
    print("Simple R1 Content Generation")
    print("=" * 40)
    print("Generating high-quality flashcards using DeepSeek R1")
    print()
    
    # Initialize generator
    generator = SimpleR1Generator()
    
    if not generator.client:
        print("ERROR: DeepSeek R1 not available")
        return
    
    print("DeepSeek R1 is available!")
    
    # Languages and content plan
    languages = {
        "spanish": 50,
        "french": 40, 
        "german": 30,
        "italian": 20,
        "portuguese": 20
    }
    
    total_requested = sum(languages.values())
    total_generated = 0
    language_results = {}
    
    print(f"Generating {total_requested} flashcards across {len(languages)} languages...")
    print()
    
    for language, count in languages.items():
        print(f"Generating {count} flashcards for {language.title()}...")
        
        start_time = time.time()
        
        try:
            result = generator.generate_batch(language, count)
            
            if result["success"]:
                generated = result["generated_count"]
                success_rate = result["success_rate"]
                
                total_generated += generated
                language_results[language] = {
                    "requested": count,
                    "generated": generated,
                    "success_rate": success_rate
                }
                
                elapsed = time.time() - start_time
                print(f"  SUCCESS: {generated}/{count} flashcards ({success_rate:.1f}%) in {elapsed:.1f}s")
                
                # Show sample
                if result["flashcards"]:
                    sample = result["flashcards"][0]
                    print(f"  Sample: {sample.question_text[:60]}...")
            else:
                print(f"  FAILED: Could not generate {language} content")
                language_results[language] = {
                    "requested": count,
                    "generated": 0,
                    "success_rate": 0
                }
        
        except Exception as e:
            print(f"  ERROR: {e}")
            language_results[language] = {
                "requested": count,
                "generated": 0,
                "success_rate": 0
            }
        
        # Small delay between languages
        time.sleep(1)
        print()
    
    # Final summary
    print("Content Generation Complete!")
    print("=" * 40)
    print(f"Overall Results:")
    print(f"  Total Requested: {total_requested}")
    print(f"  Total Generated: {total_generated}")
    print(f"  Overall Success Rate: {(total_generated/total_requested*100):.1f}%")
    print()
    
    print("Language Breakdown:")
    for language, results in language_results.items():
        print(f"  {language.title()}: {results['generated']}/{results['requested']} ({results['success_rate']:.1f}%)")
    
    # Database verification
    db_count = ContentItem.objects.count()
    r1_count = ContentItem.objects.filter(tags__contains=['r1_generated']).count()
    
    print(f"\nDatabase Status:")
    print(f"  Total Content Items: {db_count}")
    print(f"  R1 Generated Items: {r1_count}")
    
    # Quality sample
    if db_count > 0:
        print(f"\nQuality Samples:")
        samples = ContentItem.objects.filter(tags__contains=['r1_generated'])[:3]
        
        for i, sample in enumerate(samples, 1):
            print(f"  {i}. {sample.language.title()}: {sample.question_text[:50]}...")
            print(f"     Answer: {sample.answer_text}")
            print(f"     Type: {', '.join(sample.tags)}")
            print()
    
    if total_generated > 0:
        print("SUCCESS: R1 content generation completed!")
        print("Your language learning platform now has high-quality flashcards!")
    else:
        print("WARNING: No content was generated. Check your setup.")

if __name__ == "__main__":
    main()
