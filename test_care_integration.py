#!/usr/bin/env python
"""
Test script to verify C.A.R.E. Framework integration
"""
import requests
import json


def test_care_integration():
    """Test C.A.R.E. framework endpoints and integration"""
    base_url = "http://127.0.0.1:8000"

    print("🎓 Testing C.A.R.E. Framework Integration")
    print("=" * 50)

    # Test 1: Lesson List
    print("\n📚 Test 1: C.A.R.E. Lesson List")
    try:
        response = requests.get(f"{base_url}/care/api/lessons/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Lessons available: {len(data.get('lessons', []))}")
            for lesson in data.get("lessons", [])[:2]:  # Show first 2
                print(f"   - {lesson.get('title')}")
            print("   ✅ Lesson list working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 2: Contextualize Phase
    print("\n🌍 Test 2: Contextualize Phase")
    try:
        response = requests.get(
            f"{base_url}/care/api/phase/contextualize/?lesson=restaurant_ordering"
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Stage: {data.get('stage')}")
            context = data.get("context", {})
            if "title" in context:
                print(f"   Context: {context['title'][:50]}...")
            print("   ✅ Contextualize phase working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 3: Acquire Phase
    print("\n🧠 Test 3: Acquire Phase")
    try:
        response = requests.get(
            f"{base_url}/care/api/phase/acquire/?lesson=restaurant_ordering"
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Stage: {data.get('stage')}")
            content_items = data.get("content_items", [])
            if content_items:
                item = content_items[0]
                print(f"   Question: {item.get('question', 'N/A')[:50]}...")
                print(f"   Options: {len(item.get('options', []))}")
            print("   ✅ Acquire phase working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 4: Reinforce Phase
    print("\n💪 Test 4: Reinforce Phase")
    try:
        response = requests.get(
            f"{base_url}/care/api/phase/reinforce/?lesson=restaurant_ordering"
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Stage: {data.get('stage')}")
            exercises = data.get("practice_exercises", [])
            if exercises:
                exercise = exercises[0]
                print(f"   Exercise: {exercise.get('question', 'N/A')[:50]}...")
            print("   ✅ Reinforce phase working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 5: Extend Phase
    print("\n🚀 Test 5: Extend Phase")
    try:
        response = requests.get(
            f"{base_url}/care/api/phase/extend/?lesson=restaurant_ordering"
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Stage: {data.get('stage')}")
            creative = data.get("creative_applications", [])
            if creative:
                task = creative[0]
                print(f"   Creative Task: {task.get('question', 'N/A')[:50]}...")
            print("   ✅ Extend phase working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 6: Answer Submission
    print("\n📝 Test 6: Answer Submission")
    try:
        answer_data = {
            "answer": "restaurant",
            "question_id": "rest_001",
            "lesson": "restaurant_ordering",
        }
        response = requests.post(
            f"{base_url}/care/api/submit/acquire/",
            json=answer_data,
            headers={"Content-Type": "application/json"},
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Correct: {data.get('correct')}")
            print(f"   Feedback: {data.get('feedback', 'N/A')[:50]}...")
            print("   ✅ Answer submission working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    # Test 7: AI Tutor
    print("\n🤖 Test 7: AI Tutor Chat")
    try:
        response = requests.get(
            f"{base_url}/care/api/tutor/",
            params={
                "message": "How do I order food in Spanish?",
                "lesson": "restaurant_ordering",
                "phase": "contextualize",
            },
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            print(f"   Response: {data.get('response', 'N/A')[:50]}...")
            print("   ✅ AI Tutor working")
        else:
            print(f"   ❌ Error: {response.text[:100]}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")

    print("\n" + "=" * 50)
    print("🎯 C.A.R.E. Framework Integration Test Complete!")
    print("✨ All endpoints are now ready for the enhanced learning experience")


if __name__ == "__main__":
    test_care_integration()
