# TalonTalk: App Overview & Phased Rollout

## Core Concept & Vision
TalonTalk is a language learning platform designed to make mastering a new language an engaging, habit-forming, and deeply rewarding experience. Guided by our peregrine falcon mascot—a symbol of speed, precision, and global travel—TalonTalk combines proven gamification mechanics with a powerful, tiered learning path that takes users from their first word to fluent conversation.

---

## Technology Decisions
- **Automatic Speech Recognition (ASR):** TalonTalk uses [fastwhisper](https://github.com/SYSTRAN/fast-whisper) as the default ASR engine for all speech-to-text features.
- **Large Language Model (LLM):** The open-source LLM to be used for AI features will be selected and agreed upon at the relevant development phase.

---

## The Three-Tiered Learning Journey
- **Tier 1: Foundational Learning (Free):** Highly gamified, habit-building, interactive flashcards/quizzes.
- **Tier 2: AI Practice Agent (Subscription):** AI-powered, adaptive speaking/listening practice.
- **Tier 3: Human Tutors (Pay-per-lesson):** Real-world, one-on-one sessions with vetted tutors.

---

## User Journey: From Landing to Learning
1. **Landing Page:** Mobile-first, emotional hook, clear CTAs, social proof, and tiered path explanation.
2. **First Login & Onboarding:** Welcome modal, language selection, goal setting, guided tour, first rewarding lesson with instant feedback.

---

## Core Features & Emotional Design
- XP, levels, streaks, badges, achievements
- Animated feedback (confetti, sounds, microinteractions)
- Personalized dashboards
- Emotional nudges (in-app messages, notifications)

---

## Phased Development Plan
**For each phase, reference the appropriate workflow template in `django_workflow_templates/` and document app-specific notes in `app_workflow_notes/`. After each phase, perform a backup to your GitHub repository: [https://github.com/finessed/talontalk.git](https://github.com/finessed/talontalk.git).**

### Phase 1: Project Setup & Infrastructure
- Follow `DJANGO_WORKFLOW.md` (section: Project Setup & Infrastructure)
- Initialize Django project, database, Git, and settings
- **Backup:** Commit and push to GitHub after setup

### Phase 2: Core Django Apps Creation
- Reference `DJANGO_WORKFLOW.md` (section: Django Apps Creation)
- Create apps: users, profiles, lessons, gamification (each with local Tailwind config)
- **Backup:** Commit and push to GitHub after app creation

### Phase 3: Database Models Design
- Reference `DJANGO_WORKFLOW.md` (section: Database Models Design)
- Implement models for users, lessons, vocabulary, gamification
- **Backup:** Commit and push to GitHub after models/migrations

### Phase 4: API Development
- Reference `DJANGO_WORKFLOW.md` (section: API Development)
- Build REST API endpoints (Django REST Framework)
- **Backup:** Commit and push to GitHub after API implementation

### Phase 5: Frontend Templates
- Reference `DJANGO_WORKFLOW.md` (section: Frontend Templates)
- Develop mobile-first UI (landing, dashboard, lessons) with Django templates, Tailwind CSS, DaisyUI, and vanilla TypeScript/JavaScript
- **Backup:** Commit and push to GitHub after UI implementation

### Phase 6: Integration & Testing
- Reference `DJANGO_WORKFLOW.md` (section: Integration & Testing)
- Write tests, integrate features, QA
- **Backup:** Commit and push to GitHub after successful integration/testing

### Phase 7: Deployment Preparation
- Reference `DJANGO_WORKFLOW.md` (section: Deployment Preparation)
- Prepare for production (Gunicorn, static/media, docs)
- **Backup:** Commit and push to GitHub before and after deployment

### Ongoing: Backups, Migrations, Version Control
- Always follow best practices for backups, migrations, and version control as outlined in `django_workflow_templates/`
- **Backup:** Commit and push to GitHub after every significant change

---

## Tech Stack Summary
- **Backend:** Django (Python)
- **Frontend:** Django Templates + Tailwind CSS + HTML + DaisyUI
- **Interactivity:** Vanilla TypeScript/JavaScript (progressive enhancement only)

---

**Remember:**  
- Reference the workflow templates after each phase.  
- Document app-specific notes in `app_workflow_notes/`.  
- Perform a GitHub backup after every phase and major change.
