"""
Management command to purge poor quality content and regenerate with quality checks
"""

import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone

from lessons.models import ContentItem, UserLearningProfile
from lessons.advanced_quality_engine import AdvancedQualityEngine, QualityLevel
from lessons.services.content_generator import ContentGenerator
from ai_services.llm_config import get_recommended_config

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Purge poor quality content and regenerate with quality validation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--language',
            type=str,
            help='Only process specific language (e.g., spanish, french)',
        )
        parser.add_argument(
            '--min-quality-score',
            type=int,
            default=60,
            help='Minimum quality score to keep content (default: 60)',
        )
        parser.add_argument(
            '--regenerate-count',
            type=int,
            default=50,
            help='Number of new content items to generate per language (default: 50)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting content quality purge and regeneration...')
        )
        
        dry_run = options['dry_run']
        target_language = options['language']
        min_quality_score = options['min_quality_score']
        regenerate_count = options['regenerate_count']
        
        # Initialize quality engine
        quality_engine = AdvancedQualityEngine()
        
        # Get content to analyze
        content_query = ContentItem.objects.all()
        if target_language:
            content_query = content_query.filter(language=target_language)
        
        total_content = content_query.count()
        self.stdout.write(f'Analyzing {total_content} content items...')
        
        # Analyze content quality
        poor_content = []
        good_content = []
        
        for content_item in content_query:
            content_dict = {
                "id": str(content_item.id),
                "question": content_item.question,
                "content_type": content_item.content_type,
                "language": content_item.language,
            }
            
            # Add optional fields
            if hasattr(content_item, 'explanation') and content_item.explanation:
                content_dict["explanation"] = content_item.explanation
            if hasattr(content_item, 'options') and content_item.options:
                content_dict["options"] = content_item.options
            if hasattr(content_item, 'correct_answer') and content_item.correct_answer:
                content_dict["correct_answer"] = content_item.correct_answer
            
            try:
                report = quality_engine.validate_content(content_dict, content_item.language)
                
                if report.overall_score < min_quality_score or report.quality_level == QualityLevel.REJECTED:
                    poor_content.append({
                        'item': content_item,
                        'score': report.overall_score,
                        'level': report.quality_level.value,
                        'issues': len(report.issues)
                    })
                else:
                    good_content.append(content_item)
                    
            except Exception as e:
                logger.error(f"Error analyzing content {content_item.id}: {e}")
                poor_content.append({
                    'item': content_item,
                    'score': 0,
                    'level': 'error',
                    'issues': 1
                })
        
        # Report findings
        self.stdout.write(
            self.style.WARNING(f'Found {len(poor_content)} poor quality items')
        )
        self.stdout.write(
            self.style.SUCCESS(f'Found {len(good_content)} good quality items')
        )
        
        # Show sample of poor content
        if poor_content:
            self.stdout.write('\nSample of poor quality content:')
            for item_data in poor_content[:5]:
                item = item_data['item']
                self.stdout.write(
                    f'  - ID {item.id}: "{item.question[:50]}..." '
                    f'(Score: {item_data["score"]:.1f}, Level: {item_data["level"]})'
                )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN: No content was actually deleted')
            )
            return
        
        # Confirm deletion
        if poor_content:
            confirm = input(f'\nDelete {len(poor_content)} poor quality items? (y/N): ')
            if confirm.lower() != 'y':
                self.stdout.write('Operation cancelled')
                return
        
        # Delete poor content
        deleted_count = 0
        if poor_content:
            with transaction.atomic():
                for item_data in poor_content:
                    item_data['item'].delete()
                    deleted_count += 1
            
            self.stdout.write(
                self.style.SUCCESS(f'Deleted {deleted_count} poor quality items')
            )
        
        # Regenerate content
        if regenerate_count > 0:
            self.stdout.write(f'\nRegenerating {regenerate_count} new content items...')
            
            # Get languages to regenerate for
            languages_to_process = [target_language] if target_language else ['spanish', 'french', 'german', 'italian', 'portuguese']
            
            content_generator = ContentGenerator(get_recommended_config())
            generated_count = 0
            
            for language in languages_to_process:
                if not self._is_language_active(language):
                    continue
                
                self.stdout.write(f'Generating content for {language}...')
                
                # Generate different types of content
                content_types = ['flashcard', 'mcq', 'translation']
                items_per_language = regenerate_count // len(languages_to_process)
                
                try:
                    result = content_generator.generate_care_content_batch(
                        language=language,
                        difficulty_level=2,  # Intermediate level
                        content_types=content_types,
                        batch_size=items_per_language
                    )
                    
                    if result.get('success'):
                        # Validate generated content
                        validated_items = []
                        for content_item in result.get('content_items', []):
                            content_dict = {
                                "question": content_item.question,
                                "content_type": content_item.content_type,
                                "language": content_item.language,
                            }
                            
                            report = quality_engine.validate_content(content_dict, language)
                            if report.overall_score >= min_quality_score:
                                validated_items.append(content_item)
                            else:
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'Rejected generated item with score {report.overall_score:.1f}'
                                    )
                                )
                        
                        generated_count += len(validated_items)
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'Generated {len(validated_items)} quality items for {language}'
                            )
                        )
                    
                except Exception as e:
                    logger.error(f"Error generating content for {language}: {e}")
                    self.stdout.write(
                        self.style.ERROR(f'Failed to generate content for {language}: {e}')
                    )
            
            self.stdout.write(
                self.style.SUCCESS(f'Total generated: {generated_count} new content items')
            )
        
        # Final summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('Content purge and regeneration complete!'))
        self.stdout.write(f'Deleted: {deleted_count} poor quality items')
        self.stdout.write(f'Generated: {generated_count} new quality items')
        self.stdout.write(f'Remaining good content: {len(good_content)} items')
    
    def _is_language_active(self, language):
        """Check if language is active for content generation"""
        from lessons.language_manager import LanguageManager
        return LanguageManager.is_language_active(language)
