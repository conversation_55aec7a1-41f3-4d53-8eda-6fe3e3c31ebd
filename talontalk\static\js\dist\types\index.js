/**
 * TalonTalk Application Type Definitions
 * Core types for the language learning platform
 */
// ==================== Error Types ====================
export class CAREError extends Error {
    constructor(message, code, phase, context) {
        super(message);
        this.name = 'CAREError';
        this.code = code;
        this.phase = phase;
        this.context = context;
    }
}
//# sourceMappingURL=index.js.map