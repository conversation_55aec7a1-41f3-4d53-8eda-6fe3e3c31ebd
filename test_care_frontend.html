<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C.A.R.E. Framework Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold mb-6">C.A.R.E. Framework API Test</h1>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <button onclick="testPhase('contextualize')"
                    class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    Test Contextualize
                </button>
                <button onclick="testPhase('acquire')"
                    class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Test Acquire
                </button>
                <button onclick="testPhase('reinforce')"
                    class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Test Reinforce
                </button>
                <button onclick="testPhase('extend')"
                    class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    Test Extend
                </button>
            </div>

            <div id="results" class="bg-gray-50 p-4 rounded min-h-96 overflow-auto">
                <p class="text-gray-600">Click a button above to test C.A.R.E. phase APIs</p>
            </div>
        </div>
    </div>

    <script>
        async function testPhase(phase) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="flex items-center justify-center py-8">
                    <div class="loading-spinner border-4 border-gray-300 border-t-blue-500 rounded-full w-8 h-8"></div>
                    <span class="ml-3">Loading ${phase} phase...</span>
                </div>
            `;

            try {
                const response = await fetch(`/care/api/phase/${phase}/`);
                const data = await response.json();

                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="space-y-4">
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                                ✅ ${phase.toUpperCase()} Phase API Working!
                            </div>
                            
                            <div class="bg-white p-4 rounded border">
                                <h3 class="font-bold text-lg mb-2">API Response:</h3>
                                <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto">${JSON.stringify(data.content, null, 2)}</pre>
                            </div>
                            
                            <div class="bg-blue-50 p-4 rounded border">
                                <h3 class="font-bold text-lg mb-2">Content Preview:</h3>
                                <div id="content-preview-${phase}">Rendering content...</div>
                            </div>
                        </div>
                    `;

                    // Render the content using the same logic as the main app
                    renderPhaseContent(phase, data.content);

                } else {
                    resultsDiv.innerHTML = `
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                            ❌ Error: ${data.error || 'Unknown error'}
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        ❌ Network Error: ${error.message}
                    </div>
                `;
            }
        }

        function renderPhaseContent(phase, content) {
            const previewDiv = document.getElementById(`content-preview-${phase}`);

            if (phase === 'contextualize') {
                const scenario = content?.scenario || {};
                const culturalContext = content?.cultural_context || {};
                const keyPhrases = content?.key_phrases || [];

                previewDiv.innerHTML = `
                    <div class="space-y-3">
                        <div class="bg-green-50 p-3 rounded">
                            <h4 class="font-bold">${scenario.title || 'Scenario'}</h4>
                            <p class="text-sm">${scenario.description || 'No description'}</p>
                        </div>
                        <div class="bg-blue-50 p-3 rounded">
                            <h4 class="font-bold">${culturalContext.title || 'Cultural Context'}</h4>
                            <ul class="text-sm">
                                ${culturalContext.facts ? culturalContext.facts.map(fact => `<li>• ${fact}</li>`).join('') : '<li>No facts available</li>'}
                            </ul>
                        </div>
                        <div class="bg-purple-50 p-3 rounded">
                            <h4 class="font-bold">Key Phrases (${keyPhrases.length})</h4>
                            ${keyPhrases.slice(0, 2).map(phrase => `
                                <div class="text-sm border-l-2 border-purple-300 pl-2 mt-1">
                                    <strong>${phrase.spanish}</strong> - ${phrase.english}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (phase === 'acquire') {
                const vocabulary = content?.vocabulary || [];
                const grammar = content?.grammar || {};

                previewDiv.innerHTML = `
                    <div class="space-y-3">
                        <div class="bg-blue-50 p-3 rounded">
                            <h4 class="font-bold">Vocabulary (${vocabulary.length} items)</h4>
                            ${vocabulary.slice(0, 3).map(item => `
                                <div class="text-sm border-l-2 border-blue-300 pl-2 mt-1">
                                    <strong>${item.word}</strong> - ${item.translation}
                                    <br><span class="text-gray-600">${item.pronunciation}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="bg-green-50 p-3 rounded">
                            <h4 class="font-bold">${grammar.topic || 'Grammar'}</h4>
                            <p class="text-sm">${grammar.structures ? grammar.structures.length + ' structures' : 'No structures'}</p>
                        </div>
                    </div>
                `;
            } else if (phase === 'reinforce') {
                const exercises = content?.exercises || [];

                previewDiv.innerHTML = `
                    <div class="space-y-3">
                        <div class="bg-purple-50 p-3 rounded">
                            <h4 class="font-bold">Exercises (${exercises.length} items)</h4>
                            ${exercises.slice(0, 2).map((exercise, index) => `
                                <div class="text-sm border-l-2 border-purple-300 pl-2 mt-1">
                                    <strong>${index + 1}. ${exercise.type}:</strong> ${exercise.question || exercise.phrase || 'Exercise content'}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else if (phase === 'extend') {
                const realWorldApps = content?.real_world_applications || [];
                const homework = content?.homework || {};

                previewDiv.innerHTML = `
                    <div class="space-y-3">
                        <div class="bg-orange-50 p-3 rounded">
                            <h4 class="font-bold">Real-World Applications (${realWorldApps.length})</h4>
                            ${realWorldApps.slice(0, 1).map(app => `
                                <div class="text-sm border-l-2 border-orange-300 pl-2 mt-1">
                                    <strong>${app.title}</strong><br>
                                    ${app.description}
                                </div>
                            `).join('')}
                        </div>
                        <div class="bg-yellow-50 p-3 rounded">
                            <h4 class="font-bold">${homework.title || 'Homework'}</h4>
                            <p class="text-sm">${homework.description || 'No homework assigned'}</p>
                        </div>
                    </div>
                `;
            }
        }
    </script>
</body>

</html>