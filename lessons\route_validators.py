"""
Route Validation System for TalonTalk
Comprehensive validation for C.A.R.E. system routes and API endpoints
"""

import json
import logging
from typing import Dict, Any, Optional, List
from functools import wraps

from django.http import JsonResponse, HttpRequest
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.core.exceptions import ValidationError
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

from .language_manager import LanguageManager
from .advanced_quality_engine import AdvancedQualityEngine, QualityLevel

logger = logging.getLogger(__name__)


class RouteValidationError(Exception):
    """Custom exception for route validation errors"""
    def __init__(self, message: str, error_code: str = "VALIDATION_ERROR", status_code: int = 400):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(self.message)


def validate_care_phase(phase: str) -> bool:
    """Validate C.A.R.E. phase parameter"""
    valid_phases = ["contextualize", "acquire", "reinforce", "extend"]
    return phase in valid_phases


def validate_content_type(content_type: str) -> bool:
    """Validate content type parameter"""
    valid_types = ["flashcard", "mcq", "translation", "grammar", "listening", "speaking"]
    return content_type in valid_types


def validate_language_code(language: str) -> bool:
    """Validate language code"""
    return LanguageManager.is_language_supported(language)


def validate_difficulty_level(difficulty: Any) -> bool:
    """Validate difficulty level"""
    try:
        level = int(difficulty)
        return 1 <= level <= 5
    except (ValueError, TypeError):
        return False


def validate_cefr_level(cefr: str) -> bool:
    """Validate CEFR level"""
    valid_levels = ["A1", "A2", "B1", "B2", "C1", "C2"]
    return cefr in valid_levels


def care_route_validator(required_params: List[str] = None, 
                        optional_params: List[str] = None,
                        validate_language: bool = True,
                        validate_quality: bool = False):
    """
    Decorator for validating C.A.R.E. system routes
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            try:
                # Parse request data
                if request.method == "POST":
                    try:
                        data = json.loads(request.body) if request.body else {}
                    except json.JSONDecodeError:
                        return JsonResponse({
                            "success": False,
                            "error": "Invalid JSON in request body",
                            "error_code": "INVALID_JSON"
                        }, status=400)
                else:
                    data = request.GET.dict()
                
                # Validate required parameters
                if required_params:
                    missing_params = []
                    for param in required_params:
                        if param not in data or not data[param]:
                            missing_params.append(param)
                    
                    if missing_params:
                        return JsonResponse({
                            "success": False,
                            "error": f"Missing required parameters: {', '.join(missing_params)}",
                            "error_code": "MISSING_PARAMETERS",
                            "missing_params": missing_params
                        }, status=400)
                
                # Validate specific parameter types
                validation_errors = []
                
                # Language validation
                if validate_language and "language" in data:
                    if not validate_language_code(data["language"]):
                        validation_errors.append("Invalid language code")
                    elif not LanguageManager.is_language_active(data["language"]):
                        validation_errors.append(f"Language '{data['language']}' is not currently supported")
                
                # C.A.R.E. phase validation
                if "care_phase" in data and not validate_care_phase(data["care_phase"]):
                    validation_errors.append("Invalid C.A.R.E. phase")
                
                # Content type validation
                if "content_type" in data and not validate_content_type(data["content_type"]):
                    validation_errors.append("Invalid content type")
                
                # Difficulty validation
                if "difficulty" in data and not validate_difficulty_level(data["difficulty"]):
                    validation_errors.append("Invalid difficulty level (must be 1-5)")
                
                # CEFR level validation
                if "cefr_level" in data and not validate_cefr_level(data["cefr_level"]):
                    validation_errors.append("Invalid CEFR level")
                
                if validation_errors:
                    return JsonResponse({
                        "success": False,
                        "error": "Validation failed",
                        "error_code": "VALIDATION_FAILED",
                        "validation_errors": validation_errors
                    }, status=400)
                
                # Quality validation for content
                if validate_quality and "content" in data:
                    quality_engine = AdvancedQualityEngine()
                    quality_report = quality_engine.validate_content(
                        data["content"], 
                        data.get("language", "spanish")
                    )
                    
                    if quality_report.quality_level == QualityLevel.REJECTED:
                        return JsonResponse({
                            "success": False,
                            "error": "Content quality too low",
                            "error_code": "QUALITY_REJECTED",
                            "quality_issues": [issue.to_dict() for issue in quality_report.issues]
                        }, status=422)
                
                # Add validated data to request
                request.validated_data = data
                
                return view_func(request, *args, **kwargs)
                
            except RouteValidationError as e:
                return JsonResponse({
                    "success": False,
                    "error": e.message,
                    "error_code": e.error_code
                }, status=e.status_code)
            
            except Exception as e:
                logger.error(f"Unexpected error in route validation: {e}")
                return JsonResponse({
                    "success": False,
                    "error": "Internal server error",
                    "error_code": "INTERNAL_ERROR"
                }, status=500)
        
        return wrapper
    return decorator


def authenticated_care_route(required_params: List[str] = None, **validator_kwargs):
    """
    Combined decorator for authentication and C.A.R.E. route validation
    """
    def decorator(view_func):
        @login_required
        @care_route_validator(required_params=required_params, **validator_kwargs)
        @wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


class RouteSecurityValidator:
    """
    Security validation for routes
    """
    
    @staticmethod
    def validate_user_permissions(user, action: str, resource: str = None) -> bool:
        """Validate user permissions for specific actions"""
        if not user.is_authenticated:
            return False
        
        # Basic permission checks
        permission_map = {
            "generate_content": True,  # All authenticated users can generate content
            "access_care": True,       # All authenticated users can access C.A.R.E.
            "view_quality_report": user.is_staff,  # Only staff can view quality reports
            "modify_language_settings": True,      # All users can modify their language
        }
        
        return permission_map.get(action, False)
    
    @staticmethod
    def validate_rate_limit(user, action: str) -> bool:
        """Basic rate limiting validation"""
        from django.core.cache import cache
        
        # Rate limits per action (requests per minute)
        rate_limits = {
            "generate_content": 30,
            "access_care": 100,
            "submit_answer": 60,
        }
        
        limit = rate_limits.get(action, 60)
        cache_key = f"rate_limit_{user.id}_{action}"
        
        current_count = cache.get(cache_key, 0)
        if current_count >= limit:
            return False
        
        cache.set(cache_key, current_count + 1, 60)  # 1 minute window
        return True


def secure_care_route(action: str, rate_limit: bool = True):
    """
    Security decorator for C.A.R.E. routes
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            user = request.user
            
            # Check permissions
            if not RouteSecurityValidator.validate_user_permissions(user, action):
                return JsonResponse({
                    "success": False,
                    "error": "Insufficient permissions",
                    "error_code": "PERMISSION_DENIED"
                }, status=403)
            
            # Check rate limits
            if rate_limit and not RouteSecurityValidator.validate_rate_limit(user, action):
                return JsonResponse({
                    "success": False,
                    "error": "Rate limit exceeded",
                    "error_code": "RATE_LIMIT_EXCEEDED"
                }, status=429)
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def comprehensive_care_validator(
    required_params: List[str] = None,
    action: str = "access_care",
    validate_quality: bool = False,
    rate_limit: bool = True
):
    """
    Comprehensive validator combining all validation layers
    """
    def decorator(view_func):
        @secure_care_route(action=action, rate_limit=rate_limit)
        @authenticated_care_route(
            required_params=required_params,
            validate_quality=validate_quality
        )
        @wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs):
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
